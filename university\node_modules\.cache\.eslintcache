[{"D:\\HAMMAD\\React\\New folder\\university\\src\\index.js": "1", "D:\\HAMMAD\\React\\New folder\\university\\src\\App.js": "2", "D:\\HAMMAD\\React\\New folder\\university\\src\\reportWebVitals.js": "3", "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\AuthContext.js": "4", "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\DataContext.js": "5", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Layout.js": "6", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\auth\\Login.js": "7", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\Dashboard.js": "8", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Sidebar.js": "9", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Header.js": "10", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\AdminDashboard.js": "11", "D:\\HAMMAD\\React\\New folder\\university\\src\\data\\mockData.js": "12", "D:\\HAMMAD\\React\\New folder\\university\\src\\utils\\helpers.js": "13", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ProtectedRoute.js": "14", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\StudentDashboard.js": "15", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\FacultyDashboard.js": "16", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\MyCourses.js": "17", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\Courses.js": "18", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ComingSoon.js": "19"}, {"size": 535, "mtime": 1748796002682, "results": "20", "hashOfConfig": "21"}, {"size": 7349, "mtime": 1748797918532, "results": "22", "hashOfConfig": "21"}, {"size": 362, "mtime": 1748796002904, "results": "23", "hashOfConfig": "21"}, {"size": 1417, "mtime": 1748796804039, "results": "24", "hashOfConfig": "21"}, {"size": 5045, "mtime": 1748796819184, "results": "25", "hashOfConfig": "21"}, {"size": 1217, "mtime": 1748798520223, "results": "26", "hashOfConfig": "21"}, {"size": 10893, "mtime": 1748798738332, "results": "27", "hashOfConfig": "21"}, {"size": 809, "mtime": 1748797263129, "results": "28", "hashOfConfig": "21"}, {"size": 6704, "mtime": 1748798582571, "results": "29", "hashOfConfig": "21"}, {"size": 4723, "mtime": 1748798634909, "results": "30", "hashOfConfig": "21"}, {"size": 9364, "mtime": 1748798771446, "results": "31", "hashOfConfig": "21"}, {"size": 9785, "mtime": 1748796856368, "results": "32", "hashOfConfig": "21"}, {"size": 5739, "mtime": 1748796877868, "results": "33", "hashOfConfig": "21"}, {"size": 971, "mtime": 1748797008708, "results": "34", "hashOfConfig": "21"}, {"size": 10231, "mtime": 1748797294921, "results": "35", "hashOfConfig": "21"}, {"size": 11038, "mtime": 1748797283771, "results": "36", "hashOfConfig": "21"}, {"size": 11069, "mtime": 1748797817019, "results": "37", "hashOfConfig": "21"}, {"size": 10882, "mtime": 1748797848970, "results": "38", "hashOfConfig": "21"}, {"size": 1179, "mtime": 1748797889454, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jjzcbq", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\HAMMAD\\React\\New folder\\university\\src\\index.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\App.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\reportWebVitals.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\AuthContext.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\DataContext.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Layout.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\auth\\Login.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\Dashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Sidebar.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Header.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\AdminDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\data\\mockData.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\utils\\helpers.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ProtectedRoute.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\StudentDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\FacultyDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\MyCourses.js", ["97"], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\Courses.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ComingSoon.js", [], [], {"ruleId": "98", "severity": 1, "message": "99", "line": 20, "column": 33, "nodeType": "100", "messageId": "101", "endLine": 20, "endColumn": 41}, "no-unused-vars", "'students' is assigned a value but never used.", "Identifier", "unusedVar"]