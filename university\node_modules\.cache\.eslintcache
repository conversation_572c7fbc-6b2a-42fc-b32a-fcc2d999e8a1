[{"D:\\HAMMAD\\React\\New folder\\university\\src\\index.js": "1", "D:\\HAMMAD\\React\\New folder\\university\\src\\App.js": "2", "D:\\HAMMAD\\React\\New folder\\university\\src\\reportWebVitals.js": "3", "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\AuthContext.js": "4", "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\DataContext.js": "5", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Layout.js": "6", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\auth\\Login.js": "7", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\Dashboard.js": "8", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Sidebar.js": "9", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Header.js": "10", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\AdminDashboard.js": "11", "D:\\HAMMAD\\React\\New folder\\university\\src\\data\\mockData.js": "12", "D:\\HAMMAD\\React\\New folder\\university\\src\\utils\\helpers.js": "13", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ProtectedRoute.js": "14", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\StudentDashboard.js": "15", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\FacultyDashboard.js": "16", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\Courses.js": "17", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\MyCourses.js": "18", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ComingSoon.js": "19", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyExams.js": "20", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyFees.js": "21", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyAttendance.js": "22", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyGrades.js": "23", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\library\\Library.js": "24", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\UniversityLogo.js": "25"}, {"size": 535, "mtime": 1748796002682, "results": "26", "hashOfConfig": "27"}, {"size": 7277, "mtime": 1748801753327, "results": "28", "hashOfConfig": "27"}, {"size": 362, "mtime": 1748796002904, "results": "29", "hashOfConfig": "27"}, {"size": 1417, "mtime": 1748796804039, "results": "30", "hashOfConfig": "27"}, {"size": 5045, "mtime": 1748796819184, "results": "31", "hashOfConfig": "27"}, {"size": 1217, "mtime": 1748798520223, "results": "32", "hashOfConfig": "27"}, {"size": 12281, "mtime": 1748803101150, "results": "33", "hashOfConfig": "27"}, {"size": 809, "mtime": 1748797263129, "results": "34", "hashOfConfig": "27"}, {"size": 7060, "mtime": 1748803111238, "results": "35", "hashOfConfig": "27"}, {"size": 6561, "mtime": 1748803089210, "results": "36", "hashOfConfig": "27"}, {"size": 9364, "mtime": 1748798771446, "results": "37", "hashOfConfig": "27"}, {"size": 41971, "mtime": 1748803879201, "results": "38", "hashOfConfig": "27"}, {"size": 5807, "mtime": 1748801926785, "results": "39", "hashOfConfig": "27"}, {"size": 971, "mtime": 1748797008708, "results": "40", "hashOfConfig": "27"}, {"size": 10910, "mtime": 1748799469992, "results": "41", "hashOfConfig": "27"}, {"size": 11172, "mtime": 1748799500562, "results": "42", "hashOfConfig": "27"}, {"size": 10882, "mtime": 1748797848970, "results": "43", "hashOfConfig": "27"}, {"size": 11582, "mtime": 1748801048952, "results": "44", "hashOfConfig": "27"}, {"size": 1246, "mtime": 1748799451512, "results": "45", "hashOfConfig": "27"}, {"size": 12621, "mtime": 1748801825328, "results": "46", "hashOfConfig": "27"}, {"size": 11564, "mtime": 1748801622341, "results": "47", "hashOfConfig": "27"}, {"size": 9607, "mtime": 1748801813043, "results": "48", "hashOfConfig": "27"}, {"size": 9067, "mtime": 1748801891896, "results": "49", "hashOfConfig": "27"}, {"size": 14501, "mtime": 1748802970897, "results": "50", "hashOfConfig": "27"}, {"size": 1926, "mtime": 1748803337442, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jjzcbq", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\HAMMAD\\React\\New folder\\university\\src\\index.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\App.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\reportWebVitals.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\AuthContext.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\DataContext.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Layout.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\auth\\Login.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\Dashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Sidebar.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Header.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\AdminDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\data\\mockData.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\utils\\helpers.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ProtectedRoute.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\StudentDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\FacultyDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\Courses.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\MyCourses.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ComingSoon.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyExams.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyFees.js", ["127", "128"], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyAttendance.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyGrades.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\library\\Library.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\UniversityLogo.js", [], [], {"ruleId": "129", "severity": 1, "message": "130", "line": 28, "column": 9, "nodeType": "131", "messageId": "132", "endLine": 28, "endColumn": 20}, {"ruleId": "129", "severity": 1, "message": "133", "line": 29, "column": 9, "nodeType": "131", "messageId": "132", "endLine": 29, "endColumn": 19}, "no-unused-vars", "'partialFees' is assigned a value but never used.", "Identifier", "unusedVar", "'unpaidFees' is assigned a value but never used."]