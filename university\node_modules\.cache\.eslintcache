[{"D:\\HAMMAD\\React\\New folder\\university\\src\\index.js": "1", "D:\\HAMMAD\\React\\New folder\\university\\src\\App.js": "2", "D:\\HAMMAD\\React\\New folder\\university\\src\\reportWebVitals.js": "3", "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\AuthContext.js": "4", "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\DataContext.js": "5", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Layout.js": "6", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\auth\\Login.js": "7", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\Dashboard.js": "8", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Sidebar.js": "9", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Header.js": "10", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\AdminDashboard.js": "11", "D:\\HAMMAD\\React\\New folder\\university\\src\\data\\mockData.js": "12", "D:\\HAMMAD\\React\\New folder\\university\\src\\utils\\helpers.js": "13", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ProtectedRoute.js": "14", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\StudentDashboard.js": "15", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\FacultyDashboard.js": "16", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\Courses.js": "17", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\MyCourses.js": "18", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ComingSoon.js": "19", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyGrades.js": "20", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyAttendance.js": "21", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyFees.js": "22", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyExams.js": "23", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\library\\Library.js": "24"}, {"size": 535, "mtime": 1748796002682, "results": "25", "hashOfConfig": "26"}, {"size": 7277, "mtime": 1748801753327, "results": "27", "hashOfConfig": "26"}, {"size": 362, "mtime": 1748796002904, "results": "28", "hashOfConfig": "26"}, {"size": 1417, "mtime": 1748796804039, "results": "29", "hashOfConfig": "26"}, {"size": 5045, "mtime": 1748796819184, "results": "30", "hashOfConfig": "26"}, {"size": 1217, "mtime": 1748798520223, "results": "31", "hashOfConfig": "26"}, {"size": 10893, "mtime": 1748798738332, "results": "32", "hashOfConfig": "26"}, {"size": 809, "mtime": 1748797263129, "results": "33", "hashOfConfig": "26"}, {"size": 6704, "mtime": 1748798582571, "results": "34", "hashOfConfig": "26"}, {"size": 4723, "mtime": 1748798634909, "results": "35", "hashOfConfig": "26"}, {"size": 9364, "mtime": 1748798771446, "results": "36", "hashOfConfig": "26"}, {"size": 29668, "mtime": 1748800298710, "results": "37", "hashOfConfig": "26"}, {"size": 5739, "mtime": 1748796877868, "results": "38", "hashOfConfig": "26"}, {"size": 971, "mtime": 1748797008708, "results": "39", "hashOfConfig": "26"}, {"size": 10910, "mtime": 1748799469992, "results": "40", "hashOfConfig": "26"}, {"size": 11172, "mtime": 1748799500562, "results": "41", "hashOfConfig": "26"}, {"size": 10882, "mtime": 1748797848970, "results": "42", "hashOfConfig": "26"}, {"size": 11582, "mtime": 1748801048952, "results": "43", "hashOfConfig": "26"}, {"size": 1246, "mtime": 1748799451512, "results": "44", "hashOfConfig": "26"}, {"size": 9038, "mtime": 1748801787052, "results": "45", "hashOfConfig": "26"}, {"size": 9607, "mtime": 1748801813043, "results": "46", "hashOfConfig": "26"}, {"size": 11564, "mtime": 1748801622341, "results": "47", "hashOfConfig": "26"}, {"size": 12621, "mtime": 1748801825328, "results": "48", "hashOfConfig": "26"}, {"size": 14235, "mtime": 1748801709787, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jjzcbq", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\HAMMAD\\React\\New folder\\university\\src\\index.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\App.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\reportWebVitals.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\AuthContext.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\DataContext.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Layout.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\auth\\Login.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\Dashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Sidebar.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Header.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\AdminDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\data\\mockData.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\utils\\helpers.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ProtectedRoute.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\StudentDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\FacultyDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\Courses.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\MyCourses.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ComingSoon.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyGrades.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyAttendance.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyFees.js", ["122", "123"], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyExams.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\library\\Library.js", [], [], {"ruleId": "124", "severity": 1, "message": "125", "line": 28, "column": 9, "nodeType": "126", "messageId": "127", "endLine": 28, "endColumn": 20}, {"ruleId": "124", "severity": 1, "message": "128", "line": 29, "column": 9, "nodeType": "126", "messageId": "127", "endLine": 29, "endColumn": 19}, "no-unused-vars", "'partialFees' is assigned a value but never used.", "Identifier", "unusedVar", "'unpaidFees' is assigned a value but never used."]