[{"D:\\HAMMAD\\React\\New folder\\university\\src\\index.js": "1", "D:\\HAMMAD\\React\\New folder\\university\\src\\App.js": "2", "D:\\HAMMAD\\React\\New folder\\university\\src\\reportWebVitals.js": "3", "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\AuthContext.js": "4", "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\DataContext.js": "5", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Layout.js": "6", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\auth\\Login.js": "7", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\Dashboard.js": "8", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Sidebar.js": "9", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Header.js": "10", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\AdminDashboard.js": "11", "D:\\HAMMAD\\React\\New folder\\university\\src\\data\\mockData.js": "12", "D:\\HAMMAD\\React\\New folder\\university\\src\\utils\\helpers.js": "13", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ProtectedRoute.js": "14", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\StudentDashboard.js": "15", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\FacultyDashboard.js": "16", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\Courses.js": "17", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\MyCourses.js": "18", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ComingSoon.js": "19", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyExams.js": "20", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyFees.js": "21", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyAttendance.js": "22", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyGrades.js": "23", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\library\\Library.js": "24", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\UniversityLogo.js": "25", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\auth\\Register.js": "26", "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\admin\\InstructorManagement.js": "27"}, {"size": 535, "mtime": 1748796002682, "results": "28", "hashOfConfig": "29"}, {"size": 7370, "mtime": 1748866586870, "results": "30", "hashOfConfig": "29"}, {"size": 362, "mtime": 1748796002904, "results": "31", "hashOfConfig": "29"}, {"size": 2249, "mtime": 1748804934963, "results": "32", "hashOfConfig": "29"}, {"size": 5045, "mtime": 1748796819184, "results": "33", "hashOfConfig": "29"}, {"size": 1217, "mtime": 1748798520223, "results": "34", "hashOfConfig": "29"}, {"size": 18171, "mtime": 1748858876427, "results": "35", "hashOfConfig": "29"}, {"size": 809, "mtime": 1748797263129, "results": "36", "hashOfConfig": "29"}, {"size": 7060, "mtime": 1748803111238, "results": "37", "hashOfConfig": "29"}, {"size": 7846, "mtime": 1748804718507, "results": "38", "hashOfConfig": "29"}, {"size": 9364, "mtime": 1748798771446, "results": "39", "hashOfConfig": "29"}, {"size": 60913, "mtime": 1748805889332, "results": "40", "hashOfConfig": "29"}, {"size": 5807, "mtime": 1748801926785, "results": "41", "hashOfConfig": "29"}, {"size": 971, "mtime": 1748797008708, "results": "42", "hashOfConfig": "29"}, {"size": 14190, "mtime": 1748804478097, "results": "43", "hashOfConfig": "29"}, {"size": 11172, "mtime": 1748799500562, "results": "44", "hashOfConfig": "29"}, {"size": 10882, "mtime": 1748797848970, "results": "45", "hashOfConfig": "29"}, {"size": 11582, "mtime": 1748801048952, "results": "46", "hashOfConfig": "29"}, {"size": 1246, "mtime": 1748799451512, "results": "47", "hashOfConfig": "29"}, {"size": 12621, "mtime": 1748801825328, "results": "48", "hashOfConfig": "29"}, {"size": 11564, "mtime": 1748801622341, "results": "49", "hashOfConfig": "29"}, {"size": 9607, "mtime": 1748801813043, "results": "50", "hashOfConfig": "29"}, {"size": 9067, "mtime": 1748801891896, "results": "51", "hashOfConfig": "29"}, {"size": 14501, "mtime": 1748802970897, "results": "52", "hashOfConfig": "29"}, {"size": 1926, "mtime": 1748803337442, "results": "53", "hashOfConfig": "29"}, {"size": 14066, "mtime": 1748804906361, "results": "54", "hashOfConfig": "29"}, {"size": 34255, "mtime": 1748866547115, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jjzcbq", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\HAMMAD\\React\\New folder\\university\\src\\index.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\App.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\reportWebVitals.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\AuthContext.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\context\\DataContext.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Layout.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\auth\\Login.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\Dashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Sidebar.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\Header.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\AdminDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\data\\mockData.js", ["137"], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\utils\\helpers.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ProtectedRoute.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\StudentDashboard.js", ["138"], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\dashboard\\FacultyDashboard.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\Courses.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\courses\\MyCourses.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\ComingSoon.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyExams.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyFees.js", ["139", "140"], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyAttendance.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\student\\MyGrades.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\library\\Library.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\common\\UniversityLogo.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\auth\\Register.js", [], [], "D:\\HAMMAD\\React\\New folder\\university\\src\\components\\admin\\InstructorManagement.js", ["141", "142"], [], {"ruleId": "143", "severity": 1, "message": "144", "line": 1698, "column": 14, "nodeType": "145", "messageId": "146", "endLine": 1698, "endColumn": 48}, {"ruleId": "147", "severity": 1, "message": "148", "line": 38, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 38, "endColumn": 22}, {"ruleId": "147", "severity": 1, "message": "151", "line": 28, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 28, "endColumn": 20}, {"ruleId": "147", "severity": 1, "message": "152", "line": 29, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 29, "endColumn": 19}, {"ruleId": "147", "severity": 1, "message": "153", "line": 12, "column": 3, "nodeType": "149", "messageId": "150", "endLine": 12, "endColumn": 13}, {"ruleId": "147", "severity": 1, "message": "154", "line": 14, "column": 3, "nodeType": "149", "messageId": "150", "endLine": 14, "endColumn": 10}, "no-script-url", "Script URL is a form of eval.", "Literal", "unexpectedScriptURL", "no-unused-vars", "'upcomingExams' is assigned a value but never used.", "Identifier", "unusedVar", "'partialFees' is assigned a value but never used.", "'unpaidFees' is assigned a value but never used.", "'FiCalendar' is defined but never used.", "'FiCheck' is defined but never used."]