{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\dashboard\\\\StudentDashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { FiBookOpen, FiCalendar, FiDollarSign, FiFileText, FiBook, FiAward } from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport { useData } from '../../context/DataContext';\nimport { formatCurrency, calculateAttendancePercentage, getGradeColor } from '../../utils/helpers';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    courses,\n    enrollments,\n    grades,\n    attendance,\n    fees,\n    borrowedBooks,\n    exams\n  } = useData();\n\n  // Get student's data\n  const studentEnrollments = enrollments.filter(e => e.studentId === user.id);\n  const studentCourses = courses.filter(c => studentEnrollments.some(e => e.courseId === c.id));\n  const studentGrades = grades.filter(g => g.studentId === user.id);\n  const studentAttendance = attendance.filter(a => a.studentId === user.id);\n  const studentFees = fees.filter(f => f.studentId === user.id);\n  const studentBorrowedBooks = borrowedBooks.filter(b => b.studentId === user.id && b.status === 'borrowed');\n\n  // Get upcoming exams for student's courses\n  const upcomingExams = exams.filter(exam => {\n    const examDate = new Date(exam.date);\n    const today = new Date();\n    return examDate >= today && studentCourses.some(c => c.id === exam.courseId);\n  }).slice(0, 3);\n\n  // Calculate statistics\n  const currentSemesterFees = studentFees.find(f => f.semester === user.semester);\n  const totalFeesDue = currentSemesterFees ? currentSemesterFees.dueAmount : 0;\n  const attendancePercentage = calculateAttendancePercentage(studentAttendance);\n  const currentGPA = studentGrades.length > 0 ? (studentGrades.reduce((sum, g) => sum + g.gpa, 0) / studentGrades.length).toFixed(2) : '0.00';\n  const stats = [{\n    name: 'Enrolled Courses',\n    value: studentCourses.length,\n    subValue: 'This semester',\n    icon: FiBookOpen,\n    color: 'bg-blue-500'\n  }, {\n    name: 'Current GPA',\n    value: currentGPA,\n    subValue: 'Overall performance',\n    icon: FiAward,\n    color: 'bg-green-500'\n  }, {\n    name: 'Attendance',\n    value: `${attendancePercentage}%`,\n    subValue: 'This semester',\n    icon: FiCalendar,\n    color: 'bg-purple-500'\n  }, {\n    name: 'Fees Due',\n    value: formatCurrency(totalFeesDue),\n    subValue: 'Current semester',\n    icon: FiDollarSign,\n    color: totalFeesDue > 0 ? 'bg-red-500' : 'bg-green-500'\n  }];\n  const recentGrades = studentGrades.slice(-3).reverse();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center lg:text-left\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold gradient-text mb-2\",\n        children: \"Student Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-300 text-lg\",\n        children: [\"Welcome back, \", user.name, \"! Here's your academic overview.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `${stat.color} p-3 rounded-lg`,\n            children: /*#__PURE__*/_jsxDEV(stat.icon, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: stat.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: stat.subValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Current Courses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: studentCourses.slice(0, 4).map(course => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: course.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [course.code, \" \\u2022 \", course.credits, \" credits\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: [course.schedule.days.join(', '), \" \\u2022 \", course.schedule.time]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                children: \"Enrolled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this)]\n          }, course.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), studentCourses.length > 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n            children: \"View all courses \\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Recent Grades\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: recentGrades.length > 0 ? recentGrades.map(grade => {\n            const course = courses.find(c => c.id === grade.courseId);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: course === null || course === void 0 ? void 0 : course.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: course === null || course === void 0 ? void 0 : course.code\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Semester \", grade.semester, \", \", grade.year]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-lg font-bold ${getGradeColor(grade.grade)}`,\n                  children: grade.grade\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [\"GPA: \", grade.gpa]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 21\n              }, this)]\n            }, grade.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-center py-4\",\n            children: \"No grades available yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n            children: \"View all grades \\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Upcoming Exams\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: upcomingExams.length > 0 ? upcomingExams.map(exam => {\n            const course = courses.find(c => c.id === exam.courseId);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-red-100 p-2 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(FiFileText, {\n                  className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: exam.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: course === null || course === void 0 ? void 0 : course.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [exam.date, \" \\u2022 \", exam.time]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this)]\n            }, exam.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-center py-4\",\n            children: \"No upcoming exams\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Borrowed Books\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: studentBorrowedBooks.length > 0 ? studentBorrowedBooks.slice(0, 3).map(borrowedBook => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-100 p-2 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(FiBook, {\n                className: \"h-4 w-4 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"Book Title\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"Due: \", borrowedBook.dueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this)]\n          }, borrowedBook.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-center py-4\",\n            children: \"No borrowed books\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n              className: \"h-5 w-5 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              children: \"View Schedule\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(FiDollarSign, {\n              className: \"h-5 w-5 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              children: \"Pay Fees\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(FiBook, {\n              className: \"h-5 w-5 text-purple-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              children: \"Library\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"w8LKEAreWF+KxcZ6dSW4J8HPilk=\", false, function () {\n  return [useAuth, useData];\n});\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "FiBookOpen", "FiCalendar", "FiDollarSign", "FiFileText", "FiBook", "FiAward", "useAuth", "useData", "formatCurrency", "calculateAttendancePercentage", "getGradeColor", "jsxDEV", "_jsxDEV", "StudentDashboard", "_s", "user", "courses", "enrollments", "grades", "attendance", "fees", "borrowedBooks", "exams", "studentEnrollments", "filter", "e", "studentId", "id", "studentCourses", "c", "some", "courseId", "studentGrades", "g", "studentAttendance", "a", "studentFees", "f", "studentBorrowedBooks", "b", "status", "upcomingExams", "exam", "examDate", "Date", "date", "today", "slice", "currentSemesterFees", "find", "semester", "totalFeesDue", "dueAmount", "attendancePercentage", "currentGPA", "length", "reduce", "sum", "gpa", "toFixed", "stats", "name", "value", "subValue", "icon", "color", "recentGrades", "reverse", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "stat", "index", "course", "code", "credits", "schedule", "days", "join", "time", "grade", "year", "borrowedBook", "dueDate", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/dashboard/StudentDashboard.js"], "sourcesContent": ["import React from 'react';\nimport {\n  FiBookOpen,\n  FiCalendar,\n  FiDollarSign,\n  FiFileText,\n  FiBook,\n  FiAward\n} from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport { useData } from '../../context/DataContext';\nimport { formatCurrency, calculateAttendancePercentage, getGradeColor } from '../../utils/helpers';\n\nconst StudentDashboard = () => {\n  const { user } = useAuth();\n  const { \n    courses, \n    enrollments, \n    grades, \n    attendance, \n    fees, \n    borrowedBooks,\n    exams \n  } = useData();\n\n  // Get student's data\n  const studentEnrollments = enrollments.filter(e => e.studentId === user.id);\n  const studentCourses = courses.filter(c => \n    studentEnrollments.some(e => e.courseId === c.id)\n  );\n  const studentGrades = grades.filter(g => g.studentId === user.id);\n  const studentAttendance = attendance.filter(a => a.studentId === user.id);\n  const studentFees = fees.filter(f => f.studentId === user.id);\n  const studentBorrowedBooks = borrowedBooks.filter(b => b.studentId === user.id && b.status === 'borrowed');\n  \n  // Get upcoming exams for student's courses\n  const upcomingExams = exams.filter(exam => {\n    const examDate = new Date(exam.date);\n    const today = new Date();\n    return examDate >= today && studentCourses.some(c => c.id === exam.courseId);\n  }).slice(0, 3);\n\n  // Calculate statistics\n  const currentSemesterFees = studentFees.find(f => f.semester === user.semester);\n  const totalFeesDue = currentSemesterFees ? currentSemesterFees.dueAmount : 0;\n  const attendancePercentage = calculateAttendancePercentage(studentAttendance);\n  const currentGPA = studentGrades.length > 0 \n    ? (studentGrades.reduce((sum, g) => sum + g.gpa, 0) / studentGrades.length).toFixed(2)\n    : '0.00';\n\n  const stats = [\n    {\n      name: 'Enrolled Courses',\n      value: studentCourses.length,\n      subValue: 'This semester',\n      icon: FiBookOpen,\n      color: 'bg-blue-500'\n    },\n    {\n      name: 'Current GPA',\n      value: currentGPA,\n      subValue: 'Overall performance',\n      icon: FiAward,\n      color: 'bg-green-500'\n    },\n    {\n      name: 'Attendance',\n      value: `${attendancePercentage}%`,\n      subValue: 'This semester',\n      icon: FiCalendar,\n      color: 'bg-purple-500'\n    },\n    {\n      name: 'Fees Due',\n      value: formatCurrency(totalFeesDue),\n      subValue: 'Current semester',\n      icon: FiDollarSign,\n      color: totalFeesDue > 0 ? 'bg-red-500' : 'bg-green-500'\n    }\n  ];\n\n  const recentGrades = studentGrades.slice(-3).reverse();\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"text-center lg:text-left\">\n        <h1 className=\"text-4xl font-bold gradient-text mb-2\">Student Dashboard</h1>\n        <p className=\"text-gray-300 text-lg\">Welcome back, {user.name}! Here's your academic overview.</p>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {stats.map((stat, index) => (\n          <div key={index} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center\">\n              <div className={`${stat.color} p-3 rounded-lg`}>\n                <stat.icon className=\"h-6 w-6 text-white\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">{stat.name}</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stat.value}</p>\n                <p className=\"text-sm text-gray-500\">{stat.subValue}</p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Main Content Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Current Courses */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Current Courses</h3>\n          <div className=\"space-y-4\">\n            {studentCourses.slice(0, 4).map((course) => (\n              <div key={course.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">{course.name}</h4>\n                  <p className=\"text-sm text-gray-600\">{course.code} • {course.credits} credits</p>\n                  <p className=\"text-xs text-gray-500\">\n                    {course.schedule.days.join(', ')} • {course.schedule.time}\n                  </p>\n                </div>\n                <div className=\"text-right\">\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                    Enrolled\n                  </span>\n                </div>\n              </div>\n            ))}\n          </div>\n          {studentCourses.length > 4 && (\n            <div className=\"mt-4\">\n              <button className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\">\n                View all courses →\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Recent Grades */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Grades</h3>\n          <div className=\"space-y-4\">\n            {recentGrades.length > 0 ? (\n              recentGrades.map((grade) => {\n                const course = courses.find(c => c.id === grade.courseId);\n                return (\n                  <div key={grade.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg\">\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">{course?.name}</h4>\n                      <p className=\"text-sm text-gray-600\">{course?.code}</p>\n                      <p className=\"text-xs text-gray-500\">Semester {grade.semester}, {grade.year}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className={`text-lg font-bold ${getGradeColor(grade.grade)}`}>\n                        {grade.grade}\n                      </div>\n                      <div className=\"text-sm text-gray-600\">GPA: {grade.gpa}</div>\n                    </div>\n                  </div>\n                );\n              })\n            ) : (\n              <p className=\"text-gray-500 text-center py-4\">No grades available yet</p>\n            )}\n          </div>\n          <div className=\"mt-4\">\n            <button className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\">\n              View all grades →\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Upcoming Exams */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Upcoming Exams</h3>\n          <div className=\"space-y-3\">\n            {upcomingExams.length > 0 ? (\n              upcomingExams.map((exam) => {\n                const course = courses.find(c => c.id === exam.courseId);\n                return (\n                  <div key={exam.id} className=\"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg\">\n                    <div className=\"bg-red-100 p-2 rounded-lg\">\n                      <FiFileText className=\"h-4 w-4 text-red-600\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h4 className=\"font-medium text-gray-900\">{exam.name}</h4>\n                      <p className=\"text-sm text-gray-600\">{course?.name}</p>\n                      <p className=\"text-xs text-gray-500\">{exam.date} • {exam.time}</p>\n                    </div>\n                  </div>\n                );\n              })\n            ) : (\n              <p className=\"text-gray-500 text-center py-4\">No upcoming exams</p>\n            )}\n          </div>\n        </div>\n\n        {/* Library Books */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Borrowed Books</h3>\n          <div className=\"space-y-3\">\n            {studentBorrowedBooks.length > 0 ? (\n              studentBorrowedBooks.slice(0, 3).map((borrowedBook) => (\n                <div key={borrowedBook.id} className=\"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg\">\n                  <div className=\"bg-blue-100 p-2 rounded-lg\">\n                    <FiBook className=\"h-4 w-4 text-blue-600\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-medium text-gray-900\">Book Title</h4>\n                    <p className=\"text-sm text-gray-600\">Due: {borrowedBook.dueDate}</p>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <p className=\"text-gray-500 text-center py-4\">No borrowed books</p>\n            )}\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n          <div className=\"space-y-3\">\n            <button className=\"w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50\">\n              <FiCalendar className=\"h-5 w-5 text-blue-600\" />\n              <span className=\"text-sm font-medium\">View Schedule</span>\n            </button>\n            <button className=\"w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50\">\n              <FiDollarSign className=\"h-5 w-5 text-green-600\" />\n              <span className=\"text-sm font-medium\">Pay Fees</span>\n            </button>\n            <button className=\"w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50\">\n              <FiBook className=\"h-5 w-5 text-purple-600\" />\n              <span className=\"text-sm font-medium\">Library</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,UAAU,EACVC,UAAU,EACVC,YAAY,EACZC,UAAU,EACVC,MAAM,EACNC,OAAO,QACF,gBAAgB;AACvB,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,EAAEC,6BAA6B,EAAEC,aAAa,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnG,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM;IACJU,OAAO;IACPC,WAAW;IACXC,MAAM;IACNC,UAAU;IACVC,IAAI;IACJC,aAAa;IACbC;EACF,CAAC,GAAGf,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMgB,kBAAkB,GAAGN,WAAW,CAACO,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKX,IAAI,CAACY,EAAE,CAAC;EAC3E,MAAMC,cAAc,GAAGZ,OAAO,CAACQ,MAAM,CAACK,CAAC,IACrCN,kBAAkB,CAACO,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACM,QAAQ,KAAKF,CAAC,CAACF,EAAE,CAClD,CAAC;EACD,MAAMK,aAAa,GAAGd,MAAM,CAACM,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACP,SAAS,KAAKX,IAAI,CAACY,EAAE,CAAC;EACjE,MAAMO,iBAAiB,GAAGf,UAAU,CAACK,MAAM,CAACW,CAAC,IAAIA,CAAC,CAACT,SAAS,KAAKX,IAAI,CAACY,EAAE,CAAC;EACzE,MAAMS,WAAW,GAAGhB,IAAI,CAACI,MAAM,CAACa,CAAC,IAAIA,CAAC,CAACX,SAAS,KAAKX,IAAI,CAACY,EAAE,CAAC;EAC7D,MAAMW,oBAAoB,GAAGjB,aAAa,CAACG,MAAM,CAACe,CAAC,IAAIA,CAAC,CAACb,SAAS,KAAKX,IAAI,CAACY,EAAE,IAAIY,CAAC,CAACC,MAAM,KAAK,UAAU,CAAC;;EAE1G;EACA,MAAMC,aAAa,GAAGnB,KAAK,CAACE,MAAM,CAACkB,IAAI,IAAI;IACzC,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAACF,IAAI,CAACG,IAAI,CAAC;IACpC,MAAMC,KAAK,GAAG,IAAIF,IAAI,CAAC,CAAC;IACxB,OAAOD,QAAQ,IAAIG,KAAK,IAAIlB,cAAc,CAACE,IAAI,CAACD,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKe,IAAI,CAACX,QAAQ,CAAC;EAC9E,CAAC,CAAC,CAACgB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEd;EACA,MAAMC,mBAAmB,GAAGZ,WAAW,CAACa,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACa,QAAQ,KAAKnC,IAAI,CAACmC,QAAQ,CAAC;EAC/E,MAAMC,YAAY,GAAGH,mBAAmB,GAAGA,mBAAmB,CAACI,SAAS,GAAG,CAAC;EAC5E,MAAMC,oBAAoB,GAAG5C,6BAA6B,CAACyB,iBAAiB,CAAC;EAC7E,MAAMoB,UAAU,GAAGtB,aAAa,CAACuB,MAAM,GAAG,CAAC,GACvC,CAACvB,aAAa,CAACwB,MAAM,CAAC,CAACC,GAAG,EAAExB,CAAC,KAAKwB,GAAG,GAAGxB,CAAC,CAACyB,GAAG,EAAE,CAAC,CAAC,GAAG1B,aAAa,CAACuB,MAAM,EAAEI,OAAO,CAAC,CAAC,CAAC,GACpF,MAAM;EAEV,MAAMC,KAAK,GAAG,CACZ;IACEC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAElC,cAAc,CAAC2B,MAAM;IAC5BQ,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAEhE,UAAU;IAChBiE,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAER,UAAU;IACjBS,QAAQ,EAAE,qBAAqB;IAC/BC,IAAI,EAAE3D,OAAO;IACb4D,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,GAAGT,oBAAoB,GAAG;IACjCU,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE/D,UAAU;IAChBgE,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAEtD,cAAc,CAAC2C,YAAY,CAAC;IACnCY,QAAQ,EAAE,kBAAkB;IAC5BC,IAAI,EAAE9D,YAAY;IAClB+D,KAAK,EAAEd,YAAY,GAAG,CAAC,GAAG,YAAY,GAAG;EAC3C,CAAC,CACF;EAED,MAAMe,YAAY,GAAGlC,aAAa,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC,CAACoB,OAAO,CAAC,CAAC;EAEtD,oBACEvD,OAAA;IAAKwD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzD,OAAA;MAAKwD,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvCzD,OAAA;QAAIwD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5E7D,OAAA;QAAGwD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,gBAAc,EAACtD,IAAI,CAAC8C,IAAI,EAAC,kCAAgC;MAAA;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/F,CAAC,eAGN7D,OAAA;MAAKwD,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClET,KAAK,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBhE,OAAA;QAAiBwD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACnFzD,OAAA;UAAKwD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzD,OAAA;YAAKwD,SAAS,EAAE,GAAGO,IAAI,CAACV,KAAK,iBAAkB;YAAAI,QAAA,eAC7CzD,OAAA,CAAC+D,IAAI,CAACX,IAAI;cAACI,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBzD,OAAA;cAAGwD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEM,IAAI,CAACd;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChE7D,OAAA;cAAGwD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEM,IAAI,CAACb;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChE7D,OAAA;cAAGwD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEM,IAAI,CAACZ;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAVEG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN7D,OAAA;MAAKwD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDzD,OAAA;QAAKwD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEzD,OAAA;UAAIwD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E7D,OAAA;UAAKwD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBzC,cAAc,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC2B,GAAG,CAAEG,MAAM,iBACrCjE,OAAA;YAAqBwD,SAAS,EAAC,yEAAyE;YAAAC,QAAA,gBACtGzD,OAAA;cAAAyD,QAAA,gBACEzD,OAAA;gBAAIwD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEQ,MAAM,CAAChB;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5D7D,OAAA;gBAAGwD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAEQ,MAAM,CAACC,IAAI,EAAC,UAAG,EAACD,MAAM,CAACE,OAAO,EAAC,UAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjF7D,OAAA;gBAAGwD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACjCQ,MAAM,CAACG,QAAQ,CAACC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC,EAAC,UAAG,EAACL,MAAM,CAACG,QAAQ,CAACG,IAAI;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN7D,OAAA;cAAKwD,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBzD,OAAA;gBAAMwD,SAAS,EAAC,qGAAqG;gBAAAC,QAAA,EAAC;cAEtH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAZEI,MAAM,CAAClD,EAAE;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAad,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EACL7C,cAAc,CAAC2B,MAAM,GAAG,CAAC,iBACxB3C,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBzD,OAAA;YAAQwD,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EAAC;UAEhF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7D,OAAA;QAAKwD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEzD,OAAA;UAAIwD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E7D,OAAA;UAAKwD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBH,YAAY,CAACX,MAAM,GAAG,CAAC,GACtBW,YAAY,CAACQ,GAAG,CAAEU,KAAK,IAAK;YAC1B,MAAMP,MAAM,GAAG7D,OAAO,CAACiC,IAAI,CAACpB,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKyD,KAAK,CAACrD,QAAQ,CAAC;YACzD,oBACEnB,OAAA;cAAoBwD,SAAS,EAAC,yEAAyE;cAAAC,QAAA,gBACrGzD,OAAA;gBAAAyD,QAAA,gBACEzD,OAAA;kBAAIwD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEQ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhB;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7D7D,OAAA;kBAAGwD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEQ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvD7D,OAAA;kBAAGwD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,WAAS,EAACe,KAAK,CAAClC,QAAQ,EAAC,IAAE,EAACkC,KAAK,CAACC,IAAI;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eACN7D,OAAA;gBAAKwD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzD,OAAA;kBAAKwD,SAAS,EAAE,qBAAqB1D,aAAa,CAAC0E,KAAK,CAACA,KAAK,CAAC,EAAG;kBAAAf,QAAA,EAC/De,KAAK,CAACA;gBAAK;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACN7D,OAAA;kBAAKwD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,OAAK,EAACe,KAAK,CAAC1B,GAAG;gBAAA;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA,GAXEW,KAAK,CAACzD,EAAE;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYb,CAAC;UAEV,CAAC,CAAC,gBAEF7D,OAAA;YAAGwD,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACzE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN7D,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBzD,OAAA;YAAQwD,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EAAC;UAEhF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAKwD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDzD,OAAA;QAAKwD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEzD,OAAA;UAAIwD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E7D,OAAA;UAAKwD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB5B,aAAa,CAACc,MAAM,GAAG,CAAC,GACvBd,aAAa,CAACiC,GAAG,CAAEhC,IAAI,IAAK;YAC1B,MAAMmC,MAAM,GAAG7D,OAAO,CAACiC,IAAI,CAACpB,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKe,IAAI,CAACX,QAAQ,CAAC;YACxD,oBACEnB,OAAA;cAAmBwD,SAAS,EAAC,mEAAmE;cAAAC,QAAA,gBAC9FzD,OAAA;gBAAKwD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACxCzD,OAAA,CAACT,UAAU;kBAACiE,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACN7D,OAAA;gBAAKwD,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBzD,OAAA;kBAAIwD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAE3B,IAAI,CAACmB;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1D7D,OAAA;kBAAGwD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEQ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhB;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvD7D,OAAA;kBAAGwD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAE3B,IAAI,CAACG,IAAI,EAAC,UAAG,EAACH,IAAI,CAACyC,IAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA,GARE/B,IAAI,CAACf,EAAE;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASZ,CAAC;UAEV,CAAC,CAAC,gBAEF7D,OAAA;YAAGwD,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACnE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7D,OAAA;QAAKwD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEzD,OAAA;UAAIwD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E7D,OAAA;UAAKwD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB/B,oBAAoB,CAACiB,MAAM,GAAG,CAAC,GAC9BjB,oBAAoB,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC2B,GAAG,CAAEY,YAAY,iBAChD1E,OAAA;YAA2BwD,SAAS,EAAC,mEAAmE;YAAAC,QAAA,gBACtGzD,OAAA;cAAKwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzCzD,OAAA,CAACR,MAAM;gBAACgE,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACN7D,OAAA;cAAKwD,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBzD,OAAA;gBAAIwD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzD7D,OAAA;gBAAGwD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,OAAK,EAACiB,YAAY,CAACC,OAAO;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC;UAAA,GAPEa,YAAY,CAAC3D,EAAE;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQpB,CACN,CAAC,gBAEF7D,OAAA;YAAGwD,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACnE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7D,OAAA;QAAKwD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEzD,OAAA;UAAIwD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E7D,OAAA;UAAKwD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzD,OAAA;YAAQwD,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBAC3GzD,OAAA,CAACX,UAAU;cAACmE,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD7D,OAAA;cAAMwD,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACT7D,OAAA;YAAQwD,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBAC3GzD,OAAA,CAACV,YAAY;cAACkE,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnD7D,OAAA;cAAMwD,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACT7D,OAAA;YAAQwD,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBAC3GzD,OAAA,CAACR,MAAM;cAACgE,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C7D,OAAA;cAAMwD,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3D,EAAA,CA1OID,gBAAgB;EAAA,QACHP,OAAO,EASpBC,OAAO;AAAA;AAAAiF,EAAA,GAVP3E,gBAAgB;AA4OtB,eAAeA,gBAAgB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}