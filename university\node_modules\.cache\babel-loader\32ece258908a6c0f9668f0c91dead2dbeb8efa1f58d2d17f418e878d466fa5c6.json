{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\nimport { DataProvider } from './context/DataContext';\nimport Layout from './components/common/Layout';\nimport Login from './components/auth/Login';\nimport Dashboard from './components/dashboard/Dashboard';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport MyCourses from './components/courses/MyCourses';\nimport Courses from './components/courses/Courses';\nimport ComingSoon from './components/common/ComingSoon';\nimport MyGrades from './components/student/MyGrades';\nimport MyAttendance from './components/student/MyAttendance';\nimport MyFees from './components/student/MyFees';\nimport MyExams from './components/student/MyExams';\nimport Library from './components/library/Library';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(DataProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App dark min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 40\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                index: true,\n                element: /*#__PURE__*/_jsxDEV(Navigate, {\n                  to: \"/dashboard\",\n                  replace: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 27,\n                  columnNumber: 39\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"dashboard\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 32,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-courses\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(MyCourses, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 40,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"courses\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(Courses, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 48,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"students\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Student Management\",\n                    description: \"Manage student records, enrollment, and academic information.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 58,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"faculty\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Faculty Management\",\n                    description: \"Manage faculty members, assignments, and professional information.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"academic\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Academic Management\",\n                    description: \"Manage academic records, grades, and transcripts.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 74,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"fees\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Fee Management\",\n                    description: \"Manage fee structures, payments, and financial records.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 82,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"library\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Library, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"exams\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Examination System\",\n                    description: \"Schedule exams, manage results, and track performance.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"reports\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Reports & Analytics\",\n                    description: \"Generate comprehensive reports and analytics.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"settings\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"System Settings\",\n                    description: \"Configure system settings and preferences.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-students\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"faculty\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"My Students\",\n                    description: \"View and manage students in your courses.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"grades\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"faculty\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Grade Management\",\n                    description: \"Manage student grades and assessments.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"attendance\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"faculty\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Attendance Management\",\n                    description: \"Track and manage student attendance.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-grades\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"student\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"My Grades\",\n                    description: \"View your academic performance and grades.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-attendance\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"student\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"My Attendance\",\n                    description: \"View your attendance records and statistics.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-fees\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"student\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"My Fees\",\n                    description: \"View fee details, payment history, and make payments.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-exams\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"student\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"My Exams\",\n                    description: \"View exam schedules, results, and performance.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"*\",\n                element: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                  title: \"Page Not Found\",\n                  description: \"The page you're looking for doesn't exist or is under development.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "DataProvider", "Layout", "<PERSON><PERSON>", "Dashboard", "ProtectedRoute", "MyCourses", "Courses", "ComingSoon", "MyGrades", "MyAttendance", "MyFees", "MyExams", "Library", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "to", "replace", "requiredRole", "title", "description", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\nimport { DataProvider } from './context/DataContext';\nimport Layout from './components/common/Layout';\nimport Login from './components/auth/Login';\nimport Dashboard from './components/dashboard/Dashboard';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport MyCourses from './components/courses/MyCourses';\nimport Courses from './components/courses/Courses';\nimport ComingSoon from './components/common/ComingSoon';\nimport MyGrades from './components/student/MyGrades';\nimport MyAttendance from './components/student/MyAttendance';\nimport MyFees from './components/student/MyFees';\nimport MyExams from './components/student/MyExams';\nimport Library from './components/library/Library';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <DataProvider>\n        <Router>\n          <div className=\"App dark min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700\">\n            <Routes>\n              <Route path=\"/login\" element={<Login />} />\n              <Route path=\"/\" element={<Layout />}>\n                <Route index element={<Navigate to=\"/dashboard\" replace />} />\n                <Route\n                  path=\"dashboard\"\n                  element={\n                    <ProtectedRoute>\n                      <Dashboard />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"my-courses\"\n                  element={\n                    <ProtectedRoute>\n                      <MyCourses />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"courses\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <Courses />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Admin Routes */}\n                <Route\n                  path=\"students\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"Student Management\" description=\"Manage student records, enrollment, and academic information.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"faculty\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"Faculty Management\" description=\"Manage faculty members, assignments, and professional information.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"academic\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"Academic Management\" description=\"Manage academic records, grades, and transcripts.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"fees\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"Fee Management\" description=\"Manage fee structures, payments, and financial records.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"library\"\n                  element={\n                    <ProtectedRoute>\n                      <Library />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"exams\"\n                  element={\n                    <ProtectedRoute>\n                      <ComingSoon title=\"Examination System\" description=\"Schedule exams, manage results, and track performance.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"reports\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"Reports & Analytics\" description=\"Generate comprehensive reports and analytics.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"settings\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"System Settings\" description=\"Configure system settings and preferences.\" />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Faculty Routes */}\n                <Route\n                  path=\"my-students\"\n                  element={\n                    <ProtectedRoute requiredRole=\"faculty\">\n                      <ComingSoon title=\"My Students\" description=\"View and manage students in your courses.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"grades\"\n                  element={\n                    <ProtectedRoute requiredRole=\"faculty\">\n                      <ComingSoon title=\"Grade Management\" description=\"Manage student grades and assessments.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"attendance\"\n                  element={\n                    <ProtectedRoute requiredRole=\"faculty\">\n                      <ComingSoon title=\"Attendance Management\" description=\"Track and manage student attendance.\" />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Student Routes */}\n                <Route\n                  path=\"my-grades\"\n                  element={\n                    <ProtectedRoute requiredRole=\"student\">\n                      <ComingSoon title=\"My Grades\" description=\"View your academic performance and grades.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"my-attendance\"\n                  element={\n                    <ProtectedRoute requiredRole=\"student\">\n                      <ComingSoon title=\"My Attendance\" description=\"View your attendance records and statistics.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"my-fees\"\n                  element={\n                    <ProtectedRoute requiredRole=\"student\">\n                      <ComingSoon title=\"My Fees\" description=\"View fee details, payment history, and make payments.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"my-exams\"\n                  element={\n                    <ProtectedRoute requiredRole=\"student\">\n                      <ComingSoon title=\"My Exams\" description=\"View exam schedules, results, and performance.\" />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Catch all route */}\n                <Route\n                  path=\"*\"\n                  element={\n                    <ComingSoon title=\"Page Not Found\" description=\"The page you're looking for doesn't exist or is under development.\" />\n                  }\n                />\n              </Route>\n            </Routes>\n          </div>\n        </Router>\n      </DataProvider>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,SAAS,MAAM,gCAAgC;AACtD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,OAAO,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACf,YAAY;IAAAiB,QAAA,eACXF,OAAA,CAACd,YAAY;MAAAgB,QAAA,eACXF,OAAA,CAACnB,MAAM;QAAAqB,QAAA,eACLF,OAAA;UAAKG,SAAS,EAAC,gFAAgF;UAAAD,QAAA,eAC7FF,OAAA,CAAClB,MAAM;YAAAoB,QAAA,gBACLF,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEL,OAAA,CAACZ,KAAK;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEL,OAAA,CAACb,MAAM;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAP,QAAA,gBAClCF,OAAA,CAACjB,KAAK;gBAAC2B,KAAK;gBAACL,OAAO,eAAEL,OAAA,CAAChB,QAAQ;kBAAC2B,EAAE,EAAC,YAAY;kBAACC,OAAO;gBAAA;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,WAAW;gBAChBC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAAAY,QAAA,eACbF,OAAA,CAACX,SAAS;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,YAAY;gBACjBC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAAAY,QAAA,eACbF,OAAA,CAACT,SAAS;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACR,OAAO;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,UAAU;gBACfC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,oBAAoB;oBAACC,WAAW,EAAC;kBAA+D;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,oBAAoB;oBAACC,WAAW,EAAC;kBAAoE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5G;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,UAAU;gBACfC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,qBAAqB;oBAACC,WAAW,EAAC;kBAAmD;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,MAAM;gBACXC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,gBAAgB;oBAACC,WAAW,EAAC;kBAAyD;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAAAY,QAAA,eACbF,OAAA,CAACF,OAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,OAAO;gBACZC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAAAY,QAAA,eACbF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,oBAAoB;oBAACC,WAAW,EAAC;kBAAwD;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,qBAAqB;oBAACC,WAAW,EAAC;kBAA+C;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,UAAU;gBACfC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,iBAAiB;oBAACC,WAAW,EAAC;kBAA4C;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,aAAa;gBAClBC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,aAAa;oBAACC,WAAW,EAAC;kBAA2C;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,QAAQ;gBACbC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,kBAAkB;oBAACC,WAAW,EAAC;kBAAwC;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,YAAY;gBACjBC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,uBAAuB;oBAACC,WAAW,EAAC;kBAAsC;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,WAAW;gBAChBC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,WAAW;oBAACC,WAAW,EAAC;kBAA4C;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,eAAe;gBACpBC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,eAAe;oBAACC,WAAW,EAAC;kBAA8C;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAuD;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,UAAU;gBACfC,OAAO,eACLL,OAAA,CAACV,cAAc;kBAACuB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACP,UAAU;oBAACqB,KAAK,EAAC,UAAU;oBAACC,WAAW,EAAC;kBAAgD;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGFT,OAAA,CAACjB,KAAK;gBACJqB,IAAI,EAAC,GAAG;gBACRC,OAAO,eACLL,OAAA,CAACP,UAAU;kBAACqB,KAAK,EAAC,gBAAgB;kBAACC,WAAW,EAAC;gBAAoE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACtH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEnB;AAACO,EAAA,GA/KQf,GAAG;AAiLZ,eAAeA,GAAG;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}