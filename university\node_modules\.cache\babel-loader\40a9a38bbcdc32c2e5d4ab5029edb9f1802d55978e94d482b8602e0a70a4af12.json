{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\common\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { FiMenu, FiBell, FiUser, FiSettings, FiLogOut, FiChevronDown } from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport UniversityLogo from './UniversityLogo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  onMenuClick\n}) => {\n  _s();\n  var _user$name, _user$name$charAt;\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [dropdownOpen, setDropdownOpen] = useState(false);\n  const dropdownRef = useRef(null);\n  const buttonRef = useRef(null);\n  const [dropdownPosition, setDropdownPosition] = useState({\n    top: 0,\n    right: 0\n  });\n\n  // Calculate dropdown position\n  useEffect(() => {\n    if (dropdownOpen && buttonRef.current) {\n      const rect = buttonRef.current.getBoundingClientRect();\n      setDropdownPosition({\n        top: rect.bottom + 8,\n        right: window.innerWidth - rect.right\n      });\n    }\n  }, [dropdownOpen]);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target) && buttonRef.current && !buttonRef.current.contains(event.target)) {\n        setDropdownOpen(false);\n      }\n    };\n    if (dropdownOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n      };\n    }\n  }, [dropdownOpen]);\n  const handleLogout = () => {\n    logout();\n    setDropdownOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"glass-dark border-b border-primary-600/20 backdrop-blur-xl\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between px-6 py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onMenuClick,\n          className: \"p-2 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring transition-all duration-200 lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(FiMenu, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4 ml-4 lg:ml-0 animate-slide-in-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(UniversityLogo, {\n              className: \"h-14 w-14 animate-float hover-scale\",\n              showUpload: (user === null || user === void 0 ? void 0 : user.role) === 'admin',\n              onLogoChange: logoUrl => {\n                localStorage.setItem('universityLogo', logoUrl);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold gradient-text hover-glow transition-all duration-300\",\n                children: \"NFC IET MULTAN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:block text-gray-400 text-lg animate-pulse-slow\",\n                children: \"|\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:block text-lg font-semibold text-white hover:text-primary-300 transition-colors duration-300\",\n                children: \"Management System\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 font-medium hover:text-gray-300 transition-colors duration-300\",\n              children: \"National Fertilizer Corporation Institute of Engineering & Technology\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4 animate-slide-in-right\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-3 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring relative transition-all duration-200 group hover-lift animate-bounce\",\n          children: [/*#__PURE__*/_jsxDEV(FiBell, {\n            className: \"h-5 w-5 group-hover:animate-bounce\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-500 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-400 animate-ping\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: dropdownRef,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setDropdownOpen(!dropdownOpen),\n            className: \"flex items-center space-x-3 p-2 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring transition-all duration-200 hover-lift group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-purple ring-2 ring-primary-600/30 animate-glow hover-scale\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white text-sm font-semibold animate-scale-in\",\n                  children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden md:block text-left animate-fade-in-up\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-semibold text-white group-hover:text-primary-200 transition-colors duration-200\",\n                  children: user === null || user === void 0 ? void 0 : user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-primary-300 capitalize font-medium group-hover:text-primary-200 transition-colors duration-200\",\n                  children: user === null || user === void 0 ? void 0 : user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FiChevronDown, {\n              className: `h-4 w-4 transition-transform duration-200 ${dropdownOpen ? 'rotate-180' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), dropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fixed right-4 top-16 w-64 bg-dark-900/98 backdrop-blur-xl rounded-xl shadow-2xl py-2 border border-primary-600/30\",\n            style: {\n              zIndex: 99999,\n              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.9), 0 0 0 1px rgba(124, 58, 237, 0.2), 0 0 20px rgba(124, 58, 237, 0.1)',\n              animation: 'fadeInDown 0.3s ease-out'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-3 border-b border-primary-600/20 bg-gradient-to-r from-primary-600/10 to-primary-700/10\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-semibold text-white\",\n                children: user === null || user === void 0 ? void 0 : user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-primary-300\",\n                children: user === null || user === void 0 ? void 0 : user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-400 capitalize mt-1\",\n                children: user === null || user === void 0 ? void 0 : user.role\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"py-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:text-white hover:bg-primary-600/20 transition-all duration-200 group\",\n                children: [/*#__PURE__*/_jsxDEV(FiUser, {\n                  className: \"mr-3 h-4 w-4 group-hover:text-primary-300 transition-colors duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Profile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:text-white hover:bg-primary-600/20 transition-all duration-200 group\",\n                children: [/*#__PURE__*/_jsxDEV(FiSettings, {\n                  className: \"mr-3 h-4 w-4 group-hover:text-primary-300 transition-colors duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Settings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-t border-primary-600/20 py-1\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"flex items-center w-full px-4 py-3 text-sm text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-200 group\",\n                children: [/*#__PURE__*/_jsxDEV(FiLogOut, {\n                  className: \"mr-3 h-4 w-4 group-hover:text-red-300 transition-colors duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Sign out\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"d1yhae14+mVfCs3ba3se5VEWIik=\", false, function () {\n  return [useAuth];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "createPortal", "FiMenu", "FiBell", "FiUser", "FiSettings", "FiLogOut", "FiChevronDown", "useAuth", "UniversityLogo", "jsxDEV", "_jsxDEV", "Header", "onMenuClick", "_s", "_user$name", "_user$name$charAt", "user", "logout", "dropdownOpen", "setDropdownOpen", "dropdownRef", "buttonRef", "dropdownPosition", "setDropdownPosition", "top", "right", "current", "rect", "getBoundingClientRect", "bottom", "window", "innerWidth", "handleClickOutside", "event", "contains", "target", "document", "addEventListener", "removeEventListener", "handleLogout", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showUpload", "role", "onLogoChange", "logoUrl", "localStorage", "setItem", "ref", "name", "char<PERSON>t", "toUpperCase", "style", "zIndex", "boxShadow", "animation", "email", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/common/Header.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport {\n  FiMenu,\n  FiBell,\n  FiUser,\n  FiSettings,\n  FiLogOut,\n  FiChevronDown\n} from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport UniversityLogo from './UniversityLogo';\n\nconst Header = ({ onMenuClick }) => {\n  const { user, logout } = useAuth();\n  const [dropdownOpen, setDropdownOpen] = useState(false);\n  const dropdownRef = useRef(null);\n  const buttonRef = useRef(null);\n  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0 });\n\n  // Calculate dropdown position\n  useEffect(() => {\n    if (dropdownOpen && buttonRef.current) {\n      const rect = buttonRef.current.getBoundingClientRect();\n      setDropdownPosition({\n        top: rect.bottom + 8,\n        right: window.innerWidth - rect.right\n      });\n    }\n  }, [dropdownOpen]);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target) &&\n          buttonRef.current && !buttonRef.current.contains(event.target)) {\n        setDropdownOpen(false);\n      }\n    };\n\n    if (dropdownOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n      };\n    }\n  }, [dropdownOpen]);\n\n  const handleLogout = () => {\n    logout();\n    setDropdownOpen(false);\n  };\n\n  return (\n    <header className=\"glass-dark border-b border-primary-600/20 backdrop-blur-xl\">\n      <div className=\"flex items-center justify-between px-6 py-4\">\n        {/* Left side */}\n        <div className=\"flex items-center\">\n          <button\n            onClick={onMenuClick}\n            className=\"p-2 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring transition-all duration-200 lg:hidden\"\n          >\n            <FiMenu className=\"h-6 w-6\" />\n          </button>\n\n          <div className=\"flex items-center space-x-4 ml-4 lg:ml-0 animate-slide-in-left\">\n            {/* University Logo */}\n            <div className=\"flex-shrink-0\">\n              <UniversityLogo\n                className=\"h-14 w-14 animate-float hover-scale\"\n                showUpload={user?.role === 'admin'}\n                onLogoChange={(logoUrl) => {\n                  localStorage.setItem('universityLogo', logoUrl);\n                }}\n              />\n            </div>\n\n            {/* University Name and System Title */}\n            <div className=\"animate-fade-in-up\">\n              <div className=\"flex items-center space-x-2 mb-1\">\n                <h1 className=\"text-xl font-bold gradient-text hover-glow transition-all duration-300\">\n                  NFC IET MULTAN\n                </h1>\n                <span className=\"hidden sm:block text-gray-400 text-lg animate-pulse-slow\">|</span>\n                <span className=\"hidden sm:block text-lg font-semibold text-white hover:text-primary-300 transition-colors duration-300\">\n                  Management System\n                </span>\n              </div>\n              <p className=\"text-sm text-gray-400 font-medium hover:text-gray-300 transition-colors duration-300\">\n                National Fertilizer Corporation Institute of Engineering & Technology\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Right side */}\n        <div className=\"flex items-center space-x-4 animate-slide-in-right\">\n          {/* Notifications */}\n          <button className=\"p-3 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring relative transition-all duration-200 group hover-lift animate-bounce\">\n            <FiBell className=\"h-5 w-5 group-hover:animate-bounce\" />\n            <span className=\"absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-500 animate-pulse\"></span>\n            <span className=\"absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-400 animate-ping\"></span>\n          </button>\n\n          {/* User dropdown */}\n          <div className=\"relative\" ref={dropdownRef}>\n            <button\n              onClick={() => setDropdownOpen(!dropdownOpen)}\n              className=\"flex items-center space-x-3 p-2 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring transition-all duration-200 hover-lift group\"\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-purple ring-2 ring-primary-600/30 animate-glow hover-scale\">\n                  <span className=\"text-white text-sm font-semibold animate-scale-in\">\n                    {user?.name?.charAt(0)?.toUpperCase()}\n                  </span>\n                </div>\n                <div className=\"hidden md:block text-left animate-fade-in-up\">\n                  <p className=\"text-sm font-semibold text-white group-hover:text-primary-200 transition-colors duration-200\">{user?.name}</p>\n                  <p className=\"text-xs text-primary-300 capitalize font-medium group-hover:text-primary-200 transition-colors duration-200\">{user?.role}</p>\n                </div>\n              </div>\n              <FiChevronDown className={`h-4 w-4 transition-transform duration-200 ${dropdownOpen ? 'rotate-180' : ''}`} />\n            </button>\n\n            {/* Dropdown menu */}\n            {dropdownOpen && (\n              <div\n                className=\"fixed right-4 top-16 w-64 bg-dark-900/98 backdrop-blur-xl rounded-xl shadow-2xl py-2 border border-primary-600/30\"\n                style={{\n                  zIndex: 99999,\n                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.9), 0 0 0 1px rgba(124, 58, 237, 0.2), 0 0 20px rgba(124, 58, 237, 0.1)',\n                  animation: 'fadeInDown 0.3s ease-out'\n                }}\n              >\n                {/* User Info Header */}\n                <div className=\"px-4 py-3 border-b border-primary-600/20 bg-gradient-to-r from-primary-600/10 to-primary-700/10\">\n                  <p className=\"text-sm font-semibold text-white\">{user?.name}</p>\n                  <p className=\"text-xs text-primary-300\">{user?.email}</p>\n                  <p className=\"text-xs text-gray-400 capitalize mt-1\">{user?.role}</p>\n                </div>\n\n                {/* Menu Items */}\n                <div className=\"py-1\">\n                  <button className=\"flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:text-white hover:bg-primary-600/20 transition-all duration-200 group\">\n                    <FiUser className=\"mr-3 h-4 w-4 group-hover:text-primary-300 transition-colors duration-200\" />\n                    <span>Profile</span>\n                  </button>\n\n                  <button className=\"flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:text-white hover:bg-primary-600/20 transition-all duration-200 group\">\n                    <FiSettings className=\"mr-3 h-4 w-4 group-hover:text-primary-300 transition-colors duration-200\" />\n                    <span>Settings</span>\n                  </button>\n                </div>\n\n                {/* Logout Section */}\n                <div className=\"border-t border-primary-600/20 py-1\">\n                  <button\n                    onClick={handleLogout}\n                    className=\"flex items-center w-full px-4 py-3 text-sm text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-200 group\"\n                  >\n                    <FiLogOut className=\"mr-3 h-4 w-4 group-hover:text-red-300 transition-colors duration-200\" />\n                    <span>Sign out</span>\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,YAAY,QAAQ,WAAW;AACxC,SACEC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,QAAQ,EACRC,aAAa,QACR,gBAAgB;AACvB,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClC,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMuB,WAAW,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMuB,SAAS,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC;IAAE2B,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC,CAAC;;EAE9E;EACA1B,SAAS,CAAC,MAAM;IACd,IAAImB,YAAY,IAAIG,SAAS,CAACK,OAAO,EAAE;MACrC,MAAMC,IAAI,GAAGN,SAAS,CAACK,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACtDL,mBAAmB,CAAC;QAClBC,GAAG,EAAEG,IAAI,CAACE,MAAM,GAAG,CAAC;QACpBJ,KAAK,EAAEK,MAAM,CAACC,UAAU,GAAGJ,IAAI,CAACF;MAClC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACP,YAAY,CAAC,CAAC;;EAElB;EACAnB,SAAS,CAAC,MAAM;IACd,MAAMiC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIb,WAAW,CAACM,OAAO,IAAI,CAACN,WAAW,CAACM,OAAO,CAACQ,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,IAClEd,SAAS,CAACK,OAAO,IAAI,CAACL,SAAS,CAACK,OAAO,CAACQ,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;QAClEhB,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IAED,IAAID,YAAY,EAAE;MAChBkB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;MAC1D,OAAO,MAAM;QACXI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC/D,CAAC;IACH;EACF,CAAC,EAAE,CAACd,YAAY,CAAC,CAAC;EAElB,MAAMqB,YAAY,GAAGA,CAAA,KAAM;IACzBtB,MAAM,CAAC,CAAC;IACRE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,oBACET,OAAA;IAAQ8B,SAAS,EAAC,4DAA4D;IAAAC,QAAA,eAC5E/B,OAAA;MAAK8B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D/B,OAAA;QAAK8B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC/B,OAAA;UACEgC,OAAO,EAAE9B,WAAY;UACrB4B,SAAS,EAAC,2IAA2I;UAAAC,QAAA,eAErJ/B,OAAA,CAACT,MAAM;YAACuC,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAETpC,OAAA;UAAK8B,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAE7E/B,OAAA;YAAK8B,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B/B,OAAA,CAACF,cAAc;cACbgC,SAAS,EAAC,qCAAqC;cAC/CO,UAAU,EAAE,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,IAAI,MAAK,OAAQ;cACnCC,YAAY,EAAGC,OAAO,IAAK;gBACzBC,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEF,OAAO,CAAC;cACjD;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNpC,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC/B,OAAA;cAAK8B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C/B,OAAA;gBAAI8B,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,EAAC;cAEvF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpC,OAAA;gBAAM8B,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,EAAC;cAAC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnFpC,OAAA;gBAAM8B,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,EAAC;cAEzH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNpC,OAAA;cAAG8B,SAAS,EAAC,sFAAsF;cAAAC,QAAA,EAAC;YAEpG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpC,OAAA;QAAK8B,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBAEjE/B,OAAA;UAAQ8B,SAAS,EAAC,0KAA0K;UAAAC,QAAA,gBAC1L/B,OAAA,CAACR,MAAM;YAACsC,SAAS,EAAC;UAAoC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDpC,OAAA;YAAM8B,SAAS,EAAC;UAA4E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpGpC,OAAA;YAAM8B,SAAS,EAAC;UAA2E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eAGTpC,OAAA;UAAK8B,SAAS,EAAC,UAAU;UAACa,GAAG,EAAEjC,WAAY;UAAAqB,QAAA,gBACzC/B,OAAA;YACEgC,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAAC,CAACD,YAAY,CAAE;YAC9CsB,SAAS,EAAC,8KAA8K;YAAAC,QAAA,gBAExL/B,OAAA;cAAK8B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C/B,OAAA;gBAAK8B,SAAS,EAAC,+IAA+I;gBAAAC,QAAA,eAC5J/B,OAAA;kBAAM8B,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAChEzB,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAEsC,IAAI,cAAAxC,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYyC,MAAM,CAAC,CAAC,CAAC,cAAAxC,iBAAA,uBAArBA,iBAAA,CAAuByC,WAAW,CAAC;gBAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNpC,OAAA;gBAAK8B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3D/B,OAAA;kBAAG8B,SAAS,EAAC,8FAA8F;kBAAAC,QAAA,EAAEzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5HpC,OAAA;kBAAG8B,SAAS,EAAC,6GAA6G;kBAAAC,QAAA,EAAEzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC;gBAAI;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpC,OAAA,CAACJ,aAAa;cAACkC,SAAS,EAAE,6CAA6CtB,YAAY,GAAG,YAAY,GAAG,EAAE;YAAG;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC,EAGR5B,YAAY,iBACXR,OAAA;YACE8B,SAAS,EAAC,mHAAmH;YAC7HiB,KAAK,EAAE;cACLC,MAAM,EAAE,KAAK;cACbC,SAAS,EAAE,2GAA2G;cACtHC,SAAS,EAAE;YACb,CAAE;YAAAnB,QAAA,gBAGF/B,OAAA;cAAK8B,SAAS,EAAC,iGAAiG;cAAAC,QAAA,gBAC9G/B,OAAA;gBAAG8B,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC;cAAI;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEpC,OAAA;gBAAG8B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAEzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C;cAAK;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzDpC,OAAA;gBAAG8B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eAGNpC,OAAA;cAAK8B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/B,OAAA;gBAAQ8B,SAAS,EAAC,qIAAqI;gBAAAC,QAAA,gBACrJ/B,OAAA,CAACP,MAAM;kBAACqC,SAAS,EAAC;gBAA0E;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/FpC,OAAA;kBAAA+B,QAAA,EAAM;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAETpC,OAAA;gBAAQ8B,SAAS,EAAC,qIAAqI;gBAAAC,QAAA,gBACrJ/B,OAAA,CAACN,UAAU;kBAACoC,SAAS,EAAC;gBAA0E;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnGpC,OAAA;kBAAA+B,QAAA,EAAM;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNpC,OAAA;cAAK8B,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAClD/B,OAAA;gBACEgC,OAAO,EAAEH,YAAa;gBACtBC,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,gBAE5I/B,OAAA,CAACL,QAAQ;kBAACmC,SAAS,EAAC;gBAAsE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7FpC,OAAA;kBAAA+B,QAAA,EAAM;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACjC,EAAA,CA9JIF,MAAM;EAAA,QACeJ,OAAO;AAAA;AAAAuD,EAAA,GAD5BnD,MAAM;AAgKZ,eAAeA,MAAM;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}