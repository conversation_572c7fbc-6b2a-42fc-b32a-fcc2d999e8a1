{"ast": null, "code": "export const mockData = {\n  students: [{\n    id: 'STU001',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+1234567890',\n    address: '123 Main St, City, State',\n    dateOfBirth: '2000-05-15',\n    enrollmentDate: '2022-09-01',\n    semester: 3,\n    department: 'Computer Science',\n    cgpa: 3.75,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'STU002',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+1234567891',\n    address: '456 Oak Ave, City, State',\n    dateOfBirth: '2001-03-22',\n    enrollmentDate: '2022-09-01',\n    semester: 3,\n    department: 'Business Administration',\n    cgpa: 3.92,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'STU003',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+1234567892',\n    address: '789 Pine St, City, State',\n    dateOfBirth: '1999-11-08',\n    enrollmentDate: '2021-09-01',\n    semester: 5,\n    department: 'Engineering',\n    cgpa: 3.45,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'STU004',\n    name: 'Hammad Ul Rehman',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+***********',\n    address: 'Block A, Model Town, Multan, Pakistan',\n    dateOfBirth: '2001-07-15',\n    enrollmentDate: '2022-09-01',\n    semester: 4,\n    department: 'Software Engineering',\n    cgpa: 3.85,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'STU005',\n    name: 'Ghulam Mustafa',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+92301234567',\n    address: 'Cantt Area, Multan, Pakistan',\n    dateOfBirth: '2000-12-03',\n    enrollmentDate: '2022-09-01',\n    semester: 4,\n    department: 'Software Engineering',\n    cgpa: 3.72,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'STU006',\n    name: 'Ahmar Ashraf',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+92302345678',\n    address: 'Shah Rukn-e-Alam Colony, Multan, Pakistan',\n    dateOfBirth: '2001-04-22',\n    enrollmentDate: '2022-09-01',\n    semester: 4,\n    department: 'Software Engineering',\n    cgpa: 3.68,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'STU007',\n    name: 'Saifullah',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+92303456789',\n    address: 'Gulgasht Colony, Multan, Pakistan',\n    dateOfBirth: '2001-09-10',\n    enrollmentDate: '2022-09-01',\n    semester: 4,\n    department: 'Software Engineering',\n    cgpa: 3.91,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'STU008',\n    name: 'Anus Sarfaraz',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+92304567890',\n    address: 'Bosan Road, Multan, Pakistan',\n    dateOfBirth: '2000-11-28',\n    enrollmentDate: '2022-09-01',\n    semester: 4,\n    department: 'Software Engineering',\n    cgpa: 3.79,\n    status: 'active',\n    profileImage: null\n  }],\n  faculty: [{\n    id: 'FAC001',\n    name: 'Dr. Sarah Wilson',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'faculty',\n    phone: '+1234567893',\n    address: '321 University Blvd, City, State',\n    department: 'Computer Science',\n    designation: 'Professor',\n    qualification: 'Ph.D. in Computer Science',\n    experience: 15,\n    specialization: 'Machine Learning, Data Science',\n    joinDate: '2010-08-15',\n    salary: 85000,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'FAC002',\n    name: 'Dr. Robert Brown',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'faculty',\n    phone: '+1234567894',\n    address: '654 Academic Way, City, State',\n    department: 'Business Administration',\n    designation: 'Associate Professor',\n    qualification: 'Ph.D. in Business Administration',\n    experience: 12,\n    specialization: 'Marketing, Strategic Management',\n    joinDate: '2012-01-10',\n    salary: 75000,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'FAC003',\n    name: 'Dr. Emily Davis',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'faculty',\n    phone: '+1234567895',\n    address: '987 Scholar Lane, City, State',\n    department: 'Engineering',\n    designation: 'Assistant Professor',\n    qualification: 'Ph.D. in Mechanical Engineering',\n    experience: 8,\n    specialization: 'Thermodynamics, Fluid Mechanics',\n    joinDate: '2016-09-01',\n    salary: 65000,\n    status: 'active',\n    profileImage: null\n  }],\n  courses: [{\n    id: 'CS101',\n    name: 'Introduction to Programming',\n    code: 'CS101',\n    department: 'Computer Science',\n    credits: 3,\n    semester: 1,\n    description: 'Basic programming concepts using Python',\n    facultyId: 'FAC001',\n    schedule: {\n      days: ['Monday', 'Wednesday', 'Friday'],\n      time: '09:00-10:00',\n      room: 'CS-101'\n    },\n    capacity: 50,\n    enrolled: 35,\n    status: 'active'\n  }, {\n    id: 'CS201',\n    name: 'Data Structures',\n    code: 'CS201',\n    department: 'Computer Science',\n    credits: 4,\n    semester: 3,\n    description: 'Advanced data structures and algorithms',\n    facultyId: 'FAC001',\n    schedule: {\n      days: ['Tuesday', 'Thursday'],\n      time: '10:00-12:00',\n      room: 'CS-201'\n    },\n    capacity: 40,\n    enrolled: 28,\n    status: 'active'\n  }, {\n    id: 'BUS101',\n    name: 'Business Fundamentals',\n    code: 'BUS101',\n    department: 'Business Administration',\n    credits: 3,\n    semester: 1,\n    description: 'Introduction to business concepts',\n    facultyId: 'FAC002',\n    schedule: {\n      days: ['Monday', 'Wednesday'],\n      time: '14:00-15:30',\n      room: 'BUS-101'\n    },\n    capacity: 60,\n    enrolled: 45,\n    status: 'active'\n  }, {\n    id: 'ENG101',\n    name: 'Engineering Mathematics',\n    code: 'ENG101',\n    department: 'Engineering',\n    credits: 4,\n    semester: 1,\n    description: 'Mathematical foundations for engineering',\n    facultyId: 'FAC003',\n    schedule: {\n      days: ['Tuesday', 'Thursday', 'Friday'],\n      time: '08:00-09:00',\n      room: 'ENG-101'\n    },\n    capacity: 45,\n    enrolled: 42,\n    status: 'active'\n  }, {\n    id: 'SE101',\n    name: 'Introduction to Software Engineering',\n    code: 'SE101',\n    department: 'Software Engineering',\n    credits: 3,\n    semester: 1,\n    description: 'Fundamentals of software engineering principles and practices',\n    facultyId: 'FAC001',\n    schedule: {\n      days: ['Monday', 'Wednesday', 'Friday'],\n      time: '11:00-12:00',\n      room: 'SE-101'\n    },\n    capacity: 40,\n    enrolled: 35,\n    status: 'active'\n  }, {\n    id: 'SE201',\n    name: 'Object Oriented Programming',\n    code: 'SE201',\n    department: 'Software Engineering',\n    credits: 4,\n    semester: 2,\n    description: 'Advanced programming concepts using Java and C++',\n    facultyId: 'FAC001',\n    schedule: {\n      days: ['Tuesday', 'Thursday'],\n      time: '13:00-15:00',\n      room: 'SE-201'\n    },\n    capacity: 35,\n    enrolled: 30,\n    status: 'active'\n  }, {\n    id: 'SE301',\n    name: 'Software Design Patterns',\n    code: 'SE301',\n    department: 'Software Engineering',\n    credits: 3,\n    semester: 3,\n    description: 'Design patterns and software architecture principles',\n    facultyId: 'FAC001',\n    schedule: {\n      days: ['Monday', 'Wednesday'],\n      time: '15:00-16:30',\n      room: 'SE-301'\n    },\n    capacity: 30,\n    enrolled: 25,\n    status: 'active'\n  }, {\n    id: 'SE401',\n    name: 'Software Project Management',\n    code: 'SE401',\n    department: 'Software Engineering',\n    credits: 3,\n    semester: 4,\n    description: 'Project management methodologies for software development',\n    facultyId: 'FAC001',\n    schedule: {\n      days: ['Tuesday', 'Friday'],\n      time: '10:00-11:30',\n      room: 'SE-401'\n    },\n    capacity: 25,\n    enrolled: 20,\n    status: 'active'\n  }],\n  enrollments: [{\n    id: 'ENR001',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    enrollmentDate: '2022-09-01',\n    status: 'enrolled',\n    grade: null\n  }, {\n    id: 'ENR002',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    enrollmentDate: '2023-01-15',\n    status: 'enrolled',\n    grade: null\n  }, {\n    id: 'ENR003',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    enrollmentDate: '2022-09-01',\n    status: 'enrolled',\n    grade: null\n  }, {\n    id: 'ENR004',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    enrollmentDate: '2021-09-01',\n    status: 'completed',\n    grade: 'B+'\n  },\n  // Software Engineering Students Enrollments\n  {\n    id: 'ENR005',\n    studentId: 'STU004',\n    courseId: 'SE101',\n    enrollmentDate: '2022-09-01',\n    status: 'completed',\n    grade: 'A-'\n  }, {\n    id: 'ENR006',\n    studentId: 'STU004',\n    courseId: 'SE201',\n    enrollmentDate: '2023-01-15',\n    status: 'completed',\n    grade: 'A'\n  }, {\n    id: 'ENR007',\n    studentId: 'STU004',\n    courseId: 'SE301',\n    enrollmentDate: '2023-09-01',\n    status: 'completed',\n    grade: 'A-'\n  }, {\n    id: 'ENR008',\n    studentId: 'STU004',\n    courseId: 'SE401',\n    enrollmentDate: '2024-01-15',\n    status: 'enrolled',\n    grade: null\n  }, {\n    id: 'ENR009',\n    studentId: 'STU005',\n    courseId: 'SE101',\n    enrollmentDate: '2022-09-01',\n    status: 'completed',\n    grade: 'B+'\n  }, {\n    id: 'ENR010',\n    studentId: 'STU005',\n    courseId: 'SE201',\n    enrollmentDate: '2023-01-15',\n    status: 'completed',\n    grade: 'A-'\n  }, {\n    id: 'ENR011',\n    studentId: 'STU005',\n    courseId: 'SE301',\n    enrollmentDate: '2023-09-01',\n    status: 'completed',\n    grade: 'B+'\n  }, {\n    id: 'ENR012',\n    studentId: 'STU005',\n    courseId: 'SE401',\n    enrollmentDate: '2024-01-15',\n    status: 'enrolled',\n    grade: null\n  }, {\n    id: 'ENR013',\n    studentId: 'STU006',\n    courseId: 'SE101',\n    enrollmentDate: '2022-09-01',\n    status: 'completed',\n    grade: 'B+'\n  }, {\n    id: 'ENR014',\n    studentId: 'STU006',\n    courseId: 'SE201',\n    enrollmentDate: '2023-01-15',\n    status: 'completed',\n    grade: 'B'\n  }, {\n    id: 'ENR015',\n    studentId: 'STU006',\n    courseId: 'SE301',\n    enrollmentDate: '2023-09-01',\n    status: 'completed',\n    grade: 'A-'\n  }, {\n    id: 'ENR016',\n    studentId: 'STU006',\n    courseId: 'SE401',\n    enrollmentDate: '2024-01-15',\n    status: 'enrolled',\n    grade: null\n  }, {\n    id: 'ENR017',\n    studentId: 'STU007',\n    courseId: 'SE101',\n    enrollmentDate: '2022-09-01',\n    status: 'completed',\n    grade: 'A'\n  }, {\n    id: 'ENR018',\n    studentId: 'STU007',\n    courseId: 'SE201',\n    enrollmentDate: '2023-01-15',\n    status: 'completed',\n    grade: 'A'\n  }, {\n    id: 'ENR019',\n    studentId: 'STU007',\n    courseId: 'SE301',\n    enrollmentDate: '2023-09-01',\n    status: 'completed',\n    grade: 'A'\n  }, {\n    id: 'ENR020',\n    studentId: 'STU007',\n    courseId: 'SE401',\n    enrollmentDate: '2024-01-15',\n    status: 'enrolled',\n    grade: null\n  }, {\n    id: 'ENR021',\n    studentId: 'STU008',\n    courseId: 'SE101',\n    enrollmentDate: '2022-09-01',\n    status: 'completed',\n    grade: 'A-'\n  }, {\n    id: 'ENR022',\n    studentId: 'STU008',\n    courseId: 'SE201',\n    enrollmentDate: '2023-01-15',\n    status: 'completed',\n    grade: 'B+'\n  }, {\n    id: 'ENR023',\n    studentId: 'STU008',\n    courseId: 'SE301',\n    enrollmentDate: '2023-09-01',\n    status: 'completed',\n    grade: 'A-'\n  }, {\n    id: 'ENR024',\n    studentId: 'STU008',\n    courseId: 'SE401',\n    enrollmentDate: '2024-01-15',\n    status: 'enrolled',\n    grade: null\n  }],\n  grades: [\n  // Student 1 (John Doe) - CS Student\n  {\n    id: 'GRD001',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    semester: 1,\n    year: 2022,\n    midterm: 85,\n    final: 88,\n    assignments: 92,\n    quizzes: 90,\n    participation: 95,\n    total: 88.5,\n    grade: 'A-',\n    gpa: 3.7,\n    status: 'completed'\n  }, {\n    id: 'GRD002',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    semester: 3,\n    year: 2023,\n    midterm: 92,\n    final: 89,\n    assignments: 95,\n    quizzes: 88,\n    participation: 92,\n    total: 91.2,\n    grade: 'A',\n    gpa: 4.0,\n    status: 'completed'\n  }, {\n    id: 'GRD003',\n    studentId: 'STU001',\n    courseId: 'ENG101',\n    semester: 2,\n    year: 2023,\n    midterm: 78,\n    final: 82,\n    assignments: 85,\n    quizzes: 80,\n    participation: 88,\n    total: 81.5,\n    grade: 'B+',\n    gpa: 3.3,\n    status: 'completed'\n  },\n  // Student 2 (Jane Smith) - Business Student\n  {\n    id: 'GRD004',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    semester: 1,\n    year: 2022,\n    midterm: 92,\n    final: 95,\n    assignments: 89,\n    quizzes: 94,\n    participation: 96,\n    total: 92.3,\n    grade: 'A',\n    gpa: 4.0,\n    status: 'completed'\n  }, {\n    id: 'GRD005',\n    studentId: 'STU002',\n    courseId: 'CS101',\n    semester: 2,\n    year: 2023,\n    midterm: 88,\n    final: 85,\n    assignments: 90,\n    quizzes: 87,\n    participation: 89,\n    total: 87.8,\n    grade: 'A-',\n    gpa: 3.7,\n    status: 'completed'\n  }, {\n    id: 'GRD006',\n    studentId: 'STU002',\n    courseId: 'ENG101',\n    semester: 3,\n    year: 2023,\n    midterm: 95,\n    final: 93,\n    assignments: 97,\n    quizzes: 92,\n    participation: 98,\n    total: 94.5,\n    grade: 'A',\n    gpa: 4.0,\n    status: 'completed'\n  },\n  // Student 3 (Mike Johnson) - Engineering Student\n  {\n    id: 'GRD007',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    semester: 1,\n    year: 2021,\n    midterm: 78,\n    final: 82,\n    assignments: 85,\n    quizzes: 75,\n    participation: 80,\n    total: 81.5,\n    grade: 'B+',\n    gpa: 3.3,\n    status: 'completed'\n  }, {\n    id: 'GRD008',\n    studentId: 'STU003',\n    courseId: 'CS101',\n    semester: 2,\n    year: 2022,\n    midterm: 72,\n    final: 75,\n    assignments: 78,\n    quizzes: 70,\n    participation: 85,\n    total: 74.8,\n    grade: 'B',\n    gpa: 3.0,\n    status: 'completed'\n  }, {\n    id: 'GRD009',\n    studentId: 'STU003',\n    courseId: 'CS201',\n    semester: 5,\n    year: 2024,\n    midterm: 85,\n    final: null,\n    assignments: 88,\n    quizzes: 82,\n    participation: 90,\n    total: null,\n    grade: null,\n    gpa: null,\n    status: 'in_progress'\n  },\n  // Software Engineering Students Grades\n  // Student 4 (Hammad Ul Rehman) - SE Student\n  {\n    id: 'GRD010',\n    studentId: 'STU004',\n    courseId: 'SE101',\n    semester: 1,\n    year: 2022,\n    midterm: 88,\n    final: 85,\n    assignments: 92,\n    quizzes: 87,\n    participation: 90,\n    total: 87.8,\n    grade: 'A-',\n    gpa: 3.7,\n    status: 'completed'\n  }, {\n    id: 'GRD011',\n    studentId: 'STU004',\n    courseId: 'SE201',\n    semester: 2,\n    year: 2023,\n    midterm: 92,\n    final: 89,\n    assignments: 95,\n    quizzes: 90,\n    participation: 94,\n    total: 91.5,\n    grade: 'A',\n    gpa: 4.0,\n    status: 'completed'\n  }, {\n    id: 'GRD012',\n    studentId: 'STU004',\n    courseId: 'SE301',\n    semester: 3,\n    year: 2023,\n    midterm: 85,\n    final: 88,\n    assignments: 90,\n    quizzes: 86,\n    participation: 92,\n    total: 87.8,\n    grade: 'A-',\n    gpa: 3.7,\n    status: 'completed'\n  },\n  // Student 5 (Ghulam Mustafa) - SE Student\n  {\n    id: 'GRD013',\n    studentId: 'STU005',\n    courseId: 'SE101',\n    semester: 1,\n    year: 2022,\n    midterm: 82,\n    final: 85,\n    assignments: 88,\n    quizzes: 80,\n    participation: 85,\n    total: 84.2,\n    grade: 'B+',\n    gpa: 3.3,\n    status: 'completed'\n  }, {\n    id: 'GRD014',\n    studentId: 'STU005',\n    courseId: 'SE201',\n    semester: 2,\n    year: 2023,\n    midterm: 88,\n    final: 86,\n    assignments: 90,\n    quizzes: 85,\n    participation: 89,\n    total: 87.5,\n    grade: 'A-',\n    gpa: 3.7,\n    status: 'completed'\n  }, {\n    id: 'GRD015',\n    studentId: 'STU005',\n    courseId: 'SE301',\n    semester: 3,\n    year: 2023,\n    midterm: 80,\n    final: 83,\n    assignments: 85,\n    quizzes: 78,\n    participation: 88,\n    total: 82.8,\n    grade: 'B+',\n    gpa: 3.3,\n    status: 'completed'\n  },\n  // Student 6 (Ahmar Ashraf) - SE Student\n  {\n    id: 'GRD016',\n    studentId: 'STU006',\n    courseId: 'SE101',\n    semester: 1,\n    year: 2022,\n    midterm: 80,\n    final: 82,\n    assignments: 85,\n    quizzes: 78,\n    participation: 83,\n    total: 81.8,\n    grade: 'B+',\n    gpa: 3.3,\n    status: 'completed'\n  }, {\n    id: 'GRD017',\n    studentId: 'STU006',\n    courseId: 'SE201',\n    semester: 2,\n    year: 2023,\n    midterm: 75,\n    final: 78,\n    assignments: 82,\n    quizzes: 76,\n    participation: 80,\n    total: 78.2,\n    grade: 'B',\n    gpa: 3.0,\n    status: 'completed'\n  }, {\n    id: 'GRD018',\n    studentId: 'STU006',\n    courseId: 'SE301',\n    semester: 3,\n    year: 2023,\n    midterm: 88,\n    final: 85,\n    assignments: 90,\n    quizzes: 87,\n    participation: 89,\n    total: 87.5,\n    grade: 'A-',\n    gpa: 3.7,\n    status: 'completed'\n  },\n  // Student 7 (Saifullah) - SE Student\n  {\n    id: 'GRD019',\n    studentId: 'STU007',\n    courseId: 'SE101',\n    semester: 1,\n    year: 2022,\n    midterm: 95,\n    final: 92,\n    assignments: 96,\n    quizzes: 94,\n    participation: 98,\n    total: 94.2,\n    grade: 'A',\n    gpa: 4.0,\n    status: 'completed'\n  }, {\n    id: 'GRD020',\n    studentId: 'STU007',\n    courseId: 'SE201',\n    semester: 2,\n    year: 2023,\n    midterm: 93,\n    final: 95,\n    assignments: 97,\n    quizzes: 92,\n    participation: 96,\n    total: 94.5,\n    grade: 'A',\n    gpa: 4.0,\n    status: 'completed'\n  }, {\n    id: 'GRD021',\n    studentId: 'STU007',\n    courseId: 'SE301',\n    semester: 3,\n    year: 2023,\n    midterm: 90,\n    final: 93,\n    assignments: 95,\n    quizzes: 89,\n    participation: 94,\n    total: 92.2,\n    grade: 'A',\n    gpa: 4.0,\n    status: 'completed'\n  },\n  // Student 8 (Anus Sarfaraz) - SE Student\n  {\n    id: 'GRD022',\n    studentId: 'STU008',\n    courseId: 'SE101',\n    semester: 1,\n    year: 2022,\n    midterm: 85,\n    final: 88,\n    assignments: 90,\n    quizzes: 84,\n    participation: 87,\n    total: 86.8,\n    grade: 'A-',\n    gpa: 3.7,\n    status: 'completed'\n  }, {\n    id: 'GRD023',\n    studentId: 'STU008',\n    courseId: 'SE201',\n    semester: 2,\n    year: 2023,\n    midterm: 82,\n    final: 85,\n    assignments: 88,\n    quizzes: 80,\n    participation: 86,\n    total: 84.2,\n    grade: 'B+',\n    gpa: 3.3,\n    status: 'completed'\n  }, {\n    id: 'GRD024',\n    studentId: 'STU008',\n    courseId: 'SE301',\n    semester: 3,\n    year: 2023,\n    midterm: 88,\n    final: 86,\n    assignments: 92,\n    quizzes: 85,\n    participation: 90,\n    total: 87.8,\n    grade: 'A-',\n    gpa: 3.7,\n    status: 'completed'\n  }],\n  attendance: [\n  // CS101 Attendance - January 2024\n  {\n    id: 'ATT001',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-08',\n    status: 'present',\n    time: '09:00'\n  }, {\n    id: 'ATT002',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-10',\n    status: 'present',\n    time: '09:00'\n  }, {\n    id: 'ATT003',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-12',\n    status: 'present',\n    time: '09:00'\n  }, {\n    id: 'ATT004',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-15',\n    status: 'present',\n    time: '09:00'\n  }, {\n    id: 'ATT005',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-17',\n    status: 'absent',\n    time: '09:00'\n  }, {\n    id: 'ATT006',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-19',\n    status: 'present',\n    time: '09:00'\n  }, {\n    id: 'ATT007',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-22',\n    status: 'present',\n    time: '09:00'\n  }, {\n    id: 'ATT008',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-24',\n    status: 'late',\n    time: '09:15'\n  }, {\n    id: 'ATT009',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-26',\n    status: 'present',\n    time: '09:00'\n  }, {\n    id: 'ATT010',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-29',\n    status: 'present',\n    time: '09:00'\n  },\n  // CS201 Attendance for STU001\n  {\n    id: 'ATT011',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    date: '2024-01-09',\n    status: 'present',\n    time: '10:00'\n  }, {\n    id: 'ATT012',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    date: '2024-01-11',\n    status: 'present',\n    time: '10:00'\n  }, {\n    id: 'ATT013',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    date: '2024-01-16',\n    status: 'present',\n    time: '10:00'\n  }, {\n    id: 'ATT014',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    date: '2024-01-18',\n    status: 'present',\n    time: '10:00'\n  }, {\n    id: 'ATT015',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    date: '2024-01-23',\n    status: 'absent',\n    time: '10:00'\n  }, {\n    id: 'ATT016',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    date: '2024-01-25',\n    status: 'present',\n    time: '10:00'\n  }, {\n    id: 'ATT017',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    date: '2024-01-30',\n    status: 'present',\n    time: '10:00'\n  },\n  // BUS101 Attendance for STU002\n  {\n    id: 'ATT018',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    date: '2024-01-08',\n    status: 'present',\n    time: '14:00'\n  }, {\n    id: 'ATT019',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    date: '2024-01-10',\n    status: 'present',\n    time: '14:00'\n  }, {\n    id: 'ATT020',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    date: '2024-01-15',\n    status: 'present',\n    time: '14:00'\n  }, {\n    id: 'ATT021',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    date: '2024-01-17',\n    status: 'present',\n    time: '14:00'\n  }, {\n    id: 'ATT022',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    date: '2024-01-22',\n    status: 'present',\n    time: '14:00'\n  }, {\n    id: 'ATT023',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    date: '2024-01-24',\n    status: 'present',\n    time: '14:00'\n  }, {\n    id: 'ATT024',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    date: '2024-01-29',\n    status: 'present',\n    time: '14:00'\n  }, {\n    id: 'ATT025',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    date: '2024-01-31',\n    status: 'present',\n    time: '14:00'\n  },\n  // ENG101 Attendance for STU003\n  {\n    id: 'ATT026',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    date: '2024-01-09',\n    status: 'present',\n    time: '08:00'\n  }, {\n    id: 'ATT027',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    date: '2024-01-11',\n    status: 'late',\n    time: '08:20'\n  }, {\n    id: 'ATT028',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    date: '2024-01-12',\n    status: 'present',\n    time: '08:00'\n  }, {\n    id: 'ATT029',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    date: '2024-01-16',\n    status: 'absent',\n    time: '08:00'\n  }, {\n    id: 'ATT030',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    date: '2024-01-18',\n    status: 'present',\n    time: '08:00'\n  }, {\n    id: 'ATT031',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    date: '2024-01-19',\n    status: 'present',\n    time: '08:00'\n  }, {\n    id: 'ATT032',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    date: '2024-01-23',\n    status: 'absent',\n    time: '08:00'\n  }, {\n    id: 'ATT033',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    date: '2024-01-25',\n    status: 'present',\n    time: '08:00'\n  }, {\n    id: 'ATT034',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    date: '2024-01-26',\n    status: 'late',\n    time: '08:15'\n  }, {\n    id: 'ATT035',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    date: '2024-01-30',\n    status: 'present',\n    time: '08:00'\n  },\n  // Cross-enrollment attendance\n  {\n    id: 'ATT036',\n    studentId: 'STU002',\n    courseId: 'CS101',\n    date: '2024-01-08',\n    status: 'present',\n    time: '09:00'\n  }, {\n    id: 'ATT037',\n    studentId: 'STU002',\n    courseId: 'CS101',\n    date: '2024-01-10',\n    status: 'present',\n    time: '09:00'\n  }, {\n    id: 'ATT038',\n    studentId: 'STU002',\n    courseId: 'CS101',\n    date: '2024-01-12',\n    status: 'absent',\n    time: '09:00'\n  }, {\n    id: 'ATT039',\n    studentId: 'STU002',\n    courseId: 'CS101',\n    date: '2024-01-15',\n    status: 'present',\n    time: '09:00'\n  }, {\n    id: 'ATT040',\n    studentId: 'STU002',\n    courseId: 'CS101',\n    date: '2024-01-17',\n    status: 'present',\n    time: '09:00'\n  }],\n  fees: [\n  // Student 1 (John Doe) - CS Student Fee Records\n  {\n    id: 'FEE001',\n    studentId: 'STU001',\n    semester: 1,\n    year: 2022,\n    tuitionFee: 4500,\n    libraryFee: 150,\n    labFee: 250,\n    sportsFee: 100,\n    activityFee: 75,\n    technologyFee: 200,\n    otherFees: 50,\n    totalAmount: 5325,\n    paidAmount: 5325,\n    dueAmount: 0,\n    dueDate: '2022-08-31',\n    paymentDate: '2022-08-20',\n    status: 'paid',\n    paymentMethod: 'bank_transfer',\n    transactionId: 'TXN001'\n  }, {\n    id: 'FEE002',\n    studentId: 'STU001',\n    semester: 2,\n    year: 2023,\n    tuitionFee: 4800,\n    libraryFee: 150,\n    labFee: 300,\n    sportsFee: 100,\n    activityFee: 75,\n    technologyFee: 200,\n    otherFees: 50,\n    totalAmount: 5675,\n    paidAmount: 5675,\n    dueAmount: 0,\n    dueDate: '2023-01-31',\n    paymentDate: '2023-01-25',\n    status: 'paid',\n    paymentMethod: 'credit_card',\n    transactionId: 'TXN002'\n  }, {\n    id: 'FEE003',\n    studentId: 'STU001',\n    semester: 3,\n    year: 2024,\n    tuitionFee: 5000,\n    libraryFee: 200,\n    labFee: 300,\n    sportsFee: 100,\n    activityFee: 75,\n    technologyFee: 250,\n    otherFees: 75,\n    totalAmount: 6000,\n    paidAmount: 6000,\n    dueAmount: 0,\n    dueDate: '2024-01-31',\n    paymentDate: '2024-01-15',\n    status: 'paid',\n    paymentMethod: 'bank_transfer',\n    transactionId: 'TXN003'\n  },\n  // Student 2 (Jane Smith) - Business Student Fee Records\n  {\n    id: 'FEE004',\n    studentId: 'STU002',\n    semester: 1,\n    year: 2022,\n    tuitionFee: 4200,\n    libraryFee: 150,\n    labFee: 200,\n    sportsFee: 100,\n    activityFee: 75,\n    technologyFee: 150,\n    otherFees: 50,\n    totalAmount: 4925,\n    paidAmount: 4925,\n    dueAmount: 0,\n    dueDate: '2022-08-31',\n    paymentDate: '2022-08-28',\n    status: 'paid',\n    paymentMethod: 'bank_transfer',\n    transactionId: 'TXN004'\n  }, {\n    id: 'FEE005',\n    studentId: 'STU002',\n    semester: 2,\n    year: 2023,\n    tuitionFee: 4400,\n    libraryFee: 150,\n    labFee: 200,\n    sportsFee: 100,\n    activityFee: 75,\n    technologyFee: 175,\n    otherFees: 50,\n    totalAmount: 5150,\n    paidAmount: 5150,\n    dueAmount: 0,\n    dueDate: '2023-01-31',\n    paymentDate: '2023-01-20',\n    status: 'paid',\n    paymentMethod: 'debit_card',\n    transactionId: 'TXN005'\n  }, {\n    id: 'FEE006',\n    studentId: 'STU002',\n    semester: 3,\n    year: 2024,\n    tuitionFee: 4800,\n    libraryFee: 200,\n    labFee: 250,\n    sportsFee: 100,\n    activityFee: 75,\n    technologyFee: 200,\n    otherFees: 75,\n    totalAmount: 5700,\n    paidAmount: 2000,\n    dueAmount: 3700,\n    dueDate: '2024-02-15',\n    paymentDate: '2024-01-10',\n    status: 'partial',\n    paymentMethod: 'bank_transfer',\n    transactionId: 'TXN006'\n  },\n  // Student 3 (Mike Johnson) - Engineering Student Fee Records\n  {\n    id: 'FEE007',\n    studentId: 'STU003',\n    semester: 1,\n    year: 2021,\n    tuitionFee: 5200,\n    libraryFee: 150,\n    labFee: 400,\n    sportsFee: 100,\n    activityFee: 75,\n    technologyFee: 300,\n    otherFees: 75,\n    totalAmount: 6300,\n    paidAmount: 6300,\n    dueAmount: 0,\n    dueDate: '2021-08-31',\n    paymentDate: '2021-08-25',\n    status: 'paid',\n    paymentMethod: 'bank_transfer',\n    transactionId: 'TXN007'\n  }, {\n    id: 'FEE008',\n    studentId: 'STU003',\n    semester: 4,\n    year: 2023,\n    tuitionFee: 5500,\n    libraryFee: 200,\n    labFee: 450,\n    sportsFee: 100,\n    activityFee: 75,\n    technologyFee: 350,\n    otherFees: 75,\n    totalAmount: 6750,\n    paidAmount: 4000,\n    dueAmount: 2750,\n    dueDate: '2024-02-28',\n    paymentDate: '2023-12-15',\n    status: 'partial',\n    paymentMethod: 'installment',\n    transactionId: 'TXN008'\n  }, {\n    id: 'FEE009',\n    studentId: 'STU003',\n    semester: 5,\n    year: 2024,\n    tuitionFee: 5800,\n    libraryFee: 200,\n    labFee: 500,\n    sportsFee: 100,\n    activityFee: 75,\n    technologyFee: 400,\n    otherFees: 100,\n    totalAmount: 7175,\n    paidAmount: 0,\n    dueAmount: 7175,\n    dueDate: '2024-03-15',\n    paymentDate: null,\n    status: 'unpaid',\n    paymentMethod: null,\n    transactionId: null\n  }],\n  books: [{\n    id: 'BOOK001',\n    title: 'Introduction to Algorithms',\n    author: 'Thomas H. Cormen',\n    isbn: '978-0262033848',\n    category: 'Computer Science',\n    publisher: 'MIT Press',\n    edition: '3rd',\n    totalCopies: 10,\n    availableCopies: 7,\n    location: 'CS Section - Shelf A1',\n    status: 'available'\n  }, {\n    id: 'BOOK002',\n    title: 'Clean Code',\n    author: 'Robert C. Martin',\n    isbn: '978-0132350884',\n    category: 'Computer Science',\n    publisher: 'Prentice Hall',\n    edition: '1st',\n    totalCopies: 8,\n    availableCopies: 5,\n    location: 'CS Section - Shelf A2',\n    status: 'available'\n  }, {\n    id: 'BOOK003',\n    title: 'Marketing Management',\n    author: 'Philip Kotler',\n    isbn: '978-0134236933',\n    category: 'Business',\n    publisher: 'Pearson',\n    edition: '15th',\n    totalCopies: 12,\n    availableCopies: 9,\n    location: 'Business Section - Shelf B1',\n    status: 'available'\n  }, {\n    id: 'BOOK004',\n    title: 'Data Structures and Algorithms in Java',\n    author: 'Robert Lafore',\n    isbn: '978-**********',\n    category: 'Computer Science',\n    publisher: 'Sams Publishing',\n    edition: '2nd',\n    totalCopies: 15,\n    availableCopies: 12,\n    location: 'CS Section - Shelf A3',\n    status: 'available'\n  }, {\n    id: 'BOOK005',\n    title: 'Financial Accounting',\n    author: 'Jerry J. Weygandt',\n    isbn: '978-**********',\n    category: 'Business',\n    publisher: 'Wiley',\n    edition: '10th',\n    totalCopies: 10,\n    availableCopies: 6,\n    location: 'Business Section - Shelf B2',\n    status: 'available'\n  }, {\n    id: 'BOOK006',\n    title: 'English Literature: A Survey',\n    author: 'William J. Long',\n    isbn: '978-**********',\n    category: 'Literature',\n    publisher: 'Kalyani Publishers',\n    edition: '1st',\n    totalCopies: 8,\n    availableCopies: 5,\n    location: 'Literature Section - Shelf C1',\n    status: 'available'\n  }, {\n    id: 'BOOK007',\n    title: 'Engineering Mathematics',\n    author: 'K.A. Stroud',\n    isbn: '978-**********',\n    category: 'Engineering',\n    publisher: 'Palgrave Macmillan',\n    edition: '7th',\n    totalCopies: 20,\n    availableCopies: 16,\n    location: 'Engineering Section - Shelf D1',\n    status: 'available'\n  }, {\n    id: 'BOOK008',\n    title: 'Physics for Scientists and Engineers',\n    author: 'Raymond A. Serway',\n    isbn: '978-1133947271',\n    category: 'Science',\n    publisher: 'Cengage Learning',\n    edition: '9th',\n    totalCopies: 18,\n    availableCopies: 14,\n    location: 'Science Section - Shelf E1',\n    status: 'available'\n  }, {\n    id: 'BOOK009',\n    title: 'Organic Chemistry',\n    author: 'Paula Yurkanis Bruice',\n    isbn: '978-0134042282',\n    category: 'Science',\n    publisher: 'Pearson',\n    edition: '8th',\n    totalCopies: 12,\n    availableCopies: 8,\n    location: 'Science Section - Shelf E2',\n    status: 'available'\n  }, {\n    id: 'BOOK010',\n    title: 'Database System Concepts',\n    author: 'Abraham Silberschatz',\n    isbn: '978-0078022159',\n    category: 'Computer Science',\n    publisher: 'McGraw-Hill Education',\n    edition: '7th',\n    totalCopies: 14,\n    availableCopies: 10,\n    location: 'CS Section - Shelf A4',\n    status: 'available'\n  }, {\n    id: 'BOOK011',\n    title: 'Principles of Economics',\n    author: 'N. Gregory Mankiw',\n    isbn: '978-1305585126',\n    category: 'Business',\n    publisher: 'Cengage Learning',\n    edition: '8th',\n    totalCopies: 16,\n    availableCopies: 13,\n    location: 'Business Section - Shelf B3',\n    status: 'available'\n  }, {\n    id: 'BOOK012',\n    title: 'Creative Writing: Four Genres in Brief',\n    author: 'David Starkey',\n    isbn: '978-1319035334',\n    category: 'Literature',\n    publisher: 'Bedford/St. Martins',\n    edition: '3rd',\n    totalCopies: 6,\n    availableCopies: 4,\n    location: 'Literature Section - Shelf C2',\n    status: 'available'\n  },\n  // Programming Books for Software Engineering Students\n  {\n    id: 'BOOK013',\n    title: 'Fundamentals of Programming',\n    author: 'Kenneth Lambert',\n    isbn: '978-1337109635',\n    category: 'Programming',\n    publisher: 'Cengage Learning',\n    edition: '4th',\n    totalCopies: 15,\n    availableCopies: 12,\n    location: 'Programming Section - Shelf P1',\n    status: 'available'\n  }, {\n    id: 'BOOK014',\n    title: 'Object-Oriented Programming with C++',\n    author: 'E. Balagurusamy',\n    isbn: '978-0070634381',\n    category: 'Programming',\n    publisher: 'McGraw Hill Education',\n    edition: '8th',\n    totalCopies: 20,\n    availableCopies: 16,\n    location: 'Programming Section - Shelf P2',\n    status: 'available'\n  }, {\n    id: 'BOOK015',\n    title: 'The C Programming Language',\n    author: 'Brian W. Kernighan',\n    isbn: '978-0131103627',\n    category: 'Programming',\n    publisher: 'Prentice Hall',\n    edition: '2nd',\n    totalCopies: 18,\n    availableCopies: 14,\n    location: 'Programming Section - Shelf P3',\n    status: 'available'\n  }, {\n    id: 'BOOK016',\n    title: 'JavaScript: The Definitive Guide',\n    author: 'David Flanagan',\n    isbn: '978-**********',\n    category: 'Web Development',\n    publisher: \"O'Reilly Media\",\n    edition: '7th',\n    totalCopies: 12,\n    availableCopies: 9,\n    location: 'Web Dev Section - Shelf W1',\n    status: 'available'\n  }, {\n    id: 'BOOK017',\n    title: 'Learning React: Modern Patterns',\n    author: 'Alex Banks',\n    isbn: '978-**********',\n    category: 'Web Development',\n    publisher: \"O'Reilly Media\",\n    edition: '2nd',\n    totalCopies: 10,\n    availableCopies: 7,\n    location: 'Web Dev Section - Shelf W2',\n    status: 'available'\n  }, {\n    id: 'BOOK018',\n    title: 'Python Programming: An Introduction',\n    author: 'John Zelle',\n    isbn: '978-**********',\n    category: 'Programming',\n    publisher: 'Franklin Beedle',\n    edition: '3rd',\n    totalCopies: 16,\n    availableCopies: 13,\n    location: 'Programming Section - Shelf P4',\n    status: 'available'\n  }, {\n    id: 'BOOK019',\n    title: 'Java: The Complete Reference',\n    author: 'Herbert Schildt',\n    isbn: '978-**********',\n    category: 'Programming',\n    publisher: 'McGraw-Hill Education',\n    edition: '12th',\n    totalCopies: 14,\n    availableCopies: 11,\n    location: 'Programming Section - Shelf P5',\n    status: 'available'\n  }, {\n    id: 'BOOK020',\n    title: 'Software Engineering: A Practitioner\\'s Approach',\n    author: 'Roger Pressman',\n    isbn: '978-0078022128',\n    category: 'Software Engineering',\n    publisher: 'McGraw-Hill Education',\n    edition: '8th',\n    totalCopies: 12,\n    availableCopies: 8,\n    location: 'SE Section - Shelf SE1',\n    status: 'available'\n  }],\n  borrowedBooks: [\n  // Currently Borrowed Books\n  {\n    id: 'BOR001',\n    studentId: 'STU001',\n    bookId: 'BOOK001',\n    borrowDate: '2024-01-10',\n    dueDate: '2024-02-10',\n    returnDate: null,\n    status: 'borrowed',\n    fine: 0,\n    renewalCount: 0,\n    maxRenewals: 2\n  }, {\n    id: 'BOR002',\n    studentId: 'STU001',\n    bookId: 'BOOK004',\n    borrowDate: '2024-01-15',\n    dueDate: '2024-02-15',\n    returnDate: null,\n    status: 'borrowed',\n    fine: 0,\n    renewalCount: 1,\n    maxRenewals: 2\n  }, {\n    id: 'BOR003',\n    studentId: 'STU002',\n    bookId: 'BOOK002',\n    borrowDate: '2024-01-05',\n    dueDate: '2024-02-05',\n    returnDate: null,\n    status: 'overdue',\n    fine: 15,\n    renewalCount: 0,\n    maxRenewals: 2\n  }, {\n    id: 'BOR004',\n    studentId: 'STU002',\n    bookId: 'BOOK005',\n    borrowDate: '2024-01-20',\n    dueDate: '2024-02-20',\n    returnDate: null,\n    status: 'borrowed',\n    fine: 0,\n    renewalCount: 0,\n    maxRenewals: 2\n  }, {\n    id: 'BOR005',\n    studentId: 'STU003',\n    bookId: 'BOOK006',\n    borrowDate: '2024-01-12',\n    dueDate: '2024-02-12',\n    returnDate: null,\n    status: 'borrowed',\n    fine: 0,\n    renewalCount: 0,\n    maxRenewals: 2\n  }, {\n    id: 'BOR006',\n    studentId: 'STU003',\n    bookId: 'BOOK007',\n    borrowDate: '2024-01-08',\n    dueDate: '2024-02-08',\n    returnDate: null,\n    status: 'overdue',\n    fine: 25,\n    renewalCount: 2,\n    maxRenewals: 2\n  },\n  // Previously Returned Books\n  {\n    id: 'BOR007',\n    studentId: 'STU001',\n    bookId: 'BOOK003',\n    borrowDate: '2023-12-01',\n    dueDate: '2024-01-01',\n    returnDate: '2023-12-28',\n    status: 'returned',\n    fine: 0,\n    renewalCount: 0,\n    maxRenewals: 2\n  }, {\n    id: 'BOR008',\n    studentId: 'STU002',\n    bookId: 'BOOK008',\n    borrowDate: '2023-11-15',\n    dueDate: '2023-12-15',\n    returnDate: '2023-12-20',\n    status: 'returned',\n    fine: 10,\n    renewalCount: 1,\n    maxRenewals: 2\n  }, {\n    id: 'BOR009',\n    studentId: 'STU003',\n    bookId: 'BOOK009',\n    borrowDate: '2023-10-20',\n    dueDate: '2023-11-20',\n    returnDate: '2023-11-18',\n    status: 'returned',\n    fine: 0,\n    renewalCount: 0,\n    maxRenewals: 2\n  }],\n  exams: [\n  // Upcoming Exams\n  {\n    id: 'EXM001',\n    courseId: 'CS101',\n    name: 'Midterm Examination',\n    type: 'midterm',\n    date: '2024-02-15',\n    time: '09:00-12:00',\n    room: 'Exam Hall 1',\n    duration: 180,\n    totalMarks: 100,\n    status: 'scheduled',\n    instructions: 'Bring calculator and ID card. No mobile phones allowed.',\n    syllabus: 'Chapters 1-5: Programming Fundamentals, Data Types, Control Structures'\n  }, {\n    id: 'EXM002',\n    courseId: 'CS101',\n    name: 'Final Examination',\n    type: 'final',\n    date: '2024-05-20',\n    time: '09:00-12:00',\n    room: 'Exam Hall 1',\n    duration: 180,\n    totalMarks: 150,\n    status: 'scheduled',\n    instructions: 'Comprehensive exam covering all course material. Bring calculator and ID card.',\n    syllabus: 'All chapters: Programming, Data Structures, Algorithms, Object-Oriented Programming'\n  }, {\n    id: 'EXM003',\n    courseId: 'CS201',\n    name: 'Quiz 1',\n    type: 'quiz',\n    date: '2024-02-08',\n    time: '10:00-10:30',\n    room: 'Room 201',\n    duration: 30,\n    totalMarks: 25,\n    status: 'scheduled',\n    instructions: 'Short quiz on recent topics. No materials allowed.',\n    syllabus: 'Advanced Data Structures: Trees and Graphs'\n  }, {\n    id: 'EXM004',\n    courseId: 'CS201',\n    name: 'Midterm Examination',\n    type: 'midterm',\n    date: '2024-03-10',\n    time: '14:00-17:00',\n    room: 'Exam Hall 2',\n    duration: 180,\n    totalMarks: 100,\n    status: 'scheduled',\n    instructions: 'Bring calculator and ID card. Programming questions included.',\n    syllabus: 'Chapters 1-6: Advanced Data Structures, Algorithm Analysis, Sorting'\n  }, {\n    id: 'EXM005',\n    courseId: 'BUS101',\n    name: 'Midterm Examination',\n    type: 'midterm',\n    date: '2024-02-20',\n    time: '14:00-16:30',\n    room: 'Business Hall A',\n    duration: 150,\n    totalMarks: 100,\n    status: 'scheduled',\n    instructions: 'Case study analysis. Bring calculator.',\n    syllabus: 'Business Fundamentals, Management Principles, Organizational Behavior'\n  }, {\n    id: 'EXM006',\n    courseId: 'BUS101',\n    name: 'Final Examination',\n    type: 'final',\n    date: '2024-05-25',\n    time: '14:00-17:00',\n    room: 'Business Hall A',\n    duration: 180,\n    totalMarks: 150,\n    status: 'scheduled',\n    instructions: 'Comprehensive business exam with case studies.',\n    syllabus: 'All course material: Management, Marketing, Finance, Operations'\n  }, {\n    id: 'EXM007',\n    courseId: 'ENG101',\n    name: 'Essay Examination',\n    type: 'midterm',\n    date: '2024-02-25',\n    time: '08:00-11:00',\n    room: 'Literature Hall',\n    duration: 180,\n    totalMarks: 100,\n    status: 'scheduled',\n    instructions: 'Essay writing exam. Bring pen and paper only.',\n    syllabus: 'Literary Analysis, Essay Writing, Grammar and Composition'\n  }, {\n    id: 'EXM008',\n    courseId: 'ENG101',\n    name: 'Final Examination',\n    type: 'final',\n    date: '2024-05-30',\n    time: '08:00-11:00',\n    room: 'Literature Hall',\n    duration: 180,\n    totalMarks: 150,\n    status: 'scheduled',\n    instructions: 'Comprehensive literature and writing exam.',\n    syllabus: 'Complete course: Literature, Writing, Critical Analysis, Research Methods'\n  },\n  // Current/Ongoing Exams\n  {\n    id: 'EXM009',\n    courseId: 'CS101',\n    name: 'Assignment 1 Submission',\n    type: 'assignment',\n    date: '2024-02-05',\n    time: '23:59',\n    room: 'Online Submission',\n    duration: 0,\n    totalMarks: 50,\n    status: 'ongoing',\n    instructions: 'Submit programming assignment via online portal.',\n    syllabus: 'Basic Programming Concepts and Problem Solving'\n  },\n  // Completed Exams\n  {\n    id: 'EXM010',\n    courseId: 'CS101',\n    name: 'Quiz 1',\n    type: 'quiz',\n    date: '2024-01-20',\n    time: '09:00-09:30',\n    room: 'Room 101',\n    duration: 30,\n    totalMarks: 25,\n    status: 'completed',\n    instructions: 'Basic programming concepts quiz.',\n    syllabus: 'Introduction to Programming, Variables, Basic Operations'\n  }, {\n    id: 'EXM011',\n    courseId: 'BUS101',\n    name: 'Quiz 1',\n    type: 'quiz',\n    date: '2024-01-25',\n    time: '14:00-14:30',\n    room: 'Business Room 1',\n    duration: 30,\n    totalMarks: 25,\n    status: 'completed',\n    instructions: 'Business fundamentals quiz.',\n    syllabus: 'Introduction to Business, Basic Management Concepts'\n  }, {\n    id: 'EXM012',\n    courseId: 'ENG101',\n    name: 'Essay Assignment 1',\n    type: 'assignment',\n    date: '2024-01-30',\n    time: '23:59',\n    room: 'Online Submission',\n    duration: 0,\n    totalMarks: 40,\n    status: 'completed',\n    instructions: 'Submit 1000-word essay on assigned topic.',\n    syllabus: 'Essay Structure, Thesis Development, Academic Writing'\n  },\n  // Completed Exams with Results\n  {\n    id: 'EXM013',\n    courseId: 'SE101',\n    name: 'Introduction to SE - Final Exam',\n    type: 'final',\n    date: '2023-12-15',\n    time: '09:00-12:00',\n    duration: 180,\n    room: 'SE-101',\n    totalMarks: 100,\n    instructions: 'Answer all questions. Use of calculators allowed.',\n    syllabus: 'Complete SE fundamentals, SDLC, methodologies',\n    status: 'completed',\n    results: [{\n      studentId: 'STU004',\n      marks: 88,\n      grade: 'A-',\n      percentage: 88\n    }, {\n      studentId: 'STU005',\n      marks: 82,\n      grade: 'B+',\n      percentage: 82\n    }, {\n      studentId: 'STU006',\n      marks: 80,\n      grade: 'B+',\n      percentage: 80\n    }, {\n      studentId: 'STU007',\n      marks: 95,\n      grade: 'A',\n      percentage: 95\n    }, {\n      studentId: 'STU008',\n      marks: 85,\n      grade: 'A-',\n      percentage: 85\n    }]\n  }, {\n    id: 'EXM014',\n    courseId: 'SE201',\n    name: 'OOP Midterm Examination',\n    type: 'midterm',\n    date: '2024-03-10',\n    time: '10:00-12:00',\n    duration: 120,\n    room: 'SE-201',\n    totalMarks: 50,\n    instructions: 'Write code solutions for OOP problems.',\n    syllabus: 'Classes, Objects, Inheritance, Polymorphism',\n    status: 'completed',\n    results: [{\n      studentId: 'STU004',\n      marks: 46,\n      grade: 'A',\n      percentage: 92\n    }, {\n      studentId: 'STU005',\n      marks: 44,\n      grade: 'A-',\n      percentage: 88\n    }, {\n      studentId: 'STU006',\n      marks: 38,\n      grade: 'B',\n      percentage: 76\n    }, {\n      studentId: 'STU007',\n      marks: 47,\n      grade: 'A',\n      percentage: 94\n    }, {\n      studentId: 'STU008',\n      marks: 41,\n      grade: 'B+',\n      percentage: 82\n    }]\n  }, {\n    id: 'EXM015',\n    courseId: 'CS101',\n    name: 'Programming Fundamentals Quiz',\n    type: 'quiz',\n    date: '2024-02-20',\n    time: '11:00-11:30',\n    duration: 30,\n    room: 'CS-101',\n    totalMarks: 20,\n    instructions: 'Quick assessment of basic programming concepts.',\n    syllabus: 'Variables, loops, functions, arrays',\n    status: 'completed',\n    results: [{\n      studentId: 'STU001',\n      marks: 18,\n      grade: 'A',\n      percentage: 90\n    }, {\n      studentId: 'STU004',\n      marks: 17,\n      grade: 'A-',\n      percentage: 85\n    }, {\n      studentId: 'STU005',\n      marks: 16,\n      grade: 'B+',\n      percentage: 80\n    }, {\n      studentId: 'STU007',\n      marks: 19,\n      grade: 'A',\n      percentage: 95\n    }, {\n      studentId: 'STU008',\n      marks: 15,\n      grade: 'B',\n      percentage: 75\n    }]\n  }],\n  // Admin user\n  admin: {\n    id: 'ADM001',\n    name: 'Admin User',\n    email: '<EMAIL>',\n    password: 'admin123',\n    role: 'admin',\n    phone: '+1234567896',\n    address: 'University Administration Building',\n    status: 'active'\n  }\n};", "map": {"version": 3, "names": ["mockData", "students", "id", "name", "email", "password", "role", "phone", "address", "dateOfBirth", "enrollmentDate", "semester", "department", "cgpa", "status", "profileImage", "faculty", "designation", "qualification", "experience", "specialization", "joinDate", "salary", "courses", "code", "credits", "description", "facultyId", "schedule", "days", "time", "room", "capacity", "enrolled", "enrollments", "studentId", "courseId", "grade", "grades", "year", "midterm", "final", "assignments", "quizzes", "participation", "total", "gpa", "attendance", "date", "fees", "tuitionFee", "libraryFee", "lab<PERSON>ee", "sportsFee", "activityFee", "technologyFee", "otherFees", "totalAmount", "paidAmount", "dueAmount", "dueDate", "paymentDate", "paymentMethod", "transactionId", "books", "title", "author", "isbn", "category", "publisher", "edition", "totalCopies", "availableCopies", "location", "borrowedBooks", "bookId", "borrowDate", "returnDate", "fine", "renewalCount", "max<PERSON><PERSON><PERSON><PERSON>", "exams", "type", "duration", "totalMarks", "instructions", "syllabus", "results", "marks", "percentage", "admin"], "sources": ["D:/HAMMAD/React/New folder/university/src/data/mockData.js"], "sourcesContent": ["export const mockData = {\n  students: [\n    {\n      id: 'STU001',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+1234567890',\n      address: '123 Main St, City, State',\n      dateOfBirth: '2000-05-15',\n      enrollmentDate: '2022-09-01',\n      semester: 3,\n      department: 'Computer Science',\n      cgpa: 3.75,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'STU002',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+1234567891',\n      address: '456 Oak Ave, City, State',\n      dateOfBirth: '2001-03-22',\n      enrollmentDate: '2022-09-01',\n      semester: 3,\n      department: 'Business Administration',\n      cgpa: 3.92,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'STU003',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+1234567892',\n      address: '789 Pine St, City, State',\n      dateOfBirth: '1999-11-08',\n      enrollmentDate: '2021-09-01',\n      semester: 5,\n      department: 'Engineering',\n      cgpa: 3.45,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'STU004',\n      name: 'Hammad Ul Rehman',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+***********',\n      address: 'Block A, Model Town, Multan, Pakistan',\n      dateOfBirth: '2001-07-15',\n      enrollmentDate: '2022-09-01',\n      semester: 4,\n      department: 'Software Engineering',\n      cgpa: 3.85,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'STU005',\n      name: 'Ghulam Mustafa',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+92301234567',\n      address: 'Cantt Area, Multan, Pakistan',\n      dateOfBirth: '2000-12-03',\n      enrollmentDate: '2022-09-01',\n      semester: 4,\n      department: 'Software Engineering',\n      cgpa: 3.72,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'STU006',\n      name: 'Ahmar Ashraf',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+92302345678',\n      address: 'Shah Rukn-e-Alam Colony, Multan, Pakistan',\n      dateOfBirth: '2001-04-22',\n      enrollmentDate: '2022-09-01',\n      semester: 4,\n      department: 'Software Engineering',\n      cgpa: 3.68,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'STU007',\n      name: 'Saifullah',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+92303456789',\n      address: 'Gulgasht Colony, Multan, Pakistan',\n      dateOfBirth: '2001-09-10',\n      enrollmentDate: '2022-09-01',\n      semester: 4,\n      department: 'Software Engineering',\n      cgpa: 3.91,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'STU008',\n      name: 'Anus Sarfaraz',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+92304567890',\n      address: 'Bosan Road, Multan, Pakistan',\n      dateOfBirth: '2000-11-28',\n      enrollmentDate: '2022-09-01',\n      semester: 4,\n      department: 'Software Engineering',\n      cgpa: 3.79,\n      status: 'active',\n      profileImage: null,\n    },\n  ],\n\n  faculty: [\n    {\n      id: 'FAC001',\n      name: 'Dr. Sarah Wilson',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'faculty',\n      phone: '+1234567893',\n      address: '321 University Blvd, City, State',\n      department: 'Computer Science',\n      designation: 'Professor',\n      qualification: 'Ph.D. in Computer Science',\n      experience: 15,\n      specialization: 'Machine Learning, Data Science',\n      joinDate: '2010-08-15',\n      salary: 85000,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'FAC002',\n      name: 'Dr. Robert Brown',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'faculty',\n      phone: '+1234567894',\n      address: '654 Academic Way, City, State',\n      department: 'Business Administration',\n      designation: 'Associate Professor',\n      qualification: 'Ph.D. in Business Administration',\n      experience: 12,\n      specialization: 'Marketing, Strategic Management',\n      joinDate: '2012-01-10',\n      salary: 75000,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'FAC003',\n      name: 'Dr. Emily Davis',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'faculty',\n      phone: '+1234567895',\n      address: '987 Scholar Lane, City, State',\n      department: 'Engineering',\n      designation: 'Assistant Professor',\n      qualification: 'Ph.D. in Mechanical Engineering',\n      experience: 8,\n      specialization: 'Thermodynamics, Fluid Mechanics',\n      joinDate: '2016-09-01',\n      salary: 65000,\n      status: 'active',\n      profileImage: null,\n    },\n  ],\n\n  courses: [\n    {\n      id: 'CS101',\n      name: 'Introduction to Programming',\n      code: 'CS101',\n      department: 'Computer Science',\n      credits: 3,\n      semester: 1,\n      description: 'Basic programming concepts using Python',\n      facultyId: 'FAC001',\n      schedule: {\n        days: ['Monday', 'Wednesday', 'Friday'],\n        time: '09:00-10:00',\n        room: 'CS-101',\n      },\n      capacity: 50,\n      enrolled: 35,\n      status: 'active',\n    },\n    {\n      id: 'CS201',\n      name: 'Data Structures',\n      code: 'CS201',\n      department: 'Computer Science',\n      credits: 4,\n      semester: 3,\n      description: 'Advanced data structures and algorithms',\n      facultyId: 'FAC001',\n      schedule: {\n        days: ['Tuesday', 'Thursday'],\n        time: '10:00-12:00',\n        room: 'CS-201',\n      },\n      capacity: 40,\n      enrolled: 28,\n      status: 'active',\n    },\n    {\n      id: 'BUS101',\n      name: 'Business Fundamentals',\n      code: 'BUS101',\n      department: 'Business Administration',\n      credits: 3,\n      semester: 1,\n      description: 'Introduction to business concepts',\n      facultyId: 'FAC002',\n      schedule: {\n        days: ['Monday', 'Wednesday'],\n        time: '14:00-15:30',\n        room: 'BUS-101',\n      },\n      capacity: 60,\n      enrolled: 45,\n      status: 'active',\n    },\n    {\n      id: 'ENG101',\n      name: 'Engineering Mathematics',\n      code: 'ENG101',\n      department: 'Engineering',\n      credits: 4,\n      semester: 1,\n      description: 'Mathematical foundations for engineering',\n      facultyId: 'FAC003',\n      schedule: {\n        days: ['Tuesday', 'Thursday', 'Friday'],\n        time: '08:00-09:00',\n        room: 'ENG-101',\n      },\n      capacity: 45,\n      enrolled: 42,\n      status: 'active',\n    },\n    {\n      id: 'SE101',\n      name: 'Introduction to Software Engineering',\n      code: 'SE101',\n      department: 'Software Engineering',\n      credits: 3,\n      semester: 1,\n      description: 'Fundamentals of software engineering principles and practices',\n      facultyId: 'FAC001',\n      schedule: {\n        days: ['Monday', 'Wednesday', 'Friday'],\n        time: '11:00-12:00',\n        room: 'SE-101',\n      },\n      capacity: 40,\n      enrolled: 35,\n      status: 'active',\n    },\n    {\n      id: 'SE201',\n      name: 'Object Oriented Programming',\n      code: 'SE201',\n      department: 'Software Engineering',\n      credits: 4,\n      semester: 2,\n      description: 'Advanced programming concepts using Java and C++',\n      facultyId: 'FAC001',\n      schedule: {\n        days: ['Tuesday', 'Thursday'],\n        time: '13:00-15:00',\n        room: 'SE-201',\n      },\n      capacity: 35,\n      enrolled: 30,\n      status: 'active',\n    },\n    {\n      id: 'SE301',\n      name: 'Software Design Patterns',\n      code: 'SE301',\n      department: 'Software Engineering',\n      credits: 3,\n      semester: 3,\n      description: 'Design patterns and software architecture principles',\n      facultyId: 'FAC001',\n      schedule: {\n        days: ['Monday', 'Wednesday'],\n        time: '15:00-16:30',\n        room: 'SE-301',\n      },\n      capacity: 30,\n      enrolled: 25,\n      status: 'active',\n    },\n    {\n      id: 'SE401',\n      name: 'Software Project Management',\n      code: 'SE401',\n      department: 'Software Engineering',\n      credits: 3,\n      semester: 4,\n      description: 'Project management methodologies for software development',\n      facultyId: 'FAC001',\n      schedule: {\n        days: ['Tuesday', 'Friday'],\n        time: '10:00-11:30',\n        room: 'SE-401',\n      },\n      capacity: 25,\n      enrolled: 20,\n      status: 'active',\n    },\n  ],\n\n  enrollments: [\n    {\n      id: 'ENR001',\n      studentId: 'STU001',\n      courseId: 'CS101',\n      enrollmentDate: '2022-09-01',\n      status: 'enrolled',\n      grade: null,\n    },\n    {\n      id: 'ENR002',\n      studentId: 'STU001',\n      courseId: 'CS201',\n      enrollmentDate: '2023-01-15',\n      status: 'enrolled',\n      grade: null,\n    },\n    {\n      id: 'ENR003',\n      studentId: 'STU002',\n      courseId: 'BUS101',\n      enrollmentDate: '2022-09-01',\n      status: 'enrolled',\n      grade: null,\n    },\n    {\n      id: 'ENR004',\n      studentId: 'STU003',\n      courseId: 'ENG101',\n      enrollmentDate: '2021-09-01',\n      status: 'completed',\n      grade: 'B+',\n    },\n    // Software Engineering Students Enrollments\n    {\n      id: 'ENR005',\n      studentId: 'STU004',\n      courseId: 'SE101',\n      enrollmentDate: '2022-09-01',\n      status: 'completed',\n      grade: 'A-',\n    },\n    {\n      id: 'ENR006',\n      studentId: 'STU004',\n      courseId: 'SE201',\n      enrollmentDate: '2023-01-15',\n      status: 'completed',\n      grade: 'A',\n    },\n    {\n      id: 'ENR007',\n      studentId: 'STU004',\n      courseId: 'SE301',\n      enrollmentDate: '2023-09-01',\n      status: 'completed',\n      grade: 'A-',\n    },\n    {\n      id: 'ENR008',\n      studentId: 'STU004',\n      courseId: 'SE401',\n      enrollmentDate: '2024-01-15',\n      status: 'enrolled',\n      grade: null,\n    },\n    {\n      id: 'ENR009',\n      studentId: 'STU005',\n      courseId: 'SE101',\n      enrollmentDate: '2022-09-01',\n      status: 'completed',\n      grade: 'B+',\n    },\n    {\n      id: 'ENR010',\n      studentId: 'STU005',\n      courseId: 'SE201',\n      enrollmentDate: '2023-01-15',\n      status: 'completed',\n      grade: 'A-',\n    },\n    {\n      id: 'ENR011',\n      studentId: 'STU005',\n      courseId: 'SE301',\n      enrollmentDate: '2023-09-01',\n      status: 'completed',\n      grade: 'B+',\n    },\n    {\n      id: 'ENR012',\n      studentId: 'STU005',\n      courseId: 'SE401',\n      enrollmentDate: '2024-01-15',\n      status: 'enrolled',\n      grade: null,\n    },\n    {\n      id: 'ENR013',\n      studentId: 'STU006',\n      courseId: 'SE101',\n      enrollmentDate: '2022-09-01',\n      status: 'completed',\n      grade: 'B+',\n    },\n    {\n      id: 'ENR014',\n      studentId: 'STU006',\n      courseId: 'SE201',\n      enrollmentDate: '2023-01-15',\n      status: 'completed',\n      grade: 'B',\n    },\n    {\n      id: 'ENR015',\n      studentId: 'STU006',\n      courseId: 'SE301',\n      enrollmentDate: '2023-09-01',\n      status: 'completed',\n      grade: 'A-',\n    },\n    {\n      id: 'ENR016',\n      studentId: 'STU006',\n      courseId: 'SE401',\n      enrollmentDate: '2024-01-15',\n      status: 'enrolled',\n      grade: null,\n    },\n    {\n      id: 'ENR017',\n      studentId: 'STU007',\n      courseId: 'SE101',\n      enrollmentDate: '2022-09-01',\n      status: 'completed',\n      grade: 'A',\n    },\n    {\n      id: 'ENR018',\n      studentId: 'STU007',\n      courseId: 'SE201',\n      enrollmentDate: '2023-01-15',\n      status: 'completed',\n      grade: 'A',\n    },\n    {\n      id: 'ENR019',\n      studentId: 'STU007',\n      courseId: 'SE301',\n      enrollmentDate: '2023-09-01',\n      status: 'completed',\n      grade: 'A',\n    },\n    {\n      id: 'ENR020',\n      studentId: 'STU007',\n      courseId: 'SE401',\n      enrollmentDate: '2024-01-15',\n      status: 'enrolled',\n      grade: null,\n    },\n    {\n      id: 'ENR021',\n      studentId: 'STU008',\n      courseId: 'SE101',\n      enrollmentDate: '2022-09-01',\n      status: 'completed',\n      grade: 'A-',\n    },\n    {\n      id: 'ENR022',\n      studentId: 'STU008',\n      courseId: 'SE201',\n      enrollmentDate: '2023-01-15',\n      status: 'completed',\n      grade: 'B+',\n    },\n    {\n      id: 'ENR023',\n      studentId: 'STU008',\n      courseId: 'SE301',\n      enrollmentDate: '2023-09-01',\n      status: 'completed',\n      grade: 'A-',\n    },\n    {\n      id: 'ENR024',\n      studentId: 'STU008',\n      courseId: 'SE401',\n      enrollmentDate: '2024-01-15',\n      status: 'enrolled',\n      grade: null,\n    },\n  ],\n\n  grades: [\n    // Student 1 (John Doe) - CS Student\n    {\n      id: 'GRD001',\n      studentId: 'STU001',\n      courseId: 'CS101',\n      semester: 1,\n      year: 2022,\n      midterm: 85,\n      final: 88,\n      assignments: 92,\n      quizzes: 90,\n      participation: 95,\n      total: 88.5,\n      grade: 'A-',\n      gpa: 3.7,\n      status: 'completed',\n    },\n    {\n      id: 'GRD002',\n      studentId: 'STU001',\n      courseId: 'CS201',\n      semester: 3,\n      year: 2023,\n      midterm: 92,\n      final: 89,\n      assignments: 95,\n      quizzes: 88,\n      participation: 92,\n      total: 91.2,\n      grade: 'A',\n      gpa: 4.0,\n      status: 'completed',\n    },\n    {\n      id: 'GRD003',\n      studentId: 'STU001',\n      courseId: 'ENG101',\n      semester: 2,\n      year: 2023,\n      midterm: 78,\n      final: 82,\n      assignments: 85,\n      quizzes: 80,\n      participation: 88,\n      total: 81.5,\n      grade: 'B+',\n      gpa: 3.3,\n      status: 'completed',\n    },\n\n    // Student 2 (Jane Smith) - Business Student\n    {\n      id: 'GRD004',\n      studentId: 'STU002',\n      courseId: 'BUS101',\n      semester: 1,\n      year: 2022,\n      midterm: 92,\n      final: 95,\n      assignments: 89,\n      quizzes: 94,\n      participation: 96,\n      total: 92.3,\n      grade: 'A',\n      gpa: 4.0,\n      status: 'completed',\n    },\n    {\n      id: 'GRD005',\n      studentId: 'STU002',\n      courseId: 'CS101',\n      semester: 2,\n      year: 2023,\n      midterm: 88,\n      final: 85,\n      assignments: 90,\n      quizzes: 87,\n      participation: 89,\n      total: 87.8,\n      grade: 'A-',\n      gpa: 3.7,\n      status: 'completed',\n    },\n    {\n      id: 'GRD006',\n      studentId: 'STU002',\n      courseId: 'ENG101',\n      semester: 3,\n      year: 2023,\n      midterm: 95,\n      final: 93,\n      assignments: 97,\n      quizzes: 92,\n      participation: 98,\n      total: 94.5,\n      grade: 'A',\n      gpa: 4.0,\n      status: 'completed',\n    },\n\n    // Student 3 (Mike Johnson) - Engineering Student\n    {\n      id: 'GRD007',\n      studentId: 'STU003',\n      courseId: 'ENG101',\n      semester: 1,\n      year: 2021,\n      midterm: 78,\n      final: 82,\n      assignments: 85,\n      quizzes: 75,\n      participation: 80,\n      total: 81.5,\n      grade: 'B+',\n      gpa: 3.3,\n      status: 'completed',\n    },\n    {\n      id: 'GRD008',\n      studentId: 'STU003',\n      courseId: 'CS101',\n      semester: 2,\n      year: 2022,\n      midterm: 72,\n      final: 75,\n      assignments: 78,\n      quizzes: 70,\n      participation: 85,\n      total: 74.8,\n      grade: 'B',\n      gpa: 3.0,\n      status: 'completed',\n    },\n    {\n      id: 'GRD009',\n      studentId: 'STU003',\n      courseId: 'CS201',\n      semester: 5,\n      year: 2024,\n      midterm: 85,\n      final: null,\n      assignments: 88,\n      quizzes: 82,\n      participation: 90,\n      total: null,\n      grade: null,\n      gpa: null,\n      status: 'in_progress',\n    },\n\n    // Software Engineering Students Grades\n    // Student 4 (Hammad Ul Rehman) - SE Student\n    {\n      id: 'GRD010',\n      studentId: 'STU004',\n      courseId: 'SE101',\n      semester: 1,\n      year: 2022,\n      midterm: 88,\n      final: 85,\n      assignments: 92,\n      quizzes: 87,\n      participation: 90,\n      total: 87.8,\n      grade: 'A-',\n      gpa: 3.7,\n      status: 'completed',\n    },\n    {\n      id: 'GRD011',\n      studentId: 'STU004',\n      courseId: 'SE201',\n      semester: 2,\n      year: 2023,\n      midterm: 92,\n      final: 89,\n      assignments: 95,\n      quizzes: 90,\n      participation: 94,\n      total: 91.5,\n      grade: 'A',\n      gpa: 4.0,\n      status: 'completed',\n    },\n    {\n      id: 'GRD012',\n      studentId: 'STU004',\n      courseId: 'SE301',\n      semester: 3,\n      year: 2023,\n      midterm: 85,\n      final: 88,\n      assignments: 90,\n      quizzes: 86,\n      participation: 92,\n      total: 87.8,\n      grade: 'A-',\n      gpa: 3.7,\n      status: 'completed',\n    },\n\n    // Student 5 (Ghulam Mustafa) - SE Student\n    {\n      id: 'GRD013',\n      studentId: 'STU005',\n      courseId: 'SE101',\n      semester: 1,\n      year: 2022,\n      midterm: 82,\n      final: 85,\n      assignments: 88,\n      quizzes: 80,\n      participation: 85,\n      total: 84.2,\n      grade: 'B+',\n      gpa: 3.3,\n      status: 'completed',\n    },\n    {\n      id: 'GRD014',\n      studentId: 'STU005',\n      courseId: 'SE201',\n      semester: 2,\n      year: 2023,\n      midterm: 88,\n      final: 86,\n      assignments: 90,\n      quizzes: 85,\n      participation: 89,\n      total: 87.5,\n      grade: 'A-',\n      gpa: 3.7,\n      status: 'completed',\n    },\n    {\n      id: 'GRD015',\n      studentId: 'STU005',\n      courseId: 'SE301',\n      semester: 3,\n      year: 2023,\n      midterm: 80,\n      final: 83,\n      assignments: 85,\n      quizzes: 78,\n      participation: 88,\n      total: 82.8,\n      grade: 'B+',\n      gpa: 3.3,\n      status: 'completed',\n    },\n\n    // Student 6 (Ahmar Ashraf) - SE Student\n    {\n      id: 'GRD016',\n      studentId: 'STU006',\n      courseId: 'SE101',\n      semester: 1,\n      year: 2022,\n      midterm: 80,\n      final: 82,\n      assignments: 85,\n      quizzes: 78,\n      participation: 83,\n      total: 81.8,\n      grade: 'B+',\n      gpa: 3.3,\n      status: 'completed',\n    },\n    {\n      id: 'GRD017',\n      studentId: 'STU006',\n      courseId: 'SE201',\n      semester: 2,\n      year: 2023,\n      midterm: 75,\n      final: 78,\n      assignments: 82,\n      quizzes: 76,\n      participation: 80,\n      total: 78.2,\n      grade: 'B',\n      gpa: 3.0,\n      status: 'completed',\n    },\n    {\n      id: 'GRD018',\n      studentId: 'STU006',\n      courseId: 'SE301',\n      semester: 3,\n      year: 2023,\n      midterm: 88,\n      final: 85,\n      assignments: 90,\n      quizzes: 87,\n      participation: 89,\n      total: 87.5,\n      grade: 'A-',\n      gpa: 3.7,\n      status: 'completed',\n    },\n\n    // Student 7 (Saifullah) - SE Student\n    {\n      id: 'GRD019',\n      studentId: 'STU007',\n      courseId: 'SE101',\n      semester: 1,\n      year: 2022,\n      midterm: 95,\n      final: 92,\n      assignments: 96,\n      quizzes: 94,\n      participation: 98,\n      total: 94.2,\n      grade: 'A',\n      gpa: 4.0,\n      status: 'completed',\n    },\n    {\n      id: 'GRD020',\n      studentId: 'STU007',\n      courseId: 'SE201',\n      semester: 2,\n      year: 2023,\n      midterm: 93,\n      final: 95,\n      assignments: 97,\n      quizzes: 92,\n      participation: 96,\n      total: 94.5,\n      grade: 'A',\n      gpa: 4.0,\n      status: 'completed',\n    },\n    {\n      id: 'GRD021',\n      studentId: 'STU007',\n      courseId: 'SE301',\n      semester: 3,\n      year: 2023,\n      midterm: 90,\n      final: 93,\n      assignments: 95,\n      quizzes: 89,\n      participation: 94,\n      total: 92.2,\n      grade: 'A',\n      gpa: 4.0,\n      status: 'completed',\n    },\n\n    // Student 8 (Anus Sarfaraz) - SE Student\n    {\n      id: 'GRD022',\n      studentId: 'STU008',\n      courseId: 'SE101',\n      semester: 1,\n      year: 2022,\n      midterm: 85,\n      final: 88,\n      assignments: 90,\n      quizzes: 84,\n      participation: 87,\n      total: 86.8,\n      grade: 'A-',\n      gpa: 3.7,\n      status: 'completed',\n    },\n    {\n      id: 'GRD023',\n      studentId: 'STU008',\n      courseId: 'SE201',\n      semester: 2,\n      year: 2023,\n      midterm: 82,\n      final: 85,\n      assignments: 88,\n      quizzes: 80,\n      participation: 86,\n      total: 84.2,\n      grade: 'B+',\n      gpa: 3.3,\n      status: 'completed',\n    },\n    {\n      id: 'GRD024',\n      studentId: 'STU008',\n      courseId: 'SE301',\n      semester: 3,\n      year: 2023,\n      midterm: 88,\n      final: 86,\n      assignments: 92,\n      quizzes: 85,\n      participation: 90,\n      total: 87.8,\n      grade: 'A-',\n      gpa: 3.7,\n      status: 'completed',\n    },\n  ],\n\n  attendance: [\n    // CS101 Attendance - January 2024\n    { id: 'ATT001', studentId: 'STU001', courseId: 'CS101', date: '2024-01-08', status: 'present', time: '09:00' },\n    { id: 'ATT002', studentId: 'STU001', courseId: 'CS101', date: '2024-01-10', status: 'present', time: '09:00' },\n    { id: 'ATT003', studentId: 'STU001', courseId: 'CS101', date: '2024-01-12', status: 'present', time: '09:00' },\n    { id: 'ATT004', studentId: 'STU001', courseId: 'CS101', date: '2024-01-15', status: 'present', time: '09:00' },\n    { id: 'ATT005', studentId: 'STU001', courseId: 'CS101', date: '2024-01-17', status: 'absent', time: '09:00' },\n    { id: 'ATT006', studentId: 'STU001', courseId: 'CS101', date: '2024-01-19', status: 'present', time: '09:00' },\n    { id: 'ATT007', studentId: 'STU001', courseId: 'CS101', date: '2024-01-22', status: 'present', time: '09:00' },\n    { id: 'ATT008', studentId: 'STU001', courseId: 'CS101', date: '2024-01-24', status: 'late', time: '09:15' },\n    { id: 'ATT009', studentId: 'STU001', courseId: 'CS101', date: '2024-01-26', status: 'present', time: '09:00' },\n    { id: 'ATT010', studentId: 'STU001', courseId: 'CS101', date: '2024-01-29', status: 'present', time: '09:00' },\n\n    // CS201 Attendance for STU001\n    { id: 'ATT011', studentId: 'STU001', courseId: 'CS201', date: '2024-01-09', status: 'present', time: '10:00' },\n    { id: 'ATT012', studentId: 'STU001', courseId: 'CS201', date: '2024-01-11', status: 'present', time: '10:00' },\n    { id: 'ATT013', studentId: 'STU001', courseId: 'CS201', date: '2024-01-16', status: 'present', time: '10:00' },\n    { id: 'ATT014', studentId: 'STU001', courseId: 'CS201', date: '2024-01-18', status: 'present', time: '10:00' },\n    { id: 'ATT015', studentId: 'STU001', courseId: 'CS201', date: '2024-01-23', status: 'absent', time: '10:00' },\n    { id: 'ATT016', studentId: 'STU001', courseId: 'CS201', date: '2024-01-25', status: 'present', time: '10:00' },\n    { id: 'ATT017', studentId: 'STU001', courseId: 'CS201', date: '2024-01-30', status: 'present', time: '10:00' },\n\n    // BUS101 Attendance for STU002\n    { id: 'ATT018', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-08', status: 'present', time: '14:00' },\n    { id: 'ATT019', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-10', status: 'present', time: '14:00' },\n    { id: 'ATT020', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-15', status: 'present', time: '14:00' },\n    { id: 'ATT021', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-17', status: 'present', time: '14:00' },\n    { id: 'ATT022', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-22', status: 'present', time: '14:00' },\n    { id: 'ATT023', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-24', status: 'present', time: '14:00' },\n    { id: 'ATT024', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-29', status: 'present', time: '14:00' },\n    { id: 'ATT025', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-31', status: 'present', time: '14:00' },\n\n    // ENG101 Attendance for STU003\n    { id: 'ATT026', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-09', status: 'present', time: '08:00' },\n    { id: 'ATT027', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-11', status: 'late', time: '08:20' },\n    { id: 'ATT028', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-12', status: 'present', time: '08:00' },\n    { id: 'ATT029', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-16', status: 'absent', time: '08:00' },\n    { id: 'ATT030', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-18', status: 'present', time: '08:00' },\n    { id: 'ATT031', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-19', status: 'present', time: '08:00' },\n    { id: 'ATT032', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-23', status: 'absent', time: '08:00' },\n    { id: 'ATT033', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-25', status: 'present', time: '08:00' },\n    { id: 'ATT034', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-26', status: 'late', time: '08:15' },\n    { id: 'ATT035', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-30', status: 'present', time: '08:00' },\n\n    // Cross-enrollment attendance\n    { id: 'ATT036', studentId: 'STU002', courseId: 'CS101', date: '2024-01-08', status: 'present', time: '09:00' },\n    { id: 'ATT037', studentId: 'STU002', courseId: 'CS101', date: '2024-01-10', status: 'present', time: '09:00' },\n    { id: 'ATT038', studentId: 'STU002', courseId: 'CS101', date: '2024-01-12', status: 'absent', time: '09:00' },\n    { id: 'ATT039', studentId: 'STU002', courseId: 'CS101', date: '2024-01-15', status: 'present', time: '09:00' },\n    { id: 'ATT040', studentId: 'STU002', courseId: 'CS101', date: '2024-01-17', status: 'present', time: '09:00' },\n  ],\n\n  fees: [\n    // Student 1 (John Doe) - CS Student Fee Records\n    {\n      id: 'FEE001',\n      studentId: 'STU001',\n      semester: 1,\n      year: 2022,\n      tuitionFee: 4500,\n      libraryFee: 150,\n      labFee: 250,\n      sportsFee: 100,\n      activityFee: 75,\n      technologyFee: 200,\n      otherFees: 50,\n      totalAmount: 5325,\n      paidAmount: 5325,\n      dueAmount: 0,\n      dueDate: '2022-08-31',\n      paymentDate: '2022-08-20',\n      status: 'paid',\n      paymentMethod: 'bank_transfer',\n      transactionId: 'TXN001',\n    },\n    {\n      id: 'FEE002',\n      studentId: 'STU001',\n      semester: 2,\n      year: 2023,\n      tuitionFee: 4800,\n      libraryFee: 150,\n      labFee: 300,\n      sportsFee: 100,\n      activityFee: 75,\n      technologyFee: 200,\n      otherFees: 50,\n      totalAmount: 5675,\n      paidAmount: 5675,\n      dueAmount: 0,\n      dueDate: '2023-01-31',\n      paymentDate: '2023-01-25',\n      status: 'paid',\n      paymentMethod: 'credit_card',\n      transactionId: 'TXN002',\n    },\n    {\n      id: 'FEE003',\n      studentId: 'STU001',\n      semester: 3,\n      year: 2024,\n      tuitionFee: 5000,\n      libraryFee: 200,\n      labFee: 300,\n      sportsFee: 100,\n      activityFee: 75,\n      technologyFee: 250,\n      otherFees: 75,\n      totalAmount: 6000,\n      paidAmount: 6000,\n      dueAmount: 0,\n      dueDate: '2024-01-31',\n      paymentDate: '2024-01-15',\n      status: 'paid',\n      paymentMethod: 'bank_transfer',\n      transactionId: 'TXN003',\n    },\n\n    // Student 2 (Jane Smith) - Business Student Fee Records\n    {\n      id: 'FEE004',\n      studentId: 'STU002',\n      semester: 1,\n      year: 2022,\n      tuitionFee: 4200,\n      libraryFee: 150,\n      labFee: 200,\n      sportsFee: 100,\n      activityFee: 75,\n      technologyFee: 150,\n      otherFees: 50,\n      totalAmount: 4925,\n      paidAmount: 4925,\n      dueAmount: 0,\n      dueDate: '2022-08-31',\n      paymentDate: '2022-08-28',\n      status: 'paid',\n      paymentMethod: 'bank_transfer',\n      transactionId: 'TXN004',\n    },\n    {\n      id: 'FEE005',\n      studentId: 'STU002',\n      semester: 2,\n      year: 2023,\n      tuitionFee: 4400,\n      libraryFee: 150,\n      labFee: 200,\n      sportsFee: 100,\n      activityFee: 75,\n      technologyFee: 175,\n      otherFees: 50,\n      totalAmount: 5150,\n      paidAmount: 5150,\n      dueAmount: 0,\n      dueDate: '2023-01-31',\n      paymentDate: '2023-01-20',\n      status: 'paid',\n      paymentMethod: 'debit_card',\n      transactionId: 'TXN005',\n    },\n    {\n      id: 'FEE006',\n      studentId: 'STU002',\n      semester: 3,\n      year: 2024,\n      tuitionFee: 4800,\n      libraryFee: 200,\n      labFee: 250,\n      sportsFee: 100,\n      activityFee: 75,\n      technologyFee: 200,\n      otherFees: 75,\n      totalAmount: 5700,\n      paidAmount: 2000,\n      dueAmount: 3700,\n      dueDate: '2024-02-15',\n      paymentDate: '2024-01-10',\n      status: 'partial',\n      paymentMethod: 'bank_transfer',\n      transactionId: 'TXN006',\n    },\n\n    // Student 3 (Mike Johnson) - Engineering Student Fee Records\n    {\n      id: 'FEE007',\n      studentId: 'STU003',\n      semester: 1,\n      year: 2021,\n      tuitionFee: 5200,\n      libraryFee: 150,\n      labFee: 400,\n      sportsFee: 100,\n      activityFee: 75,\n      technologyFee: 300,\n      otherFees: 75,\n      totalAmount: 6300,\n      paidAmount: 6300,\n      dueAmount: 0,\n      dueDate: '2021-08-31',\n      paymentDate: '2021-08-25',\n      status: 'paid',\n      paymentMethod: 'bank_transfer',\n      transactionId: 'TXN007',\n    },\n    {\n      id: 'FEE008',\n      studentId: 'STU003',\n      semester: 4,\n      year: 2023,\n      tuitionFee: 5500,\n      libraryFee: 200,\n      labFee: 450,\n      sportsFee: 100,\n      activityFee: 75,\n      technologyFee: 350,\n      otherFees: 75,\n      totalAmount: 6750,\n      paidAmount: 4000,\n      dueAmount: 2750,\n      dueDate: '2024-02-28',\n      paymentDate: '2023-12-15',\n      status: 'partial',\n      paymentMethod: 'installment',\n      transactionId: 'TXN008',\n    },\n    {\n      id: 'FEE009',\n      studentId: 'STU003',\n      semester: 5,\n      year: 2024,\n      tuitionFee: 5800,\n      libraryFee: 200,\n      labFee: 500,\n      sportsFee: 100,\n      activityFee: 75,\n      technologyFee: 400,\n      otherFees: 100,\n      totalAmount: 7175,\n      paidAmount: 0,\n      dueAmount: 7175,\n      dueDate: '2024-03-15',\n      paymentDate: null,\n      status: 'unpaid',\n      paymentMethod: null,\n      transactionId: null,\n    },\n  ],\n\n  books: [\n    {\n      id: 'BOOK001',\n      title: 'Introduction to Algorithms',\n      author: 'Thomas H. Cormen',\n      isbn: '978-0262033848',\n      category: 'Computer Science',\n      publisher: 'MIT Press',\n      edition: '3rd',\n      totalCopies: 10,\n      availableCopies: 7,\n      location: 'CS Section - Shelf A1',\n      status: 'available',\n    },\n    {\n      id: 'BOOK002',\n      title: 'Clean Code',\n      author: 'Robert C. Martin',\n      isbn: '978-0132350884',\n      category: 'Computer Science',\n      publisher: 'Prentice Hall',\n      edition: '1st',\n      totalCopies: 8,\n      availableCopies: 5,\n      location: 'CS Section - Shelf A2',\n      status: 'available',\n    },\n    {\n      id: 'BOOK003',\n      title: 'Marketing Management',\n      author: 'Philip Kotler',\n      isbn: '978-0134236933',\n      category: 'Business',\n      publisher: 'Pearson',\n      edition: '15th',\n      totalCopies: 12,\n      availableCopies: 9,\n      location: 'Business Section - Shelf B1',\n      status: 'available',\n    },\n    {\n      id: 'BOOK004',\n      title: 'Data Structures and Algorithms in Java',\n      author: 'Robert Lafore',\n      isbn: '978-**********',\n      category: 'Computer Science',\n      publisher: 'Sams Publishing',\n      edition: '2nd',\n      totalCopies: 15,\n      availableCopies: 12,\n      location: 'CS Section - Shelf A3',\n      status: 'available',\n    },\n    {\n      id: 'BOOK005',\n      title: 'Financial Accounting',\n      author: 'Jerry J. Weygandt',\n      isbn: '978-**********',\n      category: 'Business',\n      publisher: 'Wiley',\n      edition: '10th',\n      totalCopies: 10,\n      availableCopies: 6,\n      location: 'Business Section - Shelf B2',\n      status: 'available',\n    },\n    {\n      id: 'BOOK006',\n      title: 'English Literature: A Survey',\n      author: 'William J. Long',\n      isbn: '978-**********',\n      category: 'Literature',\n      publisher: 'Kalyani Publishers',\n      edition: '1st',\n      totalCopies: 8,\n      availableCopies: 5,\n      location: 'Literature Section - Shelf C1',\n      status: 'available',\n    },\n    {\n      id: 'BOOK007',\n      title: 'Engineering Mathematics',\n      author: 'K.A. Stroud',\n      isbn: '978-**********',\n      category: 'Engineering',\n      publisher: 'Palgrave Macmillan',\n      edition: '7th',\n      totalCopies: 20,\n      availableCopies: 16,\n      location: 'Engineering Section - Shelf D1',\n      status: 'available',\n    },\n    {\n      id: 'BOOK008',\n      title: 'Physics for Scientists and Engineers',\n      author: 'Raymond A. Serway',\n      isbn: '978-1133947271',\n      category: 'Science',\n      publisher: 'Cengage Learning',\n      edition: '9th',\n      totalCopies: 18,\n      availableCopies: 14,\n      location: 'Science Section - Shelf E1',\n      status: 'available',\n    },\n    {\n      id: 'BOOK009',\n      title: 'Organic Chemistry',\n      author: 'Paula Yurkanis Bruice',\n      isbn: '978-0134042282',\n      category: 'Science',\n      publisher: 'Pearson',\n      edition: '8th',\n      totalCopies: 12,\n      availableCopies: 8,\n      location: 'Science Section - Shelf E2',\n      status: 'available',\n    },\n    {\n      id: 'BOOK010',\n      title: 'Database System Concepts',\n      author: 'Abraham Silberschatz',\n      isbn: '978-0078022159',\n      category: 'Computer Science',\n      publisher: 'McGraw-Hill Education',\n      edition: '7th',\n      totalCopies: 14,\n      availableCopies: 10,\n      location: 'CS Section - Shelf A4',\n      status: 'available',\n    },\n    {\n      id: 'BOOK011',\n      title: 'Principles of Economics',\n      author: 'N. Gregory Mankiw',\n      isbn: '978-1305585126',\n      category: 'Business',\n      publisher: 'Cengage Learning',\n      edition: '8th',\n      totalCopies: 16,\n      availableCopies: 13,\n      location: 'Business Section - Shelf B3',\n      status: 'available',\n    },\n    {\n      id: 'BOOK012',\n      title: 'Creative Writing: Four Genres in Brief',\n      author: 'David Starkey',\n      isbn: '978-1319035334',\n      category: 'Literature',\n      publisher: 'Bedford/St. Martins',\n      edition: '3rd',\n      totalCopies: 6,\n      availableCopies: 4,\n      location: 'Literature Section - Shelf C2',\n      status: 'available',\n    },\n    // Programming Books for Software Engineering Students\n    {\n      id: 'BOOK013',\n      title: 'Fundamentals of Programming',\n      author: 'Kenneth Lambert',\n      isbn: '978-1337109635',\n      category: 'Programming',\n      publisher: 'Cengage Learning',\n      edition: '4th',\n      totalCopies: 15,\n      availableCopies: 12,\n      location: 'Programming Section - Shelf P1',\n      status: 'available',\n    },\n    {\n      id: 'BOOK014',\n      title: 'Object-Oriented Programming with C++',\n      author: 'E. Balagurusamy',\n      isbn: '978-0070634381',\n      category: 'Programming',\n      publisher: 'McGraw Hill Education',\n      edition: '8th',\n      totalCopies: 20,\n      availableCopies: 16,\n      location: 'Programming Section - Shelf P2',\n      status: 'available',\n    },\n    {\n      id: 'BOOK015',\n      title: 'The C Programming Language',\n      author: 'Brian W. Kernighan',\n      isbn: '978-0131103627',\n      category: 'Programming',\n      publisher: 'Prentice Hall',\n      edition: '2nd',\n      totalCopies: 18,\n      availableCopies: 14,\n      location: 'Programming Section - Shelf P3',\n      status: 'available',\n    },\n    {\n      id: 'BOOK016',\n      title: 'JavaScript: The Definitive Guide',\n      author: 'David Flanagan',\n      isbn: '978-**********',\n      category: 'Web Development',\n      publisher: \"O'Reilly Media\",\n      edition: '7th',\n      totalCopies: 12,\n      availableCopies: 9,\n      location: 'Web Dev Section - Shelf W1',\n      status: 'available',\n    },\n    {\n      id: 'BOOK017',\n      title: 'Learning React: Modern Patterns',\n      author: 'Alex Banks',\n      isbn: '978-**********',\n      category: 'Web Development',\n      publisher: \"O'Reilly Media\",\n      edition: '2nd',\n      totalCopies: 10,\n      availableCopies: 7,\n      location: 'Web Dev Section - Shelf W2',\n      status: 'available',\n    },\n    {\n      id: 'BOOK018',\n      title: 'Python Programming: An Introduction',\n      author: 'John Zelle',\n      isbn: '978-**********',\n      category: 'Programming',\n      publisher: 'Franklin Beedle',\n      edition: '3rd',\n      totalCopies: 16,\n      availableCopies: 13,\n      location: 'Programming Section - Shelf P4',\n      status: 'available',\n    },\n    {\n      id: 'BOOK019',\n      title: 'Java: The Complete Reference',\n      author: 'Herbert Schildt',\n      isbn: '978-**********',\n      category: 'Programming',\n      publisher: 'McGraw-Hill Education',\n      edition: '12th',\n      totalCopies: 14,\n      availableCopies: 11,\n      location: 'Programming Section - Shelf P5',\n      status: 'available',\n    },\n    {\n      id: 'BOOK020',\n      title: 'Software Engineering: A Practitioner\\'s Approach',\n      author: 'Roger Pressman',\n      isbn: '978-0078022128',\n      category: 'Software Engineering',\n      publisher: 'McGraw-Hill Education',\n      edition: '8th',\n      totalCopies: 12,\n      availableCopies: 8,\n      location: 'SE Section - Shelf SE1',\n      status: 'available',\n    },\n  ],\n\n  borrowedBooks: [\n    // Currently Borrowed Books\n    {\n      id: 'BOR001',\n      studentId: 'STU001',\n      bookId: 'BOOK001',\n      borrowDate: '2024-01-10',\n      dueDate: '2024-02-10',\n      returnDate: null,\n      status: 'borrowed',\n      fine: 0,\n      renewalCount: 0,\n      maxRenewals: 2,\n    },\n    {\n      id: 'BOR002',\n      studentId: 'STU001',\n      bookId: 'BOOK004',\n      borrowDate: '2024-01-15',\n      dueDate: '2024-02-15',\n      returnDate: null,\n      status: 'borrowed',\n      fine: 0,\n      renewalCount: 1,\n      maxRenewals: 2,\n    },\n    {\n      id: 'BOR003',\n      studentId: 'STU002',\n      bookId: 'BOOK002',\n      borrowDate: '2024-01-05',\n      dueDate: '2024-02-05',\n      returnDate: null,\n      status: 'overdue',\n      fine: 15,\n      renewalCount: 0,\n      maxRenewals: 2,\n    },\n    {\n      id: 'BOR004',\n      studentId: 'STU002',\n      bookId: 'BOOK005',\n      borrowDate: '2024-01-20',\n      dueDate: '2024-02-20',\n      returnDate: null,\n      status: 'borrowed',\n      fine: 0,\n      renewalCount: 0,\n      maxRenewals: 2,\n    },\n    {\n      id: 'BOR005',\n      studentId: 'STU003',\n      bookId: 'BOOK006',\n      borrowDate: '2024-01-12',\n      dueDate: '2024-02-12',\n      returnDate: null,\n      status: 'borrowed',\n      fine: 0,\n      renewalCount: 0,\n      maxRenewals: 2,\n    },\n    {\n      id: 'BOR006',\n      studentId: 'STU003',\n      bookId: 'BOOK007',\n      borrowDate: '2024-01-08',\n      dueDate: '2024-02-08',\n      returnDate: null,\n      status: 'overdue',\n      fine: 25,\n      renewalCount: 2,\n      maxRenewals: 2,\n    },\n\n    // Previously Returned Books\n    {\n      id: 'BOR007',\n      studentId: 'STU001',\n      bookId: 'BOOK003',\n      borrowDate: '2023-12-01',\n      dueDate: '2024-01-01',\n      returnDate: '2023-12-28',\n      status: 'returned',\n      fine: 0,\n      renewalCount: 0,\n      maxRenewals: 2,\n    },\n    {\n      id: 'BOR008',\n      studentId: 'STU002',\n      bookId: 'BOOK008',\n      borrowDate: '2023-11-15',\n      dueDate: '2023-12-15',\n      returnDate: '2023-12-20',\n      status: 'returned',\n      fine: 10,\n      renewalCount: 1,\n      maxRenewals: 2,\n    },\n    {\n      id: 'BOR009',\n      studentId: 'STU003',\n      bookId: 'BOOK009',\n      borrowDate: '2023-10-20',\n      dueDate: '2023-11-20',\n      returnDate: '2023-11-18',\n      status: 'returned',\n      fine: 0,\n      renewalCount: 0,\n      maxRenewals: 2,\n    },\n  ],\n\n  exams: [\n    // Upcoming Exams\n    {\n      id: 'EXM001',\n      courseId: 'CS101',\n      name: 'Midterm Examination',\n      type: 'midterm',\n      date: '2024-02-15',\n      time: '09:00-12:00',\n      room: 'Exam Hall 1',\n      duration: 180,\n      totalMarks: 100,\n      status: 'scheduled',\n      instructions: 'Bring calculator and ID card. No mobile phones allowed.',\n      syllabus: 'Chapters 1-5: Programming Fundamentals, Data Types, Control Structures',\n    },\n    {\n      id: 'EXM002',\n      courseId: 'CS101',\n      name: 'Final Examination',\n      type: 'final',\n      date: '2024-05-20',\n      time: '09:00-12:00',\n      room: 'Exam Hall 1',\n      duration: 180,\n      totalMarks: 150,\n      status: 'scheduled',\n      instructions: 'Comprehensive exam covering all course material. Bring calculator and ID card.',\n      syllabus: 'All chapters: Programming, Data Structures, Algorithms, Object-Oriented Programming',\n    },\n    {\n      id: 'EXM003',\n      courseId: 'CS201',\n      name: 'Quiz 1',\n      type: 'quiz',\n      date: '2024-02-08',\n      time: '10:00-10:30',\n      room: 'Room 201',\n      duration: 30,\n      totalMarks: 25,\n      status: 'scheduled',\n      instructions: 'Short quiz on recent topics. No materials allowed.',\n      syllabus: 'Advanced Data Structures: Trees and Graphs',\n    },\n    {\n      id: 'EXM004',\n      courseId: 'CS201',\n      name: 'Midterm Examination',\n      type: 'midterm',\n      date: '2024-03-10',\n      time: '14:00-17:00',\n      room: 'Exam Hall 2',\n      duration: 180,\n      totalMarks: 100,\n      status: 'scheduled',\n      instructions: 'Bring calculator and ID card. Programming questions included.',\n      syllabus: 'Chapters 1-6: Advanced Data Structures, Algorithm Analysis, Sorting',\n    },\n    {\n      id: 'EXM005',\n      courseId: 'BUS101',\n      name: 'Midterm Examination',\n      type: 'midterm',\n      date: '2024-02-20',\n      time: '14:00-16:30',\n      room: 'Business Hall A',\n      duration: 150,\n      totalMarks: 100,\n      status: 'scheduled',\n      instructions: 'Case study analysis. Bring calculator.',\n      syllabus: 'Business Fundamentals, Management Principles, Organizational Behavior',\n    },\n    {\n      id: 'EXM006',\n      courseId: 'BUS101',\n      name: 'Final Examination',\n      type: 'final',\n      date: '2024-05-25',\n      time: '14:00-17:00',\n      room: 'Business Hall A',\n      duration: 180,\n      totalMarks: 150,\n      status: 'scheduled',\n      instructions: 'Comprehensive business exam with case studies.',\n      syllabus: 'All course material: Management, Marketing, Finance, Operations',\n    },\n    {\n      id: 'EXM007',\n      courseId: 'ENG101',\n      name: 'Essay Examination',\n      type: 'midterm',\n      date: '2024-02-25',\n      time: '08:00-11:00',\n      room: 'Literature Hall',\n      duration: 180,\n      totalMarks: 100,\n      status: 'scheduled',\n      instructions: 'Essay writing exam. Bring pen and paper only.',\n      syllabus: 'Literary Analysis, Essay Writing, Grammar and Composition',\n    },\n    {\n      id: 'EXM008',\n      courseId: 'ENG101',\n      name: 'Final Examination',\n      type: 'final',\n      date: '2024-05-30',\n      time: '08:00-11:00',\n      room: 'Literature Hall',\n      duration: 180,\n      totalMarks: 150,\n      status: 'scheduled',\n      instructions: 'Comprehensive literature and writing exam.',\n      syllabus: 'Complete course: Literature, Writing, Critical Analysis, Research Methods',\n    },\n\n    // Current/Ongoing Exams\n    {\n      id: 'EXM009',\n      courseId: 'CS101',\n      name: 'Assignment 1 Submission',\n      type: 'assignment',\n      date: '2024-02-05',\n      time: '23:59',\n      room: 'Online Submission',\n      duration: 0,\n      totalMarks: 50,\n      status: 'ongoing',\n      instructions: 'Submit programming assignment via online portal.',\n      syllabus: 'Basic Programming Concepts and Problem Solving',\n    },\n\n    // Completed Exams\n    {\n      id: 'EXM010',\n      courseId: 'CS101',\n      name: 'Quiz 1',\n      type: 'quiz',\n      date: '2024-01-20',\n      time: '09:00-09:30',\n      room: 'Room 101',\n      duration: 30,\n      totalMarks: 25,\n      status: 'completed',\n      instructions: 'Basic programming concepts quiz.',\n      syllabus: 'Introduction to Programming, Variables, Basic Operations',\n    },\n    {\n      id: 'EXM011',\n      courseId: 'BUS101',\n      name: 'Quiz 1',\n      type: 'quiz',\n      date: '2024-01-25',\n      time: '14:00-14:30',\n      room: 'Business Room 1',\n      duration: 30,\n      totalMarks: 25,\n      status: 'completed',\n      instructions: 'Business fundamentals quiz.',\n      syllabus: 'Introduction to Business, Basic Management Concepts',\n    },\n    {\n      id: 'EXM012',\n      courseId: 'ENG101',\n      name: 'Essay Assignment 1',\n      type: 'assignment',\n      date: '2024-01-30',\n      time: '23:59',\n      room: 'Online Submission',\n      duration: 0,\n      totalMarks: 40,\n      status: 'completed',\n      instructions: 'Submit 1000-word essay on assigned topic.',\n      syllabus: 'Essay Structure, Thesis Development, Academic Writing',\n    },\n\n    // Completed Exams with Results\n    {\n      id: 'EXM013',\n      courseId: 'SE101',\n      name: 'Introduction to SE - Final Exam',\n      type: 'final',\n      date: '2023-12-15',\n      time: '09:00-12:00',\n      duration: 180,\n      room: 'SE-101',\n      totalMarks: 100,\n      instructions: 'Answer all questions. Use of calculators allowed.',\n      syllabus: 'Complete SE fundamentals, SDLC, methodologies',\n      status: 'completed',\n      results: [\n        { studentId: 'STU004', marks: 88, grade: 'A-', percentage: 88 },\n        { studentId: 'STU005', marks: 82, grade: 'B+', percentage: 82 },\n        { studentId: 'STU006', marks: 80, grade: 'B+', percentage: 80 },\n        { studentId: 'STU007', marks: 95, grade: 'A', percentage: 95 },\n        { studentId: 'STU008', marks: 85, grade: 'A-', percentage: 85 },\n      ],\n    },\n    {\n      id: 'EXM014',\n      courseId: 'SE201',\n      name: 'OOP Midterm Examination',\n      type: 'midterm',\n      date: '2024-03-10',\n      time: '10:00-12:00',\n      duration: 120,\n      room: 'SE-201',\n      totalMarks: 50,\n      instructions: 'Write code solutions for OOP problems.',\n      syllabus: 'Classes, Objects, Inheritance, Polymorphism',\n      status: 'completed',\n      results: [\n        { studentId: 'STU004', marks: 46, grade: 'A', percentage: 92 },\n        { studentId: 'STU005', marks: 44, grade: 'A-', percentage: 88 },\n        { studentId: 'STU006', marks: 38, grade: 'B', percentage: 76 },\n        { studentId: 'STU007', marks: 47, grade: 'A', percentage: 94 },\n        { studentId: 'STU008', marks: 41, grade: 'B+', percentage: 82 },\n      ],\n    },\n    {\n      id: 'EXM015',\n      courseId: 'CS101',\n      name: 'Programming Fundamentals Quiz',\n      type: 'quiz',\n      date: '2024-02-20',\n      time: '11:00-11:30',\n      duration: 30,\n      room: 'CS-101',\n      totalMarks: 20,\n      instructions: 'Quick assessment of basic programming concepts.',\n      syllabus: 'Variables, loops, functions, arrays',\n      status: 'completed',\n      results: [\n        { studentId: 'STU001', marks: 18, grade: 'A', percentage: 90 },\n        { studentId: 'STU004', marks: 17, grade: 'A-', percentage: 85 },\n        { studentId: 'STU005', marks: 16, grade: 'B+', percentage: 80 },\n        { studentId: 'STU007', marks: 19, grade: 'A', percentage: 95 },\n        { studentId: 'STU008', marks: 15, grade: 'B', percentage: 75 },\n      ],\n    },\n  ],\n\n  // Admin user\n  admin: {\n    id: 'ADM001',\n    name: 'Admin User',\n    email: '<EMAIL>',\n    password: 'admin123',\n    role: 'admin',\n    phone: '+1234567896',\n    address: 'University Administration Building',\n    status: 'active',\n  },\n};\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAG;EACtBC,QAAQ,EAAE,CACR;IACEC,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,yBAAyB;IAChCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,yBAAyB;IACrCC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,6BAA6B;IACpCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,aAAa;IACzBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,8BAA8B;IACrCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,uCAAuC;IAChDC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,sBAAsB;IAClCC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,+BAA+B;IACtCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,8BAA8B;IACvCC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,sBAAsB;IAClCC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,6BAA6B;IACpCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,2CAA2C;IACpDC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,sBAAsB;IAClCC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,mCAAmC;IAC5CC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,sBAAsB;IAClCC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,8BAA8B;IACrCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,8BAA8B;IACvCC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,sBAAsB;IAClCC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,CACF;EAEDC,OAAO,EAAE,CACP;IACEd,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,6BAA6B;IACpCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,kCAAkC;IAC3CI,UAAU,EAAE,kBAAkB;IAC9BK,WAAW,EAAE,WAAW;IACxBC,aAAa,EAAE,2BAA2B;IAC1CC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,gCAAgC;IAChDC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,KAAK;IACbR,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,6BAA6B;IACpCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,+BAA+B;IACxCI,UAAU,EAAE,yBAAyB;IACrCK,WAAW,EAAE,qBAAqB;IAClCC,aAAa,EAAE,kCAAkC;IACjDC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,iCAAiC;IACjDC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,KAAK;IACbR,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,4BAA4B;IACnCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,+BAA+B;IACxCI,UAAU,EAAE,aAAa;IACzBK,WAAW,EAAE,qBAAqB;IAClCC,aAAa,EAAE,iCAAiC;IAChDC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,iCAAiC;IACjDC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,KAAK;IACbR,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,CACF;EAEDQ,OAAO,EAAE,CACP;IACErB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,6BAA6B;IACnCqB,IAAI,EAAE,OAAO;IACbZ,UAAU,EAAE,kBAAkB;IAC9Ba,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,yCAAyC;IACtDC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC;MACvCC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,iBAAiB;IACvBqB,IAAI,EAAE,OAAO;IACbZ,UAAU,EAAE,kBAAkB;IAC9Ba,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,yCAAyC;IACtDC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;MAC7BC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,uBAAuB;IAC7BqB,IAAI,EAAE,QAAQ;IACdZ,UAAU,EAAE,yBAAyB;IACrCa,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,mCAAmC;IAChDC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;MAC7BC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,yBAAyB;IAC/BqB,IAAI,EAAE,QAAQ;IACdZ,UAAU,EAAE,aAAa;IACzBa,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,0CAA0C;IACvDC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;MACvCC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,sCAAsC;IAC5CqB,IAAI,EAAE,OAAO;IACbZ,UAAU,EAAE,sBAAsB;IAClCa,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,+DAA+D;IAC5EC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC;MACvCC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,6BAA6B;IACnCqB,IAAI,EAAE,OAAO;IACbZ,UAAU,EAAE,sBAAsB;IAClCa,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,kDAAkD;IAC/DC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;MAC7BC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,0BAA0B;IAChCqB,IAAI,EAAE,OAAO;IACbZ,UAAU,EAAE,sBAAsB;IAClCa,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,sDAAsD;IACnEC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;MAC7BC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,6BAA6B;IACnCqB,IAAI,EAAE,OAAO;IACbZ,UAAU,EAAE,sBAAsB;IAClCa,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,2DAA2D;IACxEC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;MAC3BC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,CACF;EAEDoB,WAAW,EAAE,CACX;IACEhC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC;EACD;EACA;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,CACF;EAEDC,MAAM,EAAE;EACN;EACA;IACEpC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC;EAED;EACA;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC;EAED;EACA;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,IAAI;IACThC,MAAM,EAAE;EACV,CAAC;EAED;EACA;EACA;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC;EAED;EACA;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC;EAED;EACA;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC;EAED;EACA;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC;EAED;EACA;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,CACF;EAEDiC,UAAU,EAAE;EACV;EACA;IAAE7C,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,QAAQ;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC7G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,MAAM;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC3G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC;EAE9G;EACA;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,QAAQ;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC7G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC;EAE9G;EACA;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC;EAE/G;EACA;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,MAAM;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC5G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,QAAQ;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,QAAQ;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC/G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,MAAM;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC5G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC;EAE/G;EACA;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,QAAQ;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC7G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,EAC9G;IAAE5B,EAAE,EAAE,QAAQ;IAAEiC,SAAS,EAAE,QAAQ;IAAEC,QAAQ,EAAE,OAAO;IAAEY,IAAI,EAAE,YAAY;IAAElC,MAAM,EAAE,SAAS;IAAEgB,IAAI,EAAE;EAAQ,CAAC,CAC/G;EAEDmB,IAAI,EAAE;EACJ;EACA;IACE/C,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,YAAY;IACzB/C,MAAM,EAAE,MAAM;IACdgD,aAAa,EAAE,eAAe;IAC9BC,aAAa,EAAE;EACjB,CAAC,EACD;IACE7D,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,YAAY;IACzB/C,MAAM,EAAE,MAAM;IACdgD,aAAa,EAAE,aAAa;IAC5BC,aAAa,EAAE;EACjB,CAAC,EACD;IACE7D,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,YAAY;IACzB/C,MAAM,EAAE,MAAM;IACdgD,aAAa,EAAE,eAAe;IAC9BC,aAAa,EAAE;EACjB,CAAC;EAED;EACA;IACE7D,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,YAAY;IACzB/C,MAAM,EAAE,MAAM;IACdgD,aAAa,EAAE,eAAe;IAC9BC,aAAa,EAAE;EACjB,CAAC,EACD;IACE7D,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,YAAY;IACzB/C,MAAM,EAAE,MAAM;IACdgD,aAAa,EAAE,YAAY;IAC3BC,aAAa,EAAE;EACjB,CAAC,EACD;IACE7D,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,YAAY;IACzB/C,MAAM,EAAE,SAAS;IACjBgD,aAAa,EAAE,eAAe;IAC9BC,aAAa,EAAE;EACjB,CAAC;EAED;EACA;IACE7D,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,YAAY;IACzB/C,MAAM,EAAE,MAAM;IACdgD,aAAa,EAAE,eAAe;IAC9BC,aAAa,EAAE;EACjB,CAAC,EACD;IACE7D,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,YAAY;IACzB/C,MAAM,EAAE,SAAS;IACjBgD,aAAa,EAAE,aAAa;IAC5BC,aAAa,EAAE;EACjB,CAAC,EACD;IACE7D,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,IAAI;IACjB/C,MAAM,EAAE,QAAQ;IAChBgD,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE;EACjB,CAAC,CACF;EAEDC,KAAK,EAAE,CACL;IACE9D,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,kBAAkB;IAC5BC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,uBAAuB;IACjC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,kBAAkB;IAC5BC,SAAS,EAAE,eAAe;IAC1BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,uBAAuB;IACjC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE,eAAe;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,6BAA6B;IACvC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,wCAAwC;IAC/CC,MAAM,EAAE,eAAe;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,kBAAkB;IAC5BC,SAAS,EAAE,iBAAiB;IAC5BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,uBAAuB;IACjC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE,mBAAmB;IAC3BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,6BAA6B;IACvC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,8BAA8B;IACrCC,MAAM,EAAE,iBAAiB;IACzBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,oBAAoB;IAC/BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,+BAA+B;IACzC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,yBAAyB;IAChCC,MAAM,EAAE,aAAa;IACrBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,oBAAoB;IAC/BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,gCAAgC;IAC1C3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,sCAAsC;IAC7CC,MAAM,EAAE,mBAAmB;IAC3BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,kBAAkB;IAC7BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,4BAA4B;IACtC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE,uBAAuB;IAC/BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,4BAA4B;IACtC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,0BAA0B;IACjCC,MAAM,EAAE,sBAAsB;IAC9BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,kBAAkB;IAC5BC,SAAS,EAAE,uBAAuB;IAClCC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,uBAAuB;IACjC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,yBAAyB;IAChCC,MAAM,EAAE,mBAAmB;IAC3BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,kBAAkB;IAC7BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,6BAA6B;IACvC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,wCAAwC;IAC/CC,MAAM,EAAE,eAAe;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,qBAAqB;IAChCC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,+BAA+B;IACzC3D,MAAM,EAAE;EACV,CAAC;EACD;EACA;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,6BAA6B;IACpCC,MAAM,EAAE,iBAAiB;IACzBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,kBAAkB;IAC7BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,gCAAgC;IAC1C3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,sCAAsC;IAC7CC,MAAM,EAAE,iBAAiB;IACzBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,uBAAuB;IAClCC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,gCAAgC;IAC1C3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAE,oBAAoB;IAC5BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,eAAe;IAC1BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,gCAAgC;IAC1C3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,kCAAkC;IACzCC,MAAM,EAAE,gBAAgB;IACxBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,iBAAiB;IAC3BC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,4BAA4B;IACtC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,iCAAiC;IACxCC,MAAM,EAAE,YAAY;IACpBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,iBAAiB;IAC3BC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,4BAA4B;IACtC3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,qCAAqC;IAC5CC,MAAM,EAAE,YAAY;IACpBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,iBAAiB;IAC5BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,gCAAgC;IAC1C3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,8BAA8B;IACrCC,MAAM,EAAE,iBAAiB;IACzBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,uBAAuB;IAClCC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,gCAAgC;IAC1C3D,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb+D,KAAK,EAAE,kDAAkD;IACzDC,MAAM,EAAE,gBAAgB;IACxBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,sBAAsB;IAChCC,SAAS,EAAE,uBAAuB;IAClCC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,wBAAwB;IAClC3D,MAAM,EAAE;EACV,CAAC,CACF;EAED4D,aAAa,EAAE;EACb;EACA;IACExE,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBwC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBhB,OAAO,EAAE,YAAY;IACrBiB,UAAU,EAAE,IAAI;IAChB/D,MAAM,EAAE,UAAU;IAClBgE,IAAI,EAAE,CAAC;IACPC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACE9E,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBwC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBhB,OAAO,EAAE,YAAY;IACrBiB,UAAU,EAAE,IAAI;IAChB/D,MAAM,EAAE,UAAU;IAClBgE,IAAI,EAAE,CAAC;IACPC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACE9E,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBwC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBhB,OAAO,EAAE,YAAY;IACrBiB,UAAU,EAAE,IAAI;IAChB/D,MAAM,EAAE,SAAS;IACjBgE,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACE9E,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBwC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBhB,OAAO,EAAE,YAAY;IACrBiB,UAAU,EAAE,IAAI;IAChB/D,MAAM,EAAE,UAAU;IAClBgE,IAAI,EAAE,CAAC;IACPC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACE9E,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBwC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBhB,OAAO,EAAE,YAAY;IACrBiB,UAAU,EAAE,IAAI;IAChB/D,MAAM,EAAE,UAAU;IAClBgE,IAAI,EAAE,CAAC;IACPC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACE9E,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBwC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBhB,OAAO,EAAE,YAAY;IACrBiB,UAAU,EAAE,IAAI;IAChB/D,MAAM,EAAE,SAAS;IACjBgE,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC;EAED;EACA;IACE9E,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBwC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBhB,OAAO,EAAE,YAAY;IACrBiB,UAAU,EAAE,YAAY;IACxB/D,MAAM,EAAE,UAAU;IAClBgE,IAAI,EAAE,CAAC;IACPC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACE9E,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBwC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBhB,OAAO,EAAE,YAAY;IACrBiB,UAAU,EAAE,YAAY;IACxB/D,MAAM,EAAE,UAAU;IAClBgE,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACE9E,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBwC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBhB,OAAO,EAAE,YAAY;IACrBiB,UAAU,EAAE,YAAY;IACxB/D,MAAM,EAAE,UAAU;IAClBgE,IAAI,EAAE,CAAC;IACPC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE;EACf,CAAC,CACF;EAEDC,KAAK,EAAE;EACL;EACA;IACE/E,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,qBAAqB;IAC3B+E,IAAI,EAAE,SAAS;IACflC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,aAAa;IACnBoD,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACftE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,yDAAyD;IACvEC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,mBAAmB;IACzB+E,IAAI,EAAE,OAAO;IACblC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,aAAa;IACnBoD,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACftE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,gFAAgF;IAC9FC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,QAAQ;IACd+E,IAAI,EAAE,MAAM;IACZlC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,UAAU;IAChBoD,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdtE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,oDAAoD;IAClEC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,qBAAqB;IAC3B+E,IAAI,EAAE,SAAS;IACflC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,aAAa;IACnBoD,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACftE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,+DAA+D;IAC7EC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,QAAQ;IAClBjC,IAAI,EAAE,qBAAqB;IAC3B+E,IAAI,EAAE,SAAS;IACflC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,iBAAiB;IACvBoD,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACftE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,wCAAwC;IACtDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,QAAQ;IAClBjC,IAAI,EAAE,mBAAmB;IACzB+E,IAAI,EAAE,OAAO;IACblC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,iBAAiB;IACvBoD,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACftE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,gDAAgD;IAC9DC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,QAAQ;IAClBjC,IAAI,EAAE,mBAAmB;IACzB+E,IAAI,EAAE,SAAS;IACflC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,iBAAiB;IACvBoD,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACftE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,+CAA+C;IAC7DC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,QAAQ;IAClBjC,IAAI,EAAE,mBAAmB;IACzB+E,IAAI,EAAE,OAAO;IACblC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,iBAAiB;IACvBoD,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACftE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,4CAA4C;IAC1DC,QAAQ,EAAE;EACZ,CAAC;EAED;EACA;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,yBAAyB;IAC/B+E,IAAI,EAAE,YAAY;IAClBlC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,mBAAmB;IACzBoD,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,EAAE;IACdtE,MAAM,EAAE,SAAS;IACjBuE,YAAY,EAAE,kDAAkD;IAChEC,QAAQ,EAAE;EACZ,CAAC;EAED;EACA;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,QAAQ;IACd+E,IAAI,EAAE,MAAM;IACZlC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,UAAU;IAChBoD,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdtE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,kCAAkC;IAChDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,QAAQ;IAClBjC,IAAI,EAAE,QAAQ;IACd+E,IAAI,EAAE,MAAM;IACZlC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,iBAAiB;IACvBoD,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdtE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,6BAA6B;IAC3CC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,QAAQ;IAClBjC,IAAI,EAAE,oBAAoB;IAC1B+E,IAAI,EAAE,YAAY;IAClBlC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,mBAAmB;IACzBoD,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,EAAE;IACdtE,MAAM,EAAE,WAAW;IACnBuE,YAAY,EAAE,2CAA2C;IACzDC,QAAQ,EAAE;EACZ,CAAC;EAED;EACA;IACEpF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,iCAAiC;IACvC+E,IAAI,EAAE,OAAO;IACblC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBqD,QAAQ,EAAE,GAAG;IACbpD,IAAI,EAAE,QAAQ;IACdqD,UAAU,EAAE,GAAG;IACfC,YAAY,EAAE,mDAAmD;IACjEC,QAAQ,EAAE,+CAA+C;IACzDxE,MAAM,EAAE,WAAW;IACnByE,OAAO,EAAE,CACP;MAAEpD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,IAAI;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC/D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,IAAI;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC/D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,IAAI;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC/D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,GAAG;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC9D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,IAAI;MAAEoD,UAAU,EAAE;IAAG,CAAC;EAEnE,CAAC,EACD;IACEvF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,yBAAyB;IAC/B+E,IAAI,EAAE,SAAS;IACflC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBqD,QAAQ,EAAE,GAAG;IACbpD,IAAI,EAAE,QAAQ;IACdqD,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,wCAAwC;IACtDC,QAAQ,EAAE,6CAA6C;IACvDxE,MAAM,EAAE,WAAW;IACnByE,OAAO,EAAE,CACP;MAAEpD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,GAAG;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC9D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,IAAI;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC/D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,GAAG;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC9D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,GAAG;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC9D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,IAAI;MAAEoD,UAAU,EAAE;IAAG,CAAC;EAEnE,CAAC,EACD;IACEvF,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,+BAA+B;IACrC+E,IAAI,EAAE,MAAM;IACZlC,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBqD,QAAQ,EAAE,EAAE;IACZpD,IAAI,EAAE,QAAQ;IACdqD,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,iDAAiD;IAC/DC,QAAQ,EAAE,qCAAqC;IAC/CxE,MAAM,EAAE,WAAW;IACnByE,OAAO,EAAE,CACP;MAAEpD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,GAAG;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC9D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,IAAI;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC/D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,IAAI;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC/D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,GAAG;MAAEoD,UAAU,EAAE;IAAG,CAAC,EAC9D;MAAEtD,SAAS,EAAE,QAAQ;MAAEqD,KAAK,EAAE,EAAE;MAAEnD,KAAK,EAAE,GAAG;MAAEoD,UAAU,EAAE;IAAG,CAAC;EAElE,CAAC,CACF;EAED;EACAC,KAAK,EAAE;IACLxF,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,sBAAsB;IAC7BC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,oCAAoC;IAC7CM,MAAM,EAAE;EACV;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}