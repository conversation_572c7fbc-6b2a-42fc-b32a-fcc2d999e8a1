{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\common\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet } from 'react-router-dom';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport { useAuth } from '../../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const {\n    isAuthenticated\n  } = useAuth();\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      onClose: () => setSidebarOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        onMenuClick: () => setSidebarOpen(true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6\",\n        children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"1c2WGl8VJiRyPpb5R2WT5GqVhjU=\", false, function () {\n  return [useAuth];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "Sidebar", "Header", "useAuth", "jsxDEV", "_jsxDEV", "Layout", "_s", "sidebarOpen", "setSidebarOpen", "isAuthenticated", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "onClose", "onMenuClick", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/common/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet } from 'react-router-dom';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport { useAuth } from '../../context/AuthContext';\n\nconst Layout = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { isAuthenticated } = useAuth();\n\n  if (!isAuthenticated) {\n    return <Outlet />;\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Sidebar */}\n      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />\n      \n      {/* Main content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Header */}\n        <Header onMenuClick={() => setSidebarOpen(true)} />\n        \n        {/* Page content */}\n        <main className=\"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6\">\n          <Outlet />\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAEW;EAAgB,CAAC,GAAGP,OAAO,CAAC,CAAC;EAErC,IAAI,CAACO,eAAe,EAAE;IACpB,oBAAOL,OAAA,CAACL,MAAM;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACET,OAAA;IAAKU,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBAExCX,OAAA,CAACJ,OAAO;MAACgB,MAAM,EAAET,WAAY;MAACU,OAAO,EAAEA,CAAA,KAAMT,cAAc,CAAC,KAAK;IAAE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtET,OAAA;MAAKU,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBAEnDX,OAAA,CAACH,MAAM;QAACiB,WAAW,EAAEA,CAAA,KAAMV,cAAc,CAAC,IAAI;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGnDT,OAAA;QAAMU,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACxEX,OAAA,CAACL,MAAM;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACP,EAAA,CAzBID,MAAM;EAAA,QAEkBH,OAAO;AAAA;AAAAiB,EAAA,GAF/Bd,MAAM;AA2BZ,eAAeA,MAAM;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}