{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\library\\\\Library.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../../context/AuthContext';\nimport { useData } from '../../context/DataContext';\nimport { FiBook, FiSearch, FiFilter, FiCalendar, FiUser, FiMapPin, FiAlertTriangle } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Library = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    books,\n    borrowedBooks\n  } = useData();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [activeTab, setActiveTab] = useState('browse');\n\n  // Get unique categories\n  const categories = [...new Set(books.map(book => book.category))];\n\n  // Filter books\n  const filteredBooks = books.filter(book => {\n    const matchesSearch = book.title.toLowerCase().includes(searchTerm.toLowerCase()) || book.author.toLowerCase().includes(searchTerm.toLowerCase()) || book.isbn.includes(searchTerm);\n    const matchesCategory = selectedCategory === 'all' || book.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  // Get user's borrowed books\n  const userBorrowedBooks = borrowedBooks.filter(b => b.studentId === user.id);\n  const currentlyBorrowed = userBorrowedBooks.filter(b => b.status === 'borrowed' || b.status === 'overdue');\n  const borrowHistory = userBorrowedBooks.filter(b => b.status === 'returned');\n  const getStatusColor = status => {\n    switch (status) {\n      case 'available':\n        return 'bg-green-500/20 text-green-400 border-green-500/30';\n      case 'borrowed':\n        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';\n      case 'overdue':\n        return 'bg-red-500/20 text-red-400 border-red-500/30';\n      default:\n        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  const getDaysUntilDue = dueDateString => {\n    const dueDate = new Date(dueDateString);\n    const today = new Date();\n    const diffTime = dueDate - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays < 0) return `${Math.abs(diffDays)} days overdue`;\n    if (diffDays === 0) return 'Due today';\n    if (diffDays === 1) return 'Due tomorrow';\n    return `${diffDays} days left`;\n  };\n  const BookCard = ({\n    book\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modern-card-dark p-6 hover:scale-105 transition-all duration-300\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-bold text-white mb-2\",\n          children: book.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-300 mb-1\",\n          children: [\"by \", book.author]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 mb-2\",\n          children: [book.publisher, \" \\u2022 \", book.edition]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n            className: \"h-4 w-4 text-primary-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-400\",\n            children: book.location\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(book.status)}`,\n        children: book.status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-4 mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-2 bg-dark-800/30 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-400\",\n          children: \"Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg font-semibold text-white\",\n          children: book.availableCopies\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-2 bg-dark-800/30 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-400\",\n          children: \"Total\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg font-semibold text-white\",\n          children: book.totalCopies\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary flex-1 py-2 rounded-xl font-semibold text-sm\",\n        disabled: book.availableCopies === 0,\n        children: book.availableCopies > 0 ? 'Borrow Book' : 'Not Available'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"px-3 py-2 border border-primary-600/30 text-primary-400 rounded-xl hover:bg-primary-600/10 transition-colors duration-200\",\n        children: \"Details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n  const BorrowedBookCard = ({\n    borrowedBook\n  }) => {\n    const book = books.find(b => b.id === borrowedBook.bookId);\n    if (!book) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-primary-600/20 rounded-xl p-6 bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-lg font-bold text-white\",\n            children: book.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300\",\n            children: [\"by \", book.author]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(borrowedBook.status)}`,\n          children: borrowedBook.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Borrowed Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white font-semibold\",\n            children: formatDate(borrowedBook.borrowDate)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Due Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white font-semibold\",\n            children: formatDate(borrowedBook.dueDate)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `font-semibold ${borrowedBook.status === 'overdue' ? 'text-red-400' : 'text-green-400'}`,\n            children: borrowedBook.status === 'borrowed' ? getDaysUntilDue(borrowedBook.dueDate) : borrowedBook.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), borrowedBook.fine > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(FiAlertTriangle, {\n          className: \"h-4 w-4 text-red-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-400 font-semibold\",\n          children: [\"Fine: $\", borrowedBook.fine]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [borrowedBook.status === 'borrowed' && borrowedBook.renewalCount < borrowedBook.maxRenewals && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-primary px-4 py-2 rounded-xl font-semibold text-sm\",\n          children: \"Renew Book\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-4 py-2 border border-primary-600/30 text-primary-400 rounded-xl hover:bg-primary-600/10 transition-colors duration-200 text-sm\",\n          children: \"Return Book\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center lg:text-left animate-fade-in-up\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold gradient-text mb-2 hover-glow\",\n        children: \"Library\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-300 text-lg animate-slide-in-left\",\n        style: {\n          animationDelay: '0.2s'\n        },\n        children: \"Browse books, manage borrowings, and track your reading\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modern-card-dark p-6 hover:scale-105 transition-all duration-300 group\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300\",\n            children: /*#__PURE__*/_jsxDEV(FiBook, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-semibold text-gray-300\",\n              children: \"Total Books\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-white\",\n              children: books.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modern-card-dark p-6 hover:scale-105 transition-all duration-300 group\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300\",\n            children: /*#__PURE__*/_jsxDEV(FiUser, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-semibold text-gray-300\",\n              children: \"Currently Borrowed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-white\",\n              children: currentlyBorrowed.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modern-card-dark p-6 hover:scale-105 transition-all duration-300 group\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-purple-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300\",\n            children: /*#__PURE__*/_jsxDEV(FiCalendar, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-semibold text-gray-300\",\n              children: \"Reading History\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-white\",\n              children: borrowHistory.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modern-card-dark p-6 hover:scale-105 transition-all duration-300 group\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300\",\n            children: /*#__PURE__*/_jsxDEV(FiAlertTriangle, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-semibold text-gray-300\",\n              children: \"Overdue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-white\",\n              children: userBorrowedBooks.filter(b => b.status === 'overdue').length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modern-card-dark p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('browse'),\n          className: `px-4 py-2 rounded-xl font-semibold transition-colors duration-200 ${activeTab === 'browse' ? 'bg-primary-600 text-white' : 'text-gray-400 hover:text-white hover:bg-primary-600/20'}`,\n          children: \"Browse Books\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('borrowed'),\n          className: `px-4 py-2 rounded-xl font-semibold transition-colors duration-200 ${activeTab === 'borrowed' ? 'bg-primary-600 text-white' : 'text-gray-400 hover:text-white hover:bg-primary-600/20'}`,\n          children: [\"My Books (\", currentlyBorrowed.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('history'),\n          className: `px-4 py-2 rounded-xl font-semibold transition-colors duration-200 ${activeTab === 'history' ? 'bg-primary-600 text-white' : 'text-gray-400 hover:text-white hover:bg-primary-600/20'}`,\n          children: \"History\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), activeTab === 'browse' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modern-card-dark p-6 animate-scale-in hover-lift\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group animate-slide-in-left\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search books, authors, or ISBN...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"modern-input-dark w-full pl-10 pr-4 py-3 border-2 border-primary-600/30 rounded-xl font-medium hover-lift transition-all duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 animate-slide-in-right\",\n            children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n              className: \"h-4 w-4 text-primary-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              className: \"modern-input-dark border-2 border-primary-600/30 rounded-xl px-4 py-3 font-medium flex-1 hover-lift transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                className: \"bg-dark-800 text-white\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category,\n                className: \"bg-dark-800 text-white\",\n                children: category\n              }, category, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: filteredBooks.map(book => /*#__PURE__*/_jsxDEV(BookCard, {\n          book: book\n        }, book.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), activeTab === 'borrowed' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: currentlyBorrowed.length > 0 ? currentlyBorrowed.map(borrowedBook => /*#__PURE__*/_jsxDEV(BorrowedBookCard, {\n        borrowedBook: borrowedBook\n      }, borrowedBook.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 15\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 modern-card-dark\",\n        children: [/*#__PURE__*/_jsxDEV(FiBook, {\n          className: \"h-12 w-12 text-primary-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white mb-2\",\n          children: \"No books borrowed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"You haven't borrowed any books yet.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 9\n    }, this), activeTab === 'history' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: borrowHistory.length > 0 ? borrowHistory.map(borrowedBook => /*#__PURE__*/_jsxDEV(BorrowedBookCard, {\n        borrowedBook: borrowedBook\n      }, borrowedBook.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 15\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 modern-card-dark\",\n        children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n          className: \"h-12 w-12 text-primary-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white mb-2\",\n          children: \"No reading history\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400\",\n          children: \"Your reading history will appear here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n};\n_s(Library, \"osX/clD3Hc/Nno2Lyu2545pU354=\", false, function () {\n  return [useAuth, useData];\n});\n_c = Library;\nexport default Library;\nvar _c;\n$RefreshReg$(_c, \"Library\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "useData", "FiBook", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "FiCalendar", "FiUser", "FiMapPin", "FiAlertTriangle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Library", "_s", "user", "books", "borrowedBooks", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "activeTab", "setActiveTab", "categories", "Set", "map", "book", "category", "filteredBooks", "filter", "matchesSearch", "title", "toLowerCase", "includes", "author", "isbn", "matchesCategory", "userBorrowedBooks", "b", "studentId", "id", "currentlyBorrowed", "status", "borrowHistory", "getStatusColor", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "getDaysUntilDue", "dueDateString", "dueDate", "today", "diffTime", "diffDays", "Math", "ceil", "abs", "BookCard", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "publisher", "edition", "location", "availableCopies", "totalCopies", "disabled", "BorrowedBookCard", "borrowedBook", "find", "bookId", "borrowDate", "fine", "renewalCount", "max<PERSON><PERSON><PERSON><PERSON>", "style", "animationDelay", "length", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/library/Library.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../../context/AuthContext';\nimport { useData } from '../../context/DataContext';\nimport { FiBook, FiSearch, FiFilter, FiCalendar, FiUser, FiMapPin, FiAlertTriangle } from 'react-icons/fi';\n\nconst Library = () => {\n  const { user } = useAuth();\n  const { books, borrowedBooks } = useData();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [activeTab, setActiveTab] = useState('browse');\n\n  // Get unique categories\n  const categories = [...new Set(books.map(book => book.category))];\n\n  // Filter books\n  const filteredBooks = books.filter(book => {\n    const matchesSearch = book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         book.isbn.includes(searchTerm);\n    const matchesCategory = selectedCategory === 'all' || book.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  // Get user's borrowed books\n  const userBorrowedBooks = borrowedBooks.filter(b => b.studentId === user.id);\n  const currentlyBorrowed = userBorrowedBooks.filter(b => b.status === 'borrowed' || b.status === 'overdue');\n  const borrowHistory = userBorrowedBooks.filter(b => b.status === 'returned');\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'available':\n        return 'bg-green-500/20 text-green-400 border-green-500/30';\n      case 'borrowed':\n        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';\n      case 'overdue':\n        return 'bg-red-500/20 text-red-400 border-red-500/30';\n      default:\n        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysUntilDue = (dueDateString) => {\n    const dueDate = new Date(dueDateString);\n    const today = new Date();\n    const diffTime = dueDate - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays < 0) return `${Math.abs(diffDays)} days overdue`;\n    if (diffDays === 0) return 'Due today';\n    if (diffDays === 1) return 'Due tomorrow';\n    return `${diffDays} days left`;\n  };\n\n  const BookCard = ({ book }) => (\n    <div className=\"modern-card-dark p-6 hover:scale-105 transition-all duration-300\">\n      <div className=\"flex items-start justify-between mb-4\">\n        <div className=\"flex-1\">\n          <h3 className=\"text-lg font-bold text-white mb-2\">{book.title}</h3>\n          <p className=\"text-gray-300 mb-1\">by {book.author}</p>\n          <p className=\"text-sm text-gray-400 mb-2\">{book.publisher} • {book.edition}</p>\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <FiMapPin className=\"h-4 w-4 text-primary-400\" />\n            <span className=\"text-sm text-gray-400\">{book.location}</span>\n          </div>\n        </div>\n        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(book.status)}`}>\n          {book.status}\n        </span>\n      </div>\n\n      <div className=\"grid grid-cols-2 gap-4 mb-4\">\n        <div className=\"text-center p-2 bg-dark-800/30 rounded-lg\">\n          <p className=\"text-xs text-gray-400\">Available</p>\n          <p className=\"text-lg font-semibold text-white\">{book.availableCopies}</p>\n        </div>\n        <div className=\"text-center p-2 bg-dark-800/30 rounded-lg\">\n          <p className=\"text-xs text-gray-400\">Total</p>\n          <p className=\"text-lg font-semibold text-white\">{book.totalCopies}</p>\n        </div>\n      </div>\n\n      <div className=\"flex items-center space-x-2\">\n        <button \n          className=\"btn-primary flex-1 py-2 rounded-xl font-semibold text-sm\"\n          disabled={book.availableCopies === 0}\n        >\n          {book.availableCopies > 0 ? 'Borrow Book' : 'Not Available'}\n        </button>\n        <button className=\"px-3 py-2 border border-primary-600/30 text-primary-400 rounded-xl hover:bg-primary-600/10 transition-colors duration-200\">\n          Details\n        </button>\n      </div>\n    </div>\n  );\n\n  const BorrowedBookCard = ({ borrowedBook }) => {\n    const book = books.find(b => b.id === borrowedBook.bookId);\n    if (!book) return null;\n\n    return (\n      <div className=\"border border-primary-600/20 rounded-xl p-6 bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200\">\n        <div className=\"flex items-start justify-between mb-4\">\n          <div>\n            <h4 className=\"text-lg font-bold text-white\">{book.title}</h4>\n            <p className=\"text-gray-300\">by {book.author}</p>\n          </div>\n          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(borrowedBook.status)}`}>\n            {borrowedBook.status}\n          </span>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n          <div>\n            <p className=\"text-xs text-gray-400\">Borrowed Date</p>\n            <p className=\"text-white font-semibold\">{formatDate(borrowedBook.borrowDate)}</p>\n          </div>\n          <div>\n            <p className=\"text-xs text-gray-400\">Due Date</p>\n            <p className=\"text-white font-semibold\">{formatDate(borrowedBook.dueDate)}</p>\n          </div>\n          <div>\n            <p className=\"text-xs text-gray-400\">Status</p>\n            <p className={`font-semibold ${borrowedBook.status === 'overdue' ? 'text-red-400' : 'text-green-400'}`}>\n              {borrowedBook.status === 'borrowed' ? getDaysUntilDue(borrowedBook.dueDate) : borrowedBook.status}\n            </p>\n          </div>\n        </div>\n\n        {borrowedBook.fine > 0 && (\n          <div className=\"flex items-center space-x-2 mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg\">\n            <FiAlertTriangle className=\"h-4 w-4 text-red-400\" />\n            <span className=\"text-red-400 font-semibold\">Fine: ${borrowedBook.fine}</span>\n          </div>\n        )}\n\n        <div className=\"flex items-center space-x-2\">\n          {borrowedBook.status === 'borrowed' && borrowedBook.renewalCount < borrowedBook.maxRenewals && (\n            <button className=\"btn-primary px-4 py-2 rounded-xl font-semibold text-sm\">\n              Renew Book\n            </button>\n          )}\n          <button className=\"px-4 py-2 border border-primary-600/30 text-primary-400 rounded-xl hover:bg-primary-600/10 transition-colors duration-200 text-sm\">\n            Return Book\n          </button>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"text-center lg:text-left animate-fade-in-up\">\n        <h1 className=\"text-4xl font-bold gradient-text mb-2 hover-glow\">Library</h1>\n        <p className=\"text-gray-300 text-lg animate-slide-in-left\" style={{animationDelay: '0.2s'}}>Browse books, manage borrowings, and track your reading</p>\n      </div>\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"modern-card-dark p-6 hover:scale-105 transition-all duration-300 group\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-blue-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300\">\n              <FiBook className=\"h-6 w-6 text-white\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-semibold text-gray-300\">Total Books</p>\n              <p className=\"text-3xl font-bold text-white\">{books.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"modern-card-dark p-6 hover:scale-105 transition-all duration-300 group\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-green-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300\">\n              <FiUser className=\"h-6 w-6 text-white\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-semibold text-gray-300\">Currently Borrowed</p>\n              <p className=\"text-3xl font-bold text-white\">{currentlyBorrowed.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"modern-card-dark p-6 hover:scale-105 transition-all duration-300 group\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-purple-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300\">\n              <FiCalendar className=\"h-6 w-6 text-white\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-semibold text-gray-300\">Reading History</p>\n              <p className=\"text-3xl font-bold text-white\">{borrowHistory.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"modern-card-dark p-6 hover:scale-105 transition-all duration-300 group\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-red-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300\">\n              <FiAlertTriangle className=\"h-6 w-6 text-white\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-semibold text-gray-300\">Overdue</p>\n              <p className=\"text-3xl font-bold text-white\">{userBorrowedBooks.filter(b => b.status === 'overdue').length}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"modern-card-dark p-6\">\n        <div className=\"flex space-x-4\">\n          <button\n            onClick={() => setActiveTab('browse')}\n            className={`px-4 py-2 rounded-xl font-semibold transition-colors duration-200 ${\n              activeTab === 'browse' \n                ? 'bg-primary-600 text-white' \n                : 'text-gray-400 hover:text-white hover:bg-primary-600/20'\n            }`}\n          >\n            Browse Books\n          </button>\n          <button\n            onClick={() => setActiveTab('borrowed')}\n            className={`px-4 py-2 rounded-xl font-semibold transition-colors duration-200 ${\n              activeTab === 'borrowed' \n                ? 'bg-primary-600 text-white' \n                : 'text-gray-400 hover:text-white hover:bg-primary-600/20'\n            }`}\n          >\n            My Books ({currentlyBorrowed.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('history')}\n            className={`px-4 py-2 rounded-xl font-semibold transition-colors duration-200 ${\n              activeTab === 'history' \n                ? 'bg-primary-600 text-white' \n                : 'text-gray-400 hover:text-white hover:bg-primary-600/20'\n            }`}\n          >\n            History\n          </button>\n        </div>\n      </div>\n\n      {/* Browse Books Tab */}\n      {activeTab === 'browse' && (\n        <>\n          {/* Search and Filters */}\n          <div className=\"modern-card-dark p-6 animate-scale-in hover-lift\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"relative group animate-slide-in-left\">\n                <FiSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search books, authors, or ISBN...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"modern-input-dark w-full pl-10 pr-4 py-3 border-2 border-primary-600/30 rounded-xl font-medium hover-lift transition-all duration-300\"\n                />\n              </div>\n              <div className=\"flex items-center space-x-2 animate-slide-in-right\">\n                <FiFilter className=\"h-4 w-4 text-primary-400\" />\n                <select\n                  value={selectedCategory}\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                  className=\"modern-input-dark border-2 border-primary-600/30 rounded-xl px-4 py-3 font-medium flex-1 hover-lift transition-all duration-300\"\n                >\n                  <option value=\"all\" className=\"bg-dark-800 text-white\">All Categories</option>\n                  {categories.map(category => (\n                    <option key={category} value={category} className=\"bg-dark-800 text-white\">\n                      {category}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Books Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredBooks.map(book => (\n              <BookCard key={book.id} book={book} />\n            ))}\n          </div>\n        </>\n      )}\n\n      {/* My Books Tab */}\n      {activeTab === 'borrowed' && (\n        <div className=\"space-y-6\">\n          {currentlyBorrowed.length > 0 ? (\n            currentlyBorrowed.map(borrowedBook => (\n              <BorrowedBookCard key={borrowedBook.id} borrowedBook={borrowedBook} />\n            ))\n          ) : (\n            <div className=\"text-center py-8 modern-card-dark\">\n              <FiBook className=\"h-12 w-12 text-primary-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-white mb-2\">No books borrowed</h3>\n              <p className=\"text-gray-400\">You haven't borrowed any books yet.</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* History Tab */}\n      {activeTab === 'history' && (\n        <div className=\"space-y-6\">\n          {borrowHistory.length > 0 ? (\n            borrowHistory.map(borrowedBook => (\n              <BorrowedBookCard key={borrowedBook.id} borrowedBook={borrowedBook} />\n            ))\n          ) : (\n            <div className=\"text-center py-8 modern-card-dark\">\n              <FiCalendar className=\"h-12 w-12 text-primary-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-white mb-2\">No reading history</h3>\n              <p className=\"text-gray-400\">Your reading history will appear here.</p>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Library;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3G,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEgB,KAAK;IAAEC;EAAc,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAC1C,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,QAAQ,CAAC;;EAEpD;EACA,MAAMyB,UAAU,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACT,KAAK,CAACU,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC;;EAEjE;EACA,MAAMC,aAAa,GAAGb,KAAK,CAACc,MAAM,CAACH,IAAI,IAAI;IACzC,MAAMI,aAAa,GAAGJ,IAAI,CAACK,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,CAAC,CAAC,CAAC,IAC5DN,IAAI,CAACQ,MAAM,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,CAAC,CAAC,CAAC,IAC5DN,IAAI,CAACS,IAAI,CAACF,QAAQ,CAAChB,UAAU,CAAC;IACnD,MAAMmB,eAAe,GAAGjB,gBAAgB,KAAK,KAAK,IAAIO,IAAI,CAACC,QAAQ,KAAKR,gBAAgB;IACxF,OAAOW,aAAa,IAAIM,eAAe;EACzC,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAGrB,aAAa,CAACa,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKzB,IAAI,CAAC0B,EAAE,CAAC;EAC5E,MAAMC,iBAAiB,GAAGJ,iBAAiB,CAACR,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACI,MAAM,KAAK,UAAU,IAAIJ,CAAC,CAACI,MAAM,KAAK,SAAS,CAAC;EAC1G,MAAMC,aAAa,GAAGN,iBAAiB,CAACR,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACI,MAAM,KAAK,UAAU,CAAC;EAE5E,MAAME,cAAc,GAAIF,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,oDAAoD;MAC7D,KAAK,UAAU;QACb,OAAO,iDAAiD;MAC1D,KAAK,SAAS;QACZ,OAAO,8CAA8C;MACvD;QACE,OAAO,iDAAiD;IAC5D;EACF,CAAC;EAED,MAAMG,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,eAAe,GAAIC,aAAa,IAAK;IACzC,MAAMC,OAAO,GAAG,IAAIP,IAAI,CAACM,aAAa,CAAC;IACvC,MAAME,KAAK,GAAG,IAAIR,IAAI,CAAC,CAAC;IACxB,MAAMS,QAAQ,GAAGF,OAAO,GAAGC,KAAK;IAChC,MAAME,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIC,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGC,IAAI,CAACE,GAAG,CAACH,QAAQ,CAAC,eAAe;IAC7D,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,WAAW;IACtC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,cAAc;IACzC,OAAO,GAAGA,QAAQ,YAAY;EAChC,CAAC;EAED,MAAMI,QAAQ,GAAGA,CAAC;IAAEnC;EAAK,CAAC,kBACxBjB,OAAA;IAAKqD,SAAS,EAAC,kEAAkE;IAAAC,QAAA,gBAC/EtD,OAAA;MAAKqD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDtD,OAAA;QAAKqD,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBtD,OAAA;UAAIqD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAErC,IAAI,CAACK;QAAK;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnE1D,OAAA;UAAGqD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAC,KAAG,EAACrC,IAAI,CAACQ,MAAM;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtD1D,OAAA;UAAGqD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAErC,IAAI,CAAC0C,SAAS,EAAC,UAAG,EAAC1C,IAAI,CAAC2C,OAAO;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/E1D,OAAA;UAAKqD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CtD,OAAA,CAACH,QAAQ;YAACwD,SAAS,EAAC;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjD1D,OAAA;YAAMqD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAErC,IAAI,CAAC4C;UAAQ;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN1D,OAAA;QAAMqD,SAAS,EAAE,gFAAgFlB,cAAc,CAAClB,IAAI,CAACgB,MAAM,CAAC,EAAG;QAAAqB,QAAA,EAC5HrC,IAAI,CAACgB;MAAM;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEN1D,OAAA;MAAKqD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CtD,OAAA;QAAKqD,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDtD,OAAA;UAAGqD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClD1D,OAAA;UAAGqD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAErC,IAAI,CAAC6C;QAAe;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACN1D,OAAA;QAAKqD,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDtD,OAAA;UAAGqD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9C1D,OAAA;UAAGqD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAErC,IAAI,CAAC8C;QAAW;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1D,OAAA;MAAKqD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CtD,OAAA;QACEqD,SAAS,EAAC,0DAA0D;QACpEW,QAAQ,EAAE/C,IAAI,CAAC6C,eAAe,KAAK,CAAE;QAAAR,QAAA,EAEpCrC,IAAI,CAAC6C,eAAe,GAAG,CAAC,GAAG,aAAa,GAAG;MAAe;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACT1D,OAAA;QAAQqD,SAAS,EAAC,2HAA2H;QAAAC,QAAA,EAAC;MAE9I;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMO,gBAAgB,GAAGA,CAAC;IAAEC;EAAa,CAAC,KAAK;IAC7C,MAAMjD,IAAI,GAAGX,KAAK,CAAC6D,IAAI,CAACtC,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKmC,YAAY,CAACE,MAAM,CAAC;IAC1D,IAAI,CAACnD,IAAI,EAAE,OAAO,IAAI;IAEtB,oBACEjB,OAAA;MAAKqD,SAAS,EAAC,qHAAqH;MAAAC,QAAA,gBAClItD,OAAA;QAAKqD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDtD,OAAA;UAAAsD,QAAA,gBACEtD,OAAA;YAAIqD,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAErC,IAAI,CAACK;UAAK;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9D1D,OAAA;YAAGqD,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,KAAG,EAACrC,IAAI,CAACQ,MAAM;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACN1D,OAAA;UAAMqD,SAAS,EAAE,gFAAgFlB,cAAc,CAAC+B,YAAY,CAACjC,MAAM,CAAC,EAAG;UAAAqB,QAAA,EACpIY,YAAY,CAACjC;QAAM;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN1D,OAAA;QAAKqD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDtD,OAAA;UAAAsD,QAAA,gBACEtD,OAAA;YAAGqD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtD1D,OAAA;YAAGqD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAElB,UAAU,CAAC8B,YAAY,CAACG,UAAU;UAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACN1D,OAAA;UAAAsD,QAAA,gBACEtD,OAAA;YAAGqD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjD1D,OAAA;YAAGqD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAElB,UAAU,CAAC8B,YAAY,CAACrB,OAAO;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACN1D,OAAA;UAAAsD,QAAA,gBACEtD,OAAA;YAAGqD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/C1D,OAAA;YAAGqD,SAAS,EAAE,iBAAiBa,YAAY,CAACjC,MAAM,KAAK,SAAS,GAAG,cAAc,GAAG,gBAAgB,EAAG;YAAAqB,QAAA,EACpGY,YAAY,CAACjC,MAAM,KAAK,UAAU,GAAGU,eAAe,CAACuB,YAAY,CAACrB,OAAO,CAAC,GAAGqB,YAAY,CAACjC;UAAM;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELQ,YAAY,CAACI,IAAI,GAAG,CAAC,iBACpBtE,OAAA;QAAKqD,SAAS,EAAC,wFAAwF;QAAAC,QAAA,gBACrGtD,OAAA,CAACF,eAAe;UAACuD,SAAS,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpD1D,OAAA;UAAMqD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,SAAO,EAACY,YAAY,CAACI,IAAI;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CACN,eAED1D,OAAA;QAAKqD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,GACzCY,YAAY,CAACjC,MAAM,KAAK,UAAU,IAAIiC,YAAY,CAACK,YAAY,GAAGL,YAAY,CAACM,WAAW,iBACzFxE,OAAA;UAAQqD,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAE3E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACD1D,OAAA;UAAQqD,SAAS,EAAC,mIAAmI;UAAAC,QAAA,EAAC;QAEtJ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACE1D,OAAA;IAAKqD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBtD,OAAA;MAAKqD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DtD,OAAA;QAAIqD,SAAS,EAAC,kDAAkD;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7E1D,OAAA;QAAGqD,SAAS,EAAC,6CAA6C;QAACoB,KAAK,EAAE;UAACC,cAAc,EAAE;QAAM,CAAE;QAAApB,QAAA,EAAC;MAAuD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpJ,CAAC,eAGN1D,OAAA;MAAKqD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDtD,OAAA;QAAKqD,SAAS,EAAC,wEAAwE;QAAAC,QAAA,eACrFtD,OAAA;UAAKqD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtD,OAAA;YAAKqD,SAAS,EAAC,8FAA8F;YAAAC,QAAA,eAC3GtD,OAAA,CAACR,MAAM;cAAC6D,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACN1D,OAAA;YAAKqD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBtD,OAAA;cAAGqD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClE1D,OAAA;cAAGqD,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAEhD,KAAK,CAACqE;YAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1D,OAAA;QAAKqD,SAAS,EAAC,wEAAwE;QAAAC,QAAA,eACrFtD,OAAA;UAAKqD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtD,OAAA;YAAKqD,SAAS,EAAC,+FAA+F;YAAAC,QAAA,eAC5GtD,OAAA,CAACJ,MAAM;cAACyD,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACN1D,OAAA;YAAKqD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBtD,OAAA;cAAGqD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzE1D,OAAA;cAAGqD,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAEtB,iBAAiB,CAAC2C;YAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1D,OAAA;QAAKqD,SAAS,EAAC,wEAAwE;QAAAC,QAAA,eACrFtD,OAAA;UAAKqD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtD,OAAA;YAAKqD,SAAS,EAAC,gGAAgG;YAAAC,QAAA,eAC7GtD,OAAA,CAACL,UAAU;cAAC0D,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN1D,OAAA;YAAKqD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBtD,OAAA;cAAGqD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtE1D,OAAA;cAAGqD,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAEpB,aAAa,CAACyC;YAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1D,OAAA;QAAKqD,SAAS,EAAC,wEAAwE;QAAAC,QAAA,eACrFtD,OAAA;UAAKqD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtD,OAAA;YAAKqD,SAAS,EAAC,6FAA6F;YAAAC,QAAA,eAC1GtD,OAAA,CAACF,eAAe;cAACuD,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN1D,OAAA;YAAKqD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBtD,OAAA;cAAGqD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9D1D,OAAA;cAAGqD,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAE1B,iBAAiB,CAACR,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACI,MAAM,KAAK,SAAS,CAAC,CAAC0C;YAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5G,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1D,OAAA;MAAKqD,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnCtD,OAAA;QAAKqD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BtD,OAAA;UACE4E,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAAC,QAAQ,CAAE;UACtCwC,SAAS,EAAE,qEACTzC,SAAS,KAAK,QAAQ,GAClB,2BAA2B,GAC3B,wDAAwD,EAC3D;UAAA0C,QAAA,EACJ;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1D,OAAA;UACE4E,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAAC,UAAU,CAAE;UACxCwC,SAAS,EAAE,qEACTzC,SAAS,KAAK,UAAU,GACpB,2BAA2B,GAC3B,wDAAwD,EAC3D;UAAA0C,QAAA,GACJ,YACW,EAACtB,iBAAiB,CAAC2C,MAAM,EAAC,GACtC;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1D,OAAA;UACE4E,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAAC,SAAS,CAAE;UACvCwC,SAAS,EAAE,qEACTzC,SAAS,KAAK,SAAS,GACnB,2BAA2B,GAC3B,wDAAwD,EAC3D;UAAA0C,QAAA,EACJ;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9C,SAAS,KAAK,QAAQ,iBACrBZ,OAAA,CAAAE,SAAA;MAAAoD,QAAA,gBAEEtD,OAAA;QAAKqD,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/DtD,OAAA;UAAKqD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDtD,OAAA;YAAKqD,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnDtD,OAAA,CAACP,QAAQ;cAAC4D,SAAS,EAAC;YAAyI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChK1D,OAAA;cACE6E,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,mCAAmC;cAC/CC,KAAK,EAAEvE,UAAW;cAClBwE,QAAQ,EAAGC,CAAC,IAAKxE,aAAa,CAACwE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/C1B,SAAS,EAAC;YAAuI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN1D,OAAA;YAAKqD,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjEtD,OAAA,CAACN,QAAQ;cAAC2D,SAAS,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD1D,OAAA;cACE+E,KAAK,EAAErE,gBAAiB;cACxBsE,QAAQ,EAAGC,CAAC,IAAKtE,mBAAmB,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACrD1B,SAAS,EAAC,iIAAiI;cAAAC,QAAA,gBAE3ItD,OAAA;gBAAQ+E,KAAK,EAAC,KAAK;gBAAC1B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC7E5C,UAAU,CAACE,GAAG,CAACE,QAAQ,iBACtBlB,OAAA;gBAAuB+E,KAAK,EAAE7D,QAAS;gBAACmC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACvEpC;cAAQ,GADEA,QAAQ;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEb,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA;QAAKqD,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEnC,aAAa,CAACH,GAAG,CAACC,IAAI,iBACrBjB,OAAA,CAACoD,QAAQ;UAAenC,IAAI,EAAEA;QAAK,GAApBA,IAAI,CAACc,EAAE;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,eACN,CACH,EAGA9C,SAAS,KAAK,UAAU,iBACvBZ,OAAA;MAAKqD,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBtB,iBAAiB,CAAC2C,MAAM,GAAG,CAAC,GAC3B3C,iBAAiB,CAAChB,GAAG,CAACkD,YAAY,iBAChClE,OAAA,CAACiE,gBAAgB;QAAuBC,YAAY,EAAEA;MAAa,GAA5CA,YAAY,CAACnC,EAAE;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA+B,CACtE,CAAC,gBAEF1D,OAAA;QAAKqD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDtD,OAAA,CAACR,MAAM;UAAC6D,SAAS,EAAC;QAAyC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9D1D,OAAA;UAAIqD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E1D,OAAA;UAAGqD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGA9C,SAAS,KAAK,SAAS,iBACtBZ,OAAA;MAAKqD,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBpB,aAAa,CAACyC,MAAM,GAAG,CAAC,GACvBzC,aAAa,CAAClB,GAAG,CAACkD,YAAY,iBAC5BlE,OAAA,CAACiE,gBAAgB;QAAuBC,YAAY,EAAEA;MAAa,GAA5CA,YAAY,CAACnC,EAAE;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA+B,CACtE,CAAC,gBAEF1D,OAAA;QAAKqD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDtD,OAAA,CAACL,UAAU;UAAC0D,SAAS,EAAC;QAAyC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClE1D,OAAA;UAAIqD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E1D,OAAA;UAAGqD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtD,EAAA,CAtUID,OAAO;EAAA,QACMb,OAAO,EACSC,OAAO;AAAA;AAAA4F,EAAA,GAFpChF,OAAO;AAwUb,eAAeA,OAAO;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}