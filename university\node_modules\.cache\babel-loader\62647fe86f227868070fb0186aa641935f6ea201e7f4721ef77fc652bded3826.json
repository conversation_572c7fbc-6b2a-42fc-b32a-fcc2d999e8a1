{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\dashboard\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { FiUsers, FiBookOpen, FiDollarSign, FiUser, FiCalendar, FiBook, FiFileText } from 'react-icons/fi';\nimport { useData } from '../../context/DataContext';\nimport { formatCurrency, calculatePercentage } from '../../utils/helpers';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const {\n    students,\n    faculty,\n    courses,\n    fees,\n    books,\n    borrowedBooks,\n    exams\n  } = useData();\n\n  // Calculate statistics\n  const totalStudents = students.length;\n  const activeStudents = students.filter(s => s.status === 'active').length;\n  const totalFaculty = faculty.length;\n  const activeFaculty = faculty.filter(f => f.status === 'active').length;\n  const totalCourses = courses.length;\n  const activeCourses = courses.filter(c => c.status === 'active').length;\n  const totalFees = fees.reduce((sum, fee) => sum + fee.totalAmount, 0);\n  const paidFees = fees.reduce((sum, fee) => sum + fee.paidAmount, 0);\n  const pendingFees = totalFees - paidFees;\n  const totalBooks = books.reduce((sum, book) => sum + book.totalCopies, 0);\n  const borrowedBooksCount = borrowedBooks.filter(b => b.status === 'borrowed').length;\n  const availableBooks = totalBooks - borrowedBooksCount;\n  const upcomingExams = exams.filter(exam => {\n    const examDate = new Date(exam.date);\n    const today = new Date();\n    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n    return examDate >= today && examDate <= nextWeek;\n  }).length;\n  const stats = [{\n    name: 'Total Students',\n    value: totalStudents,\n    subValue: `${activeStudents} active`,\n    icon: FiUsers,\n    color: 'bg-blue-500',\n    change: '+12%',\n    changeType: 'increase'\n  }, {\n    name: 'Faculty Members',\n    value: totalFaculty,\n    subValue: `${activeFaculty} active`,\n    icon: FiUser,\n    color: 'bg-green-500',\n    change: '+3%',\n    changeType: 'increase'\n  }, {\n    name: 'Active Courses',\n    value: activeCourses,\n    subValue: `${totalCourses} total`,\n    icon: FiBookOpen,\n    color: 'bg-purple-500',\n    change: '+8%',\n    changeType: 'increase'\n  }, {\n    name: 'Fee Collection',\n    value: formatCurrency(paidFees),\n    subValue: `${formatCurrency(pendingFees)} pending`,\n    icon: FiDollarSign,\n    color: 'bg-yellow-500',\n    change: '+15%',\n    changeType: 'increase'\n  }, {\n    name: 'Library Books',\n    value: totalBooks,\n    subValue: `${availableBooks} available`,\n    icon: FiBook,\n    color: 'bg-indigo-500',\n    change: '+5%',\n    changeType: 'increase'\n  }, {\n    name: 'Upcoming Exams',\n    value: upcomingExams,\n    subValue: 'This week',\n    icon: FiFileText,\n    color: 'bg-red-500',\n    change: '3 scheduled',\n    changeType: 'neutral'\n  }];\n  const recentActivities = [{\n    id: 1,\n    type: 'enrollment',\n    message: 'New student John Doe enrolled in Computer Science',\n    time: '2 hours ago',\n    icon: FiUsers,\n    color: 'text-blue-600'\n  }, {\n    id: 2,\n    type: 'course',\n    message: 'Dr. Sarah Wilson created new course \"Advanced Algorithms\"',\n    time: '4 hours ago',\n    icon: FiBookOpen,\n    color: 'text-green-600'\n  }, {\n    id: 3,\n    type: 'payment',\n    message: 'Fee payment received from Jane Smith - $5,350',\n    time: '6 hours ago',\n    icon: FiDollarSign,\n    color: 'text-yellow-600'\n  }, {\n    id: 4,\n    type: 'exam',\n    message: 'Midterm exam scheduled for CS101 on Feb 15',\n    time: '1 day ago',\n    icon: FiCalendar,\n    color: 'text-purple-600'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center lg:text-left\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold gradient-text mb-2\",\n        children: \"Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-300 text-lg\",\n        children: \"Welcome back! Here's what's happening at your university.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `${stat.color} p-3 rounded-lg`,\n            children: /*#__PURE__*/_jsxDEV(stat.icon, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4 flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: stat.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: stat.subValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-sm font-medium ${stat.changeType === 'increase' ? 'text-green-600' : stat.changeType === 'decrease' ? 'text-red-600' : 'text-gray-600'}`,\n              children: stat.change\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left\",\n            children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n              className: \"h-6 w-6 text-blue-600 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium text-gray-900\",\n              children: \"Add Student\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Register new student\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left\",\n            children: [/*#__PURE__*/_jsxDEV(FiUser, {\n              className: \"h-6 w-6 text-green-600 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium text-gray-900\",\n              children: \"Add Faculty\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Add faculty member\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left\",\n            children: [/*#__PURE__*/_jsxDEV(FiBookOpen, {\n              className: \"h-6 w-6 text-purple-600 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium text-gray-900\",\n              children: \"Create Course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Add new course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left\",\n            children: [/*#__PURE__*/_jsxDEV(FiFileText, {\n              className: \"h-6 w-6 text-red-600 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium text-gray-900\",\n              children: \"Schedule Exam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Create exam schedule\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Recent Activities\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: recentActivities.map(activity => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-2 rounded-lg bg-gray-100`,\n              children: /*#__PURE__*/_jsxDEV(activity.icon, {\n                className: `h-4 w-4 ${activity.color}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 min-w-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-900\",\n                children: activity.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: activity.time\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, activity.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n            children: \"View all activities \\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Performance Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-blue-600\",\n            children: [calculatePercentage(activeStudents, totalStudents), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Student Retention\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-green-600\",\n            children: [calculatePercentage(paidFees, totalFees), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Fee Collection\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-purple-600\",\n            children: [calculatePercentage(activeCourses, totalCourses), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Course Completion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-yellow-600\",\n            children: [calculatePercentage(availableBooks, totalBooks), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Library Utilization\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"CqEDeBH1UJ+Abp/+UFW7uc2Jeic=\", false, function () {\n  return [useData];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "FiUsers", "FiBookOpen", "FiDollarSign", "FiUser", "FiCalendar", "FiBook", "FiFileText", "useData", "formatCurrency", "calculatePercentage", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "students", "faculty", "courses", "fees", "books", "borrowedBooks", "exams", "totalStudents", "length", "activeStudents", "filter", "s", "status", "totalFaculty", "activeFaculty", "f", "totalCourses", "activeCourses", "c", "totalFees", "reduce", "sum", "fee", "totalAmount", "paidFees", "paidAmount", "pendingFees", "totalBooks", "book", "totalCopies", "borrowedBooksCount", "b", "availableBooks", "upcomingExams", "exam", "examDate", "Date", "date", "today", "nextWeek", "getTime", "stats", "name", "value", "subValue", "icon", "color", "change", "changeType", "recentActivities", "id", "type", "message", "time", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "stat", "index", "activity", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/dashboard/AdminDashboard.js"], "sourcesContent": ["import React from 'react';\nimport {\n  FiUsers,\n  FiBookOpen,\n  FiDollarSign,\n  FiUser,\n  FiCalendar,\n  FiBook,\n  FiFileText\n} from 'react-icons/fi';\nimport { useData } from '../../context/DataContext';\nimport { formatCurrency, calculatePercentage } from '../../utils/helpers';\n\nconst AdminDashboard = () => {\n  const {\n    students,\n    faculty,\n    courses,\n    fees,\n    books,\n    borrowedBooks,\n    exams\n  } = useData();\n\n  // Calculate statistics\n  const totalStudents = students.length;\n  const activeStudents = students.filter(s => s.status === 'active').length;\n  const totalFaculty = faculty.length;\n  const activeFaculty = faculty.filter(f => f.status === 'active').length;\n  const totalCourses = courses.length;\n  const activeCourses = courses.filter(c => c.status === 'active').length;\n  \n  const totalFees = fees.reduce((sum, fee) => sum + fee.totalAmount, 0);\n  const paidFees = fees.reduce((sum, fee) => sum + fee.paidAmount, 0);\n  const pendingFees = totalFees - paidFees;\n  \n  const totalBooks = books.reduce((sum, book) => sum + book.totalCopies, 0);\n  const borrowedBooksCount = borrowedBooks.filter(b => b.status === 'borrowed').length;\n  const availableBooks = totalBooks - borrowedBooksCount;\n\n  const upcomingExams = exams.filter(exam => {\n    const examDate = new Date(exam.date);\n    const today = new Date();\n    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n    return examDate >= today && examDate <= nextWeek;\n  }).length;\n\n  const stats = [\n    {\n      name: 'Total Students',\n      value: totalStudents,\n      subValue: `${activeStudents} active`,\n      icon: FiUsers,\n      color: 'bg-blue-500',\n      change: '+12%',\n      changeType: 'increase'\n    },\n    {\n      name: 'Faculty Members',\n      value: totalFaculty,\n      subValue: `${activeFaculty} active`,\n      icon: FiUser,\n      color: 'bg-green-500',\n      change: '+3%',\n      changeType: 'increase'\n    },\n    {\n      name: 'Active Courses',\n      value: activeCourses,\n      subValue: `${totalCourses} total`,\n      icon: FiBookOpen,\n      color: 'bg-purple-500',\n      change: '+8%',\n      changeType: 'increase'\n    },\n    {\n      name: 'Fee Collection',\n      value: formatCurrency(paidFees),\n      subValue: `${formatCurrency(pendingFees)} pending`,\n      icon: FiDollarSign,\n      color: 'bg-yellow-500',\n      change: '+15%',\n      changeType: 'increase'\n    },\n    {\n      name: 'Library Books',\n      value: totalBooks,\n      subValue: `${availableBooks} available`,\n      icon: FiBook,\n      color: 'bg-indigo-500',\n      change: '+5%',\n      changeType: 'increase'\n    },\n    {\n      name: 'Upcoming Exams',\n      value: upcomingExams,\n      subValue: 'This week',\n      icon: FiFileText,\n      color: 'bg-red-500',\n      change: '3 scheduled',\n      changeType: 'neutral'\n    }\n  ];\n\n  const recentActivities = [\n    {\n      id: 1,\n      type: 'enrollment',\n      message: 'New student John Doe enrolled in Computer Science',\n      time: '2 hours ago',\n      icon: FiUsers,\n      color: 'text-blue-600'\n    },\n    {\n      id: 2,\n      type: 'course',\n      message: 'Dr. Sarah Wilson created new course \"Advanced Algorithms\"',\n      time: '4 hours ago',\n      icon: FiBookOpen,\n      color: 'text-green-600'\n    },\n    {\n      id: 3,\n      type: 'payment',\n      message: 'Fee payment received from Jane Smith - $5,350',\n      time: '6 hours ago',\n      icon: FiDollarSign,\n      color: 'text-yellow-600'\n    },\n    {\n      id: 4,\n      type: 'exam',\n      message: 'Midterm exam scheduled for CS101 on Feb 15',\n      time: '1 day ago',\n      icon: FiCalendar,\n      color: 'text-purple-600'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"text-center lg:text-left\">\n        <h1 className=\"text-4xl font-bold gradient-text mb-2\">Admin Dashboard</h1>\n        <p className=\"text-gray-300 text-lg\">Welcome back! Here's what's happening at your university.</p>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {stats.map((stat, index) => (\n          <div key={index} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center\">\n              <div className={`${stat.color} p-3 rounded-lg`}>\n                <stat.icon className=\"h-6 w-6 text-white\" />\n              </div>\n              <div className=\"ml-4 flex-1\">\n                <p className=\"text-sm font-medium text-gray-600\">{stat.name}</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stat.value}</p>\n                <p className=\"text-sm text-gray-500\">{stat.subValue}</p>\n              </div>\n              <div className=\"text-right\">\n                <span className={`text-sm font-medium ${\n                  stat.changeType === 'increase' ? 'text-green-600' : \n                  stat.changeType === 'decrease' ? 'text-red-600' : 'text-gray-600'\n                }`}>\n                  {stat.change}\n                </span>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Charts and Recent Activity */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Quick Actions */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n          <div className=\"grid grid-cols-2 gap-4\">\n            <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left\">\n              <FiUsers className=\"h-6 w-6 text-blue-600 mb-2\" />\n              <p className=\"font-medium text-gray-900\">Add Student</p>\n              <p className=\"text-sm text-gray-600\">Register new student</p>\n            </button>\n            <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left\">\n              <FiUser className=\"h-6 w-6 text-green-600 mb-2\" />\n              <p className=\"font-medium text-gray-900\">Add Faculty</p>\n              <p className=\"text-sm text-gray-600\">Add faculty member</p>\n            </button>\n            <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left\">\n              <FiBookOpen className=\"h-6 w-6 text-purple-600 mb-2\" />\n              <p className=\"font-medium text-gray-900\">Create Course</p>\n              <p className=\"text-sm text-gray-600\">Add new course</p>\n            </button>\n            <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left\">\n              <FiFileText className=\"h-6 w-6 text-red-600 mb-2\" />\n              <p className=\"font-medium text-gray-900\">Schedule Exam</p>\n              <p className=\"text-sm text-gray-600\">Create exam schedule</p>\n            </button>\n          </div>\n        </div>\n\n        {/* Recent Activities */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Activities</h3>\n          <div className=\"space-y-4\">\n            {recentActivities.map((activity) => (\n              <div key={activity.id} className=\"flex items-start space-x-3\">\n                <div className={`p-2 rounded-lg bg-gray-100`}>\n                  <activity.icon className={`h-4 w-4 ${activity.color}`} />\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm text-gray-900\">{activity.message}</p>\n                  <p className=\"text-xs text-gray-500\">{activity.time}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n          <div className=\"mt-4\">\n            <button className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\">\n              View all activities →\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Performance Overview */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Performance Overview</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-blue-600\">{calculatePercentage(activeStudents, totalStudents)}%</div>\n            <div className=\"text-sm text-gray-600\">Student Retention</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-green-600\">{calculatePercentage(paidFees, totalFees)}%</div>\n            <div className=\"text-sm text-gray-600\">Fee Collection</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-purple-600\">{calculatePercentage(activeCourses, totalCourses)}%</div>\n            <div className=\"text-sm text-gray-600\">Course Completion</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-yellow-600\">{calculatePercentage(availableBooks, totalBooks)}%</div>\n            <div className=\"text-sm text-gray-600\">Library Utilization</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,OAAO,EACPC,UAAU,EACVC,YAAY,EACZC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,UAAU,QACL,gBAAgB;AACvB,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IACJC,QAAQ;IACRC,OAAO;IACPC,OAAO;IACPC,IAAI;IACJC,KAAK;IACLC,aAAa;IACbC;EACF,CAAC,GAAGb,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMc,aAAa,GAAGP,QAAQ,CAACQ,MAAM;EACrC,MAAMC,cAAc,GAAGT,QAAQ,CAACU,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACJ,MAAM;EACzE,MAAMK,YAAY,GAAGZ,OAAO,CAACO,MAAM;EACnC,MAAMM,aAAa,GAAGb,OAAO,CAACS,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACH,MAAM,KAAK,QAAQ,CAAC,CAACJ,MAAM;EACvE,MAAMQ,YAAY,GAAGd,OAAO,CAACM,MAAM;EACnC,MAAMS,aAAa,GAAGf,OAAO,CAACQ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACN,MAAM,KAAK,QAAQ,CAAC,CAACJ,MAAM;EAEvE,MAAMW,SAAS,GAAGhB,IAAI,CAACiB,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACC,WAAW,EAAE,CAAC,CAAC;EACrE,MAAMC,QAAQ,GAAGrB,IAAI,CAACiB,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACG,UAAU,EAAE,CAAC,CAAC;EACnE,MAAMC,WAAW,GAAGP,SAAS,GAAGK,QAAQ;EAExC,MAAMG,UAAU,GAAGvB,KAAK,CAACgB,MAAM,CAAC,CAACC,GAAG,EAAEO,IAAI,KAAKP,GAAG,GAAGO,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EACzE,MAAMC,kBAAkB,GAAGzB,aAAa,CAACK,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAACnB,MAAM,KAAK,UAAU,CAAC,CAACJ,MAAM;EACpF,MAAMwB,cAAc,GAAGL,UAAU,GAAGG,kBAAkB;EAEtD,MAAMG,aAAa,GAAG3B,KAAK,CAACI,MAAM,CAACwB,IAAI,IAAI;IACzC,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAACF,IAAI,CAACG,IAAI,CAAC;IACpC,MAAMC,KAAK,GAAG,IAAIF,IAAI,CAAC,CAAC;IACxB,MAAMG,QAAQ,GAAG,IAAIH,IAAI,CAACE,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpE,OAAOL,QAAQ,IAAIG,KAAK,IAAIH,QAAQ,IAAII,QAAQ;EAClD,CAAC,CAAC,CAAC/B,MAAM;EAET,MAAMiC,KAAK,GAAG,CACZ;IACEC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAEpC,aAAa;IACpBqC,QAAQ,EAAE,GAAGnC,cAAc,SAAS;IACpCoC,IAAI,EAAE3D,OAAO;IACb4D,KAAK,EAAE,aAAa;IACpBC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE9B,YAAY;IACnB+B,QAAQ,EAAE,GAAG9B,aAAa,SAAS;IACnC+B,IAAI,EAAExD,MAAM;IACZyD,KAAK,EAAE,cAAc;IACrBC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE1B,aAAa;IACpB2B,QAAQ,EAAE,GAAG5B,YAAY,QAAQ;IACjC6B,IAAI,EAAE1D,UAAU;IAChB2D,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAEjD,cAAc,CAAC8B,QAAQ,CAAC;IAC/BoB,QAAQ,EAAE,GAAGlD,cAAc,CAACgC,WAAW,CAAC,UAAU;IAClDmB,IAAI,EAAEzD,YAAY;IAClB0D,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAEhB,UAAU;IACjBiB,QAAQ,EAAE,GAAGZ,cAAc,YAAY;IACvCa,IAAI,EAAEtD,MAAM;IACZuD,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAEV,aAAa;IACpBW,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAErD,UAAU;IAChBsD,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,aAAa;IACrBC,UAAU,EAAE;EACd,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAG,CACvB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,mDAAmD;IAC5DC,IAAI,EAAE,aAAa;IACnBR,IAAI,EAAE3D,OAAO;IACb4D,KAAK,EAAE;EACT,CAAC,EACD;IACEI,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,2DAA2D;IACpEC,IAAI,EAAE,aAAa;IACnBR,IAAI,EAAE1D,UAAU;IAChB2D,KAAK,EAAE;EACT,CAAC,EACD;IACEI,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,+CAA+C;IACxDC,IAAI,EAAE,aAAa;IACnBR,IAAI,EAAEzD,YAAY;IAClB0D,KAAK,EAAE;EACT,CAAC,EACD;IACEI,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,4CAA4C;IACrDC,IAAI,EAAE,WAAW;IACjBR,IAAI,EAAEvD,UAAU;IAChBwD,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEjD,OAAA;IAAKyD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB1D,OAAA;MAAKyD,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC1D,OAAA;QAAIyD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1E9D,OAAA;QAAGyD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAAyD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/F,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEd,KAAK,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBjE,OAAA;QAAiByD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACnF1D,OAAA;UAAKyD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1D,OAAA;YAAKyD,SAAS,EAAE,GAAGO,IAAI,CAACf,KAAK,iBAAkB;YAAAS,QAAA,eAC7C1D,OAAA,CAACgE,IAAI,CAAChB,IAAI;cAACS,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B1D,OAAA;cAAGyD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEM,IAAI,CAACnB;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChE9D,OAAA;cAAGyD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEM,IAAI,CAAClB;YAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChE9D,OAAA;cAAGyD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEM,IAAI,CAACjB;YAAQ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzB1D,OAAA;cAAMyD,SAAS,EAAE,uBACfO,IAAI,CAACb,UAAU,KAAK,UAAU,GAAG,gBAAgB,GACjDa,IAAI,CAACb,UAAU,KAAK,UAAU,GAAG,cAAc,GAAG,eAAe,EAChE;cAAAO,QAAA,EACAM,IAAI,CAACd;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAlBEG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmBV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD1D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAIyD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E9D,OAAA;UAAKyD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC1D,OAAA;YAAQyD,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAClF1D,OAAA,CAACX,OAAO;cAACoE,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClD9D,OAAA;cAAGyD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxD9D,OAAA;cAAGyD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACT9D,OAAA;YAAQyD,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAClF1D,OAAA,CAACR,MAAM;cAACiE,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClD9D,OAAA;cAAGyD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxD9D,OAAA;cAAGyD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACT9D,OAAA;YAAQyD,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAClF1D,OAAA,CAACV,UAAU;cAACmE,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvD9D,OAAA;cAAGyD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1D9D,OAAA;cAAGyD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACT9D,OAAA;YAAQyD,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAClF1D,OAAA,CAACL,UAAU;cAAC8D,SAAS,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD9D,OAAA;cAAGyD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1D9D,OAAA;cAAGyD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAIyD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/E9D,OAAA;UAAKyD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBN,gBAAgB,CAACW,GAAG,CAAEG,QAAQ,iBAC7BlE,OAAA;YAAuByD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBAC3D1D,OAAA;cAAKyD,SAAS,EAAE,4BAA6B;cAAAC,QAAA,eAC3C1D,OAAA,CAACkE,QAAQ,CAAClB,IAAI;gBAACS,SAAS,EAAE,WAAWS,QAAQ,CAACjB,KAAK;cAAG;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACN9D,OAAA;cAAKyD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B1D,OAAA;gBAAGyD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEQ,QAAQ,CAACX;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3D9D,OAAA;gBAAGyD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEQ,QAAQ,CAACV;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA,GAPEI,QAAQ,CAACb,EAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQhB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB1D,OAAA;YAAQyD,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EAAC;UAEhF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvE1D,OAAA;QAAIyD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClF9D,OAAA;QAAKyD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD1D,OAAA;UAAKyD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1D,OAAA;YAAKyD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAAE5D,mBAAmB,CAACc,cAAc,EAAEF,aAAa,CAAC,EAAC,GAAC;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7G9D,OAAA;YAAKyD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1D,OAAA;YAAKyD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,GAAE5D,mBAAmB,CAAC6B,QAAQ,EAAEL,SAAS,CAAC,EAAC,GAAC;UAAA;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpG9D,OAAA;YAAKyD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1D,OAAA;YAAKyD,SAAS,EAAC,oCAAoC;YAAAC,QAAA,GAAE5D,mBAAmB,CAACsB,aAAa,EAAED,YAAY,CAAC,EAAC,GAAC;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7G9D,OAAA;YAAKyD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1D,OAAA;YAAKyD,SAAS,EAAC,oCAAoC;YAAAC,QAAA,GAAE5D,mBAAmB,CAACqC,cAAc,EAAEL,UAAU,CAAC,EAAC,GAAC;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5G9D,OAAA;YAAKyD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5D,EAAA,CA7OID,cAAc;EAAA,QASdL,OAAO;AAAA;AAAAuE,EAAA,GATPlE,cAAc;AA+OpB,eAAeA,cAAc;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}