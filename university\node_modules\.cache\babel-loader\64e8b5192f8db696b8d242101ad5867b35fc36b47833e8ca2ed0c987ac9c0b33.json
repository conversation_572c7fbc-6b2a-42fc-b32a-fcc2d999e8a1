{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Lock } from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport { useData } from '../../context/DataContext';\nimport { validateEmail } from '../../utils/helpers';\nimport UniversityLogo from '../common/UniversityLogo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    role: 'student'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const {\n    students,\n    faculty\n  } = useData();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    setLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      let user = null;\n\n      // Check admin credentials\n      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {\n        user = {\n          id: 'ADM001',\n          name: 'Admin User',\n          email: '<EMAIL>',\n          role: 'admin',\n          phone: '+1234567896',\n          address: 'University Administration Building',\n          status: 'active'\n        };\n      } else {\n        // Check student/faculty credentials\n        const allUsers = [...students, ...faculty];\n        user = allUsers.find(u => u.email === formData.email && u.password === formData.password && u.role === formData.role);\n      }\n      if (user) {\n        login(user);\n        navigate('/dashboard');\n      } else {\n        setErrors({\n          submit: 'Invalid email, password, or role'\n        });\n      }\n    } catch (error) {\n      setErrors({\n        submit: 'Login failed. Please try again.'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-primary-600/20 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-primary-800/20 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center animate-fade-in-up\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto mb-4 animate-bounce\",\n          children: /*#__PURE__*/_jsxDEV(UniversityLogo, {\n            className: \"h-20 w-20 mx-auto animate-float hover-scale\",\n            showUpload: true,\n            onLogoChange: logoUrl => {\n              // Store the logo URL in localStorage for persistence\n              localStorage.setItem('universityLogo', logoUrl);\n            },\n            customLogo: localStorage.getItem('universityLogo')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold gradient-text mb-2 animate-slide-in-left hover-glow\",\n          children: \"NFC IET MULTAN\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-300 font-semibold mb-1 animate-slide-in-left\",\n          style: {\n            animationDelay: '0.1s'\n          },\n          children: \"National Fertilizer Corporation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base text-gray-400 font-medium mb-4 animate-slide-in-left\",\n          style: {\n            animationDelay: '0.2s'\n          },\n          children: \"Institute of Engineering & Technology\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-white mb-2 animate-slide-in-right\",\n          style: {\n            animationDelay: '0.3s'\n          },\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 animate-slide-in-right\",\n          style: {\n            animationDelay: '0.4s'\n          },\n          children: \"Sign in to access your dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6 glass-dark p-8 rounded-2xl border border-primary-600/20 shadow-2xl animate-scale-in hover-lift\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            style: {\n              animationDelay: '0.5s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"role\",\n              className: \"block text-sm font-semibold text-gray-200 mb-2\",\n              children: \"Login as\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"role\",\n              name: \"role\",\n              value: formData.role,\n              onChange: handleChange,\n              className: \"modern-input-dark block w-full px-4 py-3 border-2 border-primary-600/30 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"student\",\n                className: \"bg-dark-800 text-white\",\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"faculty\",\n                className: \"bg-dark-800 text-white\",\n                children: \"Faculty\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"admin\",\n                className: \"bg-dark-800 text-white\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            style: {\n              animationDelay: '0.6s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-semibold text-gray-200 mb-2\",\n              children: \"Email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(FiUser, {\n                  className: \"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                autoComplete: \"email\",\n                value: formData.email,\n                onChange: handleChange,\n                className: `modern-input-dark block w-full pl-12 pr-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${errors.email ? 'border-red-400' : 'border-primary-600/30'}`,\n                placeholder: \"Enter your email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\",\n              children: errors.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            style: {\n              animationDelay: '0.7s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-semibold text-gray-200 mb-2\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(FiLock, {\n                  className: \"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"password\",\n                name: \"password\",\n                type: showPassword ? 'text' : 'password',\n                autoComplete: \"current-password\",\n                value: formData.password,\n                onChange: handleChange,\n                className: `modern-input-dark block w-full pl-12 pr-12 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${errors.password ? 'border-red-400' : 'border-primary-600/30'}`,\n                placeholder: \"Enter your password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-primary-400 hover:text-primary-300 transition-colors duration-200 hover-scale\",\n                onClick: () => setShowPassword(!showPassword),\n                children: showPassword ? /*#__PURE__*/_jsxDEV(FiEyeOff, {\n                  className: \"h-5 w-5 animate-bounce\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(FiEye, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\",\n              children: errors.password\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-500/10 border border-red-400/30 rounded-xl p-4 animate-slide-down\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-red-400 font-medium\",\n            children: errors.submit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in-up\",\n          style: {\n            animationDelay: '0.8s'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"btn-primary group relative w-full flex justify-center py-4 px-6 border border-transparent text-sm font-bold rounded-xl text-white focus:outline-none focus-ring disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none transition-all duration-300 hover-lift animate-glow\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), \"Signing in...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this) : 'Sign in'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-300\",\n            children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"font-semibold text-primary-400 hover:text-primary-300 transition-colors duration-200\",\n              children: \"Register here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 glass-dark border border-primary-600/20 rounded-xl p-6 animate-slide-up\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-bold text-primary-300 mb-3 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), \"Demo Credentials:\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-300 space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-primary-400\",\n              children: \"Admin:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs\",\n              children: \"<EMAIL> / admin123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-primary-400\",\n              children: \"Faculty:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs\",\n              children: \"<EMAIL> / password123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-primary-400\",\n              children: \"Student:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs\",\n              children: \"<EMAIL> / password123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"Sw1YWCfl0JeiCFcqOSg1RybP8ao=\", false, function () {\n  return [useAuth, useData, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "FiEye", "Fi<PERSON>ye<PERSON>ff", "FiUser", "FiLock", "useAuth", "useData", "validateEmail", "UniversityLogo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "role", "showPassword", "setShowPassword", "errors", "setErrors", "loading", "setLoading", "login", "students", "faculty", "navigate", "handleChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "length", "Object", "keys", "handleSubmit", "preventDefault", "Promise", "resolve", "setTimeout", "user", "id", "phone", "address", "status", "allUsers", "find", "u", "submit", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showUpload", "onLogoChange", "logoUrl", "localStorage", "setItem", "customLogo", "getItem", "style", "animationDelay", "onSubmit", "htmlFor", "onChange", "type", "autoComplete", "placeholder", "onClick", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Lock } from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport { useData } from '../../context/DataContext';\nimport { validateEmail } from '../../utils/helpers';\nimport UniversityLogo from '../common/UniversityLogo';\n\nconst Login = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    role: 'student'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  const { login } = useAuth();\n  const { students, faculty } = useData();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    setLoading(true);\n\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      let user = null;\n\n      // Check admin credentials\n      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {\n        user = {\n          id: 'ADM001',\n          name: 'Admin User',\n          email: '<EMAIL>',\n          role: 'admin',\n          phone: '+1234567896',\n          address: 'University Administration Building',\n          status: 'active',\n        };\n      } else {\n        // Check student/faculty credentials\n        const allUsers = [...students, ...faculty];\n        user = allUsers.find(u => \n          u.email === formData.email && \n          u.password === formData.password &&\n          u.role === formData.role\n        );\n      }\n\n      if (user) {\n        login(user);\n        navigate('/dashboard');\n      } else {\n        setErrors({ submit: 'Invalid email, password, or role' });\n      }\n    } catch (error) {\n      setErrors({ submit: 'Login failed. Please try again.' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\n      {/* Background decoration */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-primary-600/20 rounded-full blur-3xl\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-primary-800/20 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"max-w-md w-full space-y-8 relative z-10\">\n        <div className=\"text-center animate-fade-in-up\">\n          {/* University Logo */}\n          <div className=\"mx-auto mb-4 animate-bounce\">\n            <UniversityLogo\n              className=\"h-20 w-20 mx-auto animate-float hover-scale\"\n              showUpload={true}\n              onLogoChange={(logoUrl) => {\n                // Store the logo URL in localStorage for persistence\n                localStorage.setItem('universityLogo', logoUrl);\n              }}\n              customLogo={localStorage.getItem('universityLogo')}\n            />\n          </div>\n\n          {/* University Name */}\n          <h1 className=\"text-3xl font-bold gradient-text mb-2 animate-slide-in-left hover-glow\">\n            NFC IET MULTAN\n          </h1>\n          <p className=\"text-lg text-gray-300 font-semibold mb-1 animate-slide-in-left\" style={{animationDelay: '0.1s'}}>\n            National Fertilizer Corporation\n          </p>\n          <p className=\"text-base text-gray-400 font-medium mb-4 animate-slide-in-left\" style={{animationDelay: '0.2s'}}>\n            Institute of Engineering & Technology\n          </p>\n\n          {/* Welcome Message */}\n          <h2 className=\"text-2xl font-bold text-white mb-2 animate-slide-in-right\" style={{animationDelay: '0.3s'}}>\n            Welcome Back\n          </h2>\n          <p className=\"text-sm text-gray-400 animate-slide-in-right\" style={{animationDelay: '0.4s'}}>\n            Sign in to access your dashboard\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6 glass-dark p-8 rounded-2xl border border-primary-600/20 shadow-2xl animate-scale-in hover-lift\" onSubmit={handleSubmit}>\n          <div className=\"space-y-6\">\n            {/* Role Selection */}\n            <div className=\"animate-fade-in-up\" style={{animationDelay: '0.5s'}}>\n              <label htmlFor=\"role\" className=\"block text-sm font-semibold text-gray-200 mb-2\">\n                Login as\n              </label>\n              <select\n                id=\"role\"\n                name=\"role\"\n                value={formData.role}\n                onChange={handleChange}\n                className=\"modern-input-dark block w-full px-4 py-3 border-2 border-primary-600/30 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300\"\n              >\n                <option value=\"student\" className=\"bg-dark-800 text-white\">Student</option>\n                <option value=\"faculty\" className=\"bg-dark-800 text-white\">Faculty</option>\n                <option value=\"admin\" className=\"bg-dark-800 text-white\">Admin</option>\n              </select>\n            </div>\n\n            {/* Email */}\n            <div className=\"animate-fade-in-up\" style={{animationDelay: '0.6s'}}>\n              <label htmlFor=\"email\" className=\"block text-sm font-semibold text-gray-200 mb-2\">\n                Email address\n              </label>\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <FiUser className=\"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\" />\n                </div>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  className={`modern-input-dark block w-full pl-12 pr-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${\n                    errors.email ? 'border-red-400' : 'border-primary-600/30'\n                  }`}\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n              {errors.email && (\n                <p className=\"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\">{errors.email}</p>\n              )}\n            </div>\n\n            {/* Password */}\n            <div className=\"animate-fade-in-up\" style={{animationDelay: '0.7s'}}>\n              <label htmlFor=\"password\" className=\"block text-sm font-semibold text-gray-200 mb-2\">\n                Password\n              </label>\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <FiLock className=\"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\" />\n                </div>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  className={`modern-input-dark block w-full pl-12 pr-12 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${\n                    errors.password ? 'border-red-400' : 'border-primary-600/30'\n                  }`}\n                  placeholder=\"Enter your password\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-4 flex items-center text-primary-400 hover:text-primary-300 transition-colors duration-200 hover-scale\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <FiEyeOff className=\"h-5 w-5 animate-bounce\" />\n                  ) : (\n                    <FiEye className=\"h-5 w-5\" />\n                  )}\n                </button>\n              </div>\n              {errors.password && (\n                <p className=\"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\">{errors.password}</p>\n              )}\n            </div>\n          </div>\n\n          {errors.submit && (\n            <div className=\"bg-red-500/10 border border-red-400/30 rounded-xl p-4 animate-slide-down\">\n              <p className=\"text-sm text-red-400 font-medium\">{errors.submit}</p>\n            </div>\n          )}\n\n          <div className=\"animate-fade-in-up\" style={{animationDelay: '0.8s'}}>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"btn-primary group relative w-full flex justify-center py-4 px-6 border border-transparent text-sm font-bold rounded-xl text-white focus:outline-none focus-ring disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none transition-all duration-300 hover-lift animate-glow\"\n            >\n              {loading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Signing in...\n                </div>\n              ) : (\n                'Sign in'\n              )}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-300\">\n              Don't have an account?{' '}\n              <Link to=\"/register\" className=\"font-semibold text-primary-400 hover:text-primary-300 transition-colors duration-200\">\n                Register here\n              </Link>\n            </p>\n          </div>\n        </form>\n\n        {/* Demo credentials */}\n        <div className=\"mt-8 glass-dark border border-primary-600/20 rounded-xl p-6 animate-slide-up\">\n          <h3 className=\"text-sm font-bold text-primary-300 mb-3 flex items-center\">\n            <span className=\"w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse\"></span>\n            Demo Credentials:\n          </h3>\n          <div className=\"text-sm text-gray-300 space-y-2\">\n            <div className=\"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg\">\n              <span className=\"font-semibold text-primary-400\">Admin:</span>\n              <span className=\"text-xs\"><EMAIL> / admin123</span>\n            </div>\n            <div className=\"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg\">\n              <span className=\"font-semibold text-primary-400\">Faculty:</span>\n              <span className=\"text-xs\"><EMAIL> / password123</span>\n            </div>\n            <div className=\"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg\">\n              <span className=\"font-semibold text-primary-400\">Student:</span>\n              <span className=\"text-xs\"><EMAIL> / password123</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AAChE,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,cAAc,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAE0B;EAAM,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC3B,MAAM;IAAEoB,QAAQ;IAAEC;EAAQ,CAAC,GAAGpB,OAAO,CAAC,CAAC;EACvC,MAAMqB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAE9B,MAAM4B,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClB,WAAW,CAACmB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIX,MAAM,CAACU,IAAI,CAAC,EAAE;MAChBT,SAAS,CAACY,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACtB,QAAQ,CAACE,KAAK,EAAE;MACnBoB,SAAS,CAACpB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAACR,aAAa,CAACM,QAAQ,CAACE,KAAK,CAAC,EAAE;MACzCoB,SAAS,CAACpB,KAAK,GAAG,4BAA4B;IAChD;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBmB,SAAS,CAACnB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIH,QAAQ,CAACG,QAAQ,CAACoB,MAAM,GAAG,CAAC,EAAE;MACvCD,SAAS,CAACnB,QAAQ,GAAG,wCAAwC;IAC/D;IAEAK,SAAS,CAACc,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACC,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOV,CAAC,IAAK;IAChCA,CAAC,CAACW,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;IAErBX,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM,IAAIkB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,IAAIE,IAAI,GAAG,IAAI;;MAEf;MACA,IAAI/B,QAAQ,CAACE,KAAK,KAAK,sBAAsB,IAAIF,QAAQ,CAACG,QAAQ,KAAK,UAAU,EAAE;QACjF4B,IAAI,GAAG;UACLC,EAAE,EAAE,QAAQ;UACZf,IAAI,EAAE,YAAY;UAClBf,KAAK,EAAE,sBAAsB;UAC7BE,IAAI,EAAE,OAAO;UACb6B,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE,oCAAoC;UAC7CC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMC,QAAQ,GAAG,CAAC,GAAGxB,QAAQ,EAAE,GAAGC,OAAO,CAAC;QAC1CkB,IAAI,GAAGK,QAAQ,CAACC,IAAI,CAACC,CAAC,IACpBA,CAAC,CAACpC,KAAK,KAAKF,QAAQ,CAACE,KAAK,IAC1BoC,CAAC,CAACnC,QAAQ,KAAKH,QAAQ,CAACG,QAAQ,IAChCmC,CAAC,CAAClC,IAAI,KAAKJ,QAAQ,CAACI,IACtB,CAAC;MACH;MAEA,IAAI2B,IAAI,EAAE;QACRpB,KAAK,CAACoB,IAAI,CAAC;QACXjB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,MAAM;QACLN,SAAS,CAAC;UAAE+B,MAAM,EAAE;QAAmC,CAAC,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhC,SAAS,CAAC;QAAE+B,MAAM,EAAE;MAAkC,CAAC,CAAC;IAC1D,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEb,OAAA;IAAK4C,SAAS,EAAC,4JAA4J;IAAAC,QAAA,gBAEzK7C,OAAA;MAAK4C,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/C7C,OAAA;QAAK4C,SAAS,EAAC;MAA8E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpGjD,OAAA;QAAK4C,SAAS,EAAC;MAAgF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnG,CAAC,eAENjD,OAAA;MAAK4C,SAAS,EAAC,yCAAyC;MAAAC,QAAA,gBACtD7C,OAAA;QAAK4C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAE7C7C,OAAA;UAAK4C,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1C7C,OAAA,CAACF,cAAc;YACb8C,SAAS,EAAC,6CAA6C;YACvDM,UAAU,EAAE,IAAK;YACjBC,YAAY,EAAGC,OAAO,IAAK;cACzB;cACAC,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEF,OAAO,CAAC;YACjD,CAAE;YACFG,UAAU,EAAEF,YAAY,CAACG,OAAO,CAAC,gBAAgB;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNjD,OAAA;UAAI4C,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EAAC;QAEvF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjD,OAAA;UAAG4C,SAAS,EAAC,gEAAgE;UAACa,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAAAb,QAAA,EAAC;QAE/G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJjD,OAAA;UAAG4C,SAAS,EAAC,gEAAgE;UAACa,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAAAb,QAAA,EAAC;QAE/G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGJjD,OAAA;UAAI4C,SAAS,EAAC,2DAA2D;UAACa,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAAAb,QAAA,EAAC;QAE3G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjD,OAAA;UAAG4C,SAAS,EAAC,8CAA8C;UAACa,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAAAb,QAAA,EAAC;QAE7F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENjD,OAAA;QAAM4C,SAAS,EAAC,+GAA+G;QAACe,QAAQ,EAAE9B,YAAa;QAAAgB,QAAA,gBACrJ7C,OAAA;UAAK4C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB7C,OAAA;YAAK4C,SAAS,EAAC,oBAAoB;YAACa,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAb,QAAA,gBAClE7C,OAAA;cAAO4D,OAAO,EAAC,MAAM;cAAChB,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAAC;YAEjF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjD,OAAA;cACEmC,EAAE,EAAC,MAAM;cACTf,IAAI,EAAC,MAAM;cACXC,KAAK,EAAElB,QAAQ,CAACI,IAAK;cACrBsD,QAAQ,EAAE3C,YAAa;cACvB0B,SAAS,EAAC,iOAAiO;cAAAC,QAAA,gBAE3O7C,OAAA;gBAAQqB,KAAK,EAAC,SAAS;gBAACuB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3EjD,OAAA;gBAAQqB,KAAK,EAAC,SAAS;gBAACuB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3EjD,OAAA;gBAAQqB,KAAK,EAAC,OAAO;gBAACuB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNjD,OAAA;YAAK4C,SAAS,EAAC,oBAAoB;YAACa,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAb,QAAA,gBAClE7C,OAAA;cAAO4D,OAAO,EAAC,OAAO;cAAChB,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAAC;YAElF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjD,OAAA;cAAK4C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7C,OAAA;gBAAK4C,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnF7C,OAAA,CAACP,MAAM;kBAACmD,SAAS,EAAC;gBAAsF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC,eACNjD,OAAA;gBACEmC,EAAE,EAAC,OAAO;gBACVf,IAAI,EAAC,OAAO;gBACZ0C,IAAI,EAAC,OAAO;gBACZC,YAAY,EAAC,OAAO;gBACpB1C,KAAK,EAAElB,QAAQ,CAACE,KAAM;gBACtBwD,QAAQ,EAAE3C,YAAa;gBACvB0B,SAAS,EAAE,mNACTlC,MAAM,CAACL,KAAK,GAAG,gBAAgB,GAAG,uBAAuB,EACxD;gBACH2D,WAAW,EAAC;cAAkB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACLvC,MAAM,CAACL,KAAK,iBACXL,OAAA;cAAG4C,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAAEnC,MAAM,CAACL;YAAK;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC7F;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNjD,OAAA;YAAK4C,SAAS,EAAC,oBAAoB;YAACa,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAb,QAAA,gBAClE7C,OAAA;cAAO4D,OAAO,EAAC,UAAU;cAAChB,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAAC;YAErF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjD,OAAA;cAAK4C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7C,OAAA;gBAAK4C,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnF7C,OAAA,CAACN,MAAM;kBAACkD,SAAS,EAAC;gBAAsF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC,eACNjD,OAAA;gBACEmC,EAAE,EAAC,UAAU;gBACbf,IAAI,EAAC,UAAU;gBACf0C,IAAI,EAAEtD,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCuD,YAAY,EAAC,kBAAkB;gBAC/B1C,KAAK,EAAElB,QAAQ,CAACG,QAAS;gBACzBuD,QAAQ,EAAE3C,YAAa;gBACvB0B,SAAS,EAAE,oNACTlC,MAAM,CAACJ,QAAQ,GAAG,gBAAgB,GAAG,uBAAuB,EAC3D;gBACH0D,WAAW,EAAC;cAAqB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACFjD,OAAA;gBACE8D,IAAI,EAAC,QAAQ;gBACblB,SAAS,EAAC,sIAAsI;gBAChJqB,OAAO,EAAEA,CAAA,KAAMxD,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAAqC,QAAA,EAE7CrC,YAAY,gBACXR,OAAA,CAACR,QAAQ;kBAACoD,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/CjD,OAAA,CAACT,KAAK;kBAACqD,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC7B;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLvC,MAAM,CAACJ,QAAQ,iBACdN,OAAA;cAAG4C,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAAEnC,MAAM,CAACJ;YAAQ;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAChG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELvC,MAAM,CAACgC,MAAM,iBACZ1C,OAAA;UAAK4C,SAAS,EAAC,0EAA0E;UAAAC,QAAA,eACvF7C,OAAA;YAAG4C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEnC,MAAM,CAACgC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACN,eAEDjD,OAAA;UAAK4C,SAAS,EAAC,oBAAoB;UAACa,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAAAb,QAAA,eAClE7C,OAAA;YACE8D,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAEtD,OAAQ;YAClBgC,SAAS,EAAC,6RAA6R;YAAAC,QAAA,EAEtSjC,OAAO,gBACNZ,OAAA;cAAK4C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC7C,OAAA;gBAAK4C,SAAS,EAAC;cAAgE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,iBAExF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENjD,OAAA;UAAK4C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B7C,OAAA;YAAG4C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,wBACb,EAAC,GAAG,eAC1B7C,OAAA,CAACX,IAAI;cAAC8E,EAAE,EAAC,WAAW;cAACvB,SAAS,EAAC,sFAAsF;cAAAC,QAAA,EAAC;YAEtH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPjD,OAAA;QAAK4C,SAAS,EAAC,8EAA8E;QAAAC,QAAA,gBAC3F7C,OAAA;UAAI4C,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACvE7C,OAAA;YAAM4C,SAAS,EAAC;UAAwD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,qBAElF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjD,OAAA;UAAK4C,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9C7C,OAAA;YAAK4C,SAAS,EAAC,oEAAoE;YAAAC,QAAA,gBACjF7C,OAAA;cAAM4C,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DjD,OAAA;cAAM4C,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNjD,OAAA;YAAK4C,SAAS,EAAC,oEAAoE;YAAAC,QAAA,gBACjF7C,OAAA;cAAM4C,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEjD,OAAA;cAAM4C,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNjD,OAAA;YAAK4C,SAAS,EAAC,oEAAoE;YAAAC,QAAA,gBACjF7C,OAAA;cAAM4C,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEjD,OAAA;cAAM4C,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAqC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/C,EAAA,CAzRID,KAAK;EAAA,QAUSN,OAAO,EACKC,OAAO,EACpBN,WAAW;AAAA;AAAA8E,EAAA,GAZxBnE,KAAK;AA2RX,eAAeA,KAAK;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}