{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ff, <PERSON><PERSON><PERSON>, <PERSON>Lock } from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport { useData } from '../../context/DataContext';\nimport { validateEmail } from '../../utils/helpers';\nimport UniversityLogo from '../common/UniversityLogo';\nimport backgroundImage from '../DefaultBackground.jpg';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    role: 'student'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const {\n    students,\n    faculty\n  } = useData();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    setLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      let user = null;\n\n      // Check admin credentials\n      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {\n        user = {\n          id: 'ADM001',\n          name: 'Admin User',\n          email: '<EMAIL>',\n          role: 'admin',\n          phone: '+1234567896',\n          address: 'University Administration Building',\n          status: 'active'\n        };\n      } else {\n        // Check student/faculty credentials from mock data\n        const allUsers = [...students, ...faculty];\n\n        // Debug: Log the search details\n        console.log('Login attempt:', {\n          email: formData.email,\n          password: formData.password,\n          role: formData.role\n        });\n        console.log('Available students:', students.length);\n        console.log('Available faculty:', faculty.length);\n        console.log('Looking for user with email:', formData.email);\n\n        // Find Hammad specifically for debugging\n        const hammadUser = students.find(s => s.email === '<EMAIL>');\n        console.log('Hammad user found:', hammadUser);\n        user = allUsers.find(u => u.email === formData.email && u.password === formData.password && u.role === formData.role);\n        console.log('User found in mock data:', user);\n\n        // If not found in mock data, check registered users\n        if (!user) {\n          const registeredUsers = JSON.parse(localStorage.getItem('ums_registered_users') || '[]');\n          console.log('Checking registered users:', registeredUsers.length);\n          user = registeredUsers.find(u => u.email === formData.email && u.password === formData.password && u.role === formData.role);\n          console.log('User found in registered users:', user);\n        }\n      }\n      if (user) {\n        login(user);\n        navigate('/dashboard');\n      } else {\n        setErrors({\n          submit: 'Invalid email, password, or role'\n        });\n      }\n    } catch (error) {\n      setErrors({\n        submit: 'Login failed. Please try again.'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden\",\n    style: {\n      backgroundImage: `url(${backgroundImage})`,\n      backgroundSize: 'cover',\n      backgroundPosition: 'center',\n      backgroundRepeat: 'no-repeat',\n      backgroundAttachment: 'fixed'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-black/40 dark:bg-black/60\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-20 left-20 text-white/40 animate-float\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-16 h-16\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-32 right-32 text-white/35 animate-float\",\n        style: {\n          animationDelay: '1s'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-20 h-20\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-32 left-32 text-white/35 animate-float\",\n        style: {\n          animationDelay: '2s'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-18 h-18\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H18V1h-2v1H8V1H6v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2zm0 16h-15v-2h15v2zm0-5h-15V6h15v7z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/2 right-20 text-white/30 animate-float\",\n        style: {\n          animationDelay: '3s'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-24 h-24\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-20 right-40 text-white/35 animate-float\",\n        style: {\n          animationDelay: '4s'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-14 h-14\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-16 left-1/2 transform -translate-x-1/2 text-white/25 font-mono text-sm animate-fade-in-out\",\n        children: \"\\u222B f(x)dx = F(x) + C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-40 left-16 text-white/25 font-mono text-xs animate-fade-in-out\",\n        style: {\n          animationDelay: '2s'\n        },\n        children: \"E = mc\\xB2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-40 right-16 text-white/25 font-mono text-xs animate-fade-in-out\",\n        style: {\n          animationDelay: '3s'\n        },\n        children: \"f(x) = ax\\xB2 + bx + c\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-16 left-1/2 transform -translate-x-1/2 text-white/25 font-mono text-sm animate-fade-in-out\",\n        style: {\n          animationDelay: '1s'\n        },\n        children: \"lim(x\\u2192\\u221E) f(x) = L\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/4 left-1/4 w-32 h-32 border border-white/20 rotate-45 animate-spin-slow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-1/4 right-1/4 w-24 h-24 border border-white/20 rotate-12 animate-spin-reverse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-primary-500/20 rounded-full blur-3xl animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-primary-700/20 rounded-full blur-3xl animate-pulse\",\n        style: {\n          animationDelay: '2s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary-400/10 rounded-full blur-3xl animate-pulse\",\n        style: {\n          animationDelay: '4s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-12 gap-4 h-full\",\n          children: Array.from({\n            length: 144\n          }).map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-white/10 rounded\"\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center animate-fade-in-up\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto mb-4 animate-bounce\",\n          children: /*#__PURE__*/_jsxDEV(UniversityLogo, {\n            className: \"h-20 w-20 mx-auto animate-float hover-scale\",\n            showUpload: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold gradient-text mb-2 animate-slide-in-left hover-glow\",\n          children: \"NFC IET MULTAN\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-300 font-semibold mb-1 animate-slide-in-left\",\n          style: {\n            animationDelay: '0.1s'\n          },\n          children: \"National Fertilizer Corporation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base text-gray-400 font-medium mb-4 animate-slide-in-left\",\n          style: {\n            animationDelay: '0.2s'\n          },\n          children: \"Institute of Engineering & Technology\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-white mb-2 animate-slide-in-right\",\n          style: {\n            animationDelay: '0.3s'\n          },\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 animate-slide-in-right\",\n          style: {\n            animationDelay: '0.4s'\n          },\n          children: \"Sign in to access your dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6 glass-dark p-8 rounded-2xl border border-primary-600/20 shadow-2xl animate-scale-in hover-lift\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            style: {\n              animationDelay: '0.5s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"role\",\n              className: \"block text-sm font-semibold text-gray-200 mb-2\",\n              children: \"Login as\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"role\",\n              name: \"role\",\n              value: formData.role,\n              onChange: handleChange,\n              className: \"modern-input-dark block w-full px-4 py-3 border-2 border-primary-600/30 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"student\",\n                className: \"bg-dark-800 text-white\",\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"faculty\",\n                className: \"bg-dark-800 text-white\",\n                children: \"Faculty\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"admin\",\n                className: \"bg-dark-800 text-white\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            style: {\n              animationDelay: '0.6s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-semibold text-gray-200 mb-2\",\n              children: \"Email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(FiUser, {\n                  className: \"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                autoComplete: \"email\",\n                value: formData.email,\n                onChange: handleChange,\n                className: `modern-input-dark block w-full pl-12 pr-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${errors.email ? 'border-red-400' : 'border-primary-600/30'}`,\n                placeholder: \"Enter your email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\",\n              children: errors.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            style: {\n              animationDelay: '0.7s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-semibold text-gray-200 mb-2\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(FiLock, {\n                  className: \"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"password\",\n                name: \"password\",\n                type: showPassword ? 'text' : 'password',\n                autoComplete: \"current-password\",\n                value: formData.password,\n                onChange: handleChange,\n                className: `modern-input-dark block w-full pl-12 pr-12 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${errors.password ? 'border-red-400' : 'border-primary-600/30'}`,\n                placeholder: \"Enter your password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-primary-400 hover:text-primary-300 transition-colors duration-200 hover-scale\",\n                onClick: () => setShowPassword(!showPassword),\n                children: showPassword ? /*#__PURE__*/_jsxDEV(FiEyeOff, {\n                  className: \"h-5 w-5 animate-bounce\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(FiEye, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\",\n              children: errors.password\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-500/10 border border-red-400/30 rounded-xl p-4 animate-slide-down\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-red-400 font-medium\",\n            children: errors.submit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in-up\",\n          style: {\n            animationDelay: '0.8s'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"btn-primary group relative w-full flex justify-center py-4 px-6 border border-transparent text-sm font-bold rounded-xl text-white focus:outline-none focus-ring disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none transition-all duration-300 hover-lift animate-glow\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), \"Signing in...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this) : 'Sign in'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-300\",\n            children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"font-semibold text-primary-400 hover:text-primary-300 transition-colors duration-200\",\n              children: \"Register here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 glass-dark border border-primary-600/20 rounded-xl p-6 animate-scale-in hover-lift\",\n        style: {\n          animationDelay: '1s'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-bold text-primary-300 mb-3 flex items-center animate-slide-in-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), \"Demo Credentials:\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-300 space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\",\n            style: {\n              animationDelay: '1.1s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-primary-400\",\n              children: \"Admin:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs\",\n              children: \"<EMAIL> / admin123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\",\n            style: {\n              animationDelay: '1.2s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-primary-400\",\n              children: \"Faculty:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs\",\n              children: \"<EMAIL> / password123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\",\n            style: {\n              animationDelay: '1.3s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-primary-400\",\n              children: \"Student:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs\",\n              children: \"<EMAIL> / password123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"Sw1YWCfl0JeiCFcqOSg1RybP8ao=\", false, function () {\n  return [useAuth, useData, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "FiEye", "Fi<PERSON>ye<PERSON>ff", "FiUser", "FiLock", "useAuth", "useData", "validateEmail", "UniversityLogo", "backgroundImage", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "role", "showPassword", "setShowPassword", "errors", "setErrors", "loading", "setLoading", "login", "students", "faculty", "navigate", "handleChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "length", "Object", "keys", "handleSubmit", "preventDefault", "Promise", "resolve", "setTimeout", "user", "id", "phone", "address", "status", "allUsers", "console", "log", "hammad<PERSON><PERSON>", "find", "s", "u", "registeredUsers", "JSON", "parse", "localStorage", "getItem", "submit", "error", "className", "style", "backgroundSize", "backgroundPosition", "backgroundRepeat", "backgroundAttachment", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "d", "animationDelay", "Array", "from", "map", "_", "i", "showUpload", "onSubmit", "htmlFor", "onChange", "type", "autoComplete", "placeholder", "onClick", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FiLock } from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport { useData } from '../../context/DataContext';\nimport { validateEmail } from '../../utils/helpers';\nimport UniversityLogo from '../common/UniversityLogo';\nimport backgroundImage from '../DefaultBackground.jpg';\n\nconst Login = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    role: 'student'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  const { login } = useAuth();\n  const { students, faculty } = useData();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    setLoading(true);\n\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      let user = null;\n\n      // Check admin credentials\n      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {\n        user = {\n          id: 'ADM001',\n          name: 'Admin User',\n          email: '<EMAIL>',\n          role: 'admin',\n          phone: '+1234567896',\n          address: 'University Administration Building',\n          status: 'active',\n        };\n      } else {\n        // Check student/faculty credentials from mock data\n        const allUsers = [...students, ...faculty];\n\n        // Debug: Log the search details\n        console.log('Login attempt:', {\n          email: formData.email,\n          password: formData.password,\n          role: formData.role\n        });\n        console.log('Available students:', students.length);\n        console.log('Available faculty:', faculty.length);\n        console.log('Looking for user with email:', formData.email);\n\n        // Find Hammad specifically for debugging\n        const hammadUser = students.find(s => s.email === '<EMAIL>');\n        console.log('Hammad user found:', hammadUser);\n\n        user = allUsers.find(u =>\n          u.email === formData.email &&\n          u.password === formData.password &&\n          u.role === formData.role\n        );\n\n        console.log('User found in mock data:', user);\n\n        // If not found in mock data, check registered users\n        if (!user) {\n          const registeredUsers = JSON.parse(localStorage.getItem('ums_registered_users') || '[]');\n          console.log('Checking registered users:', registeredUsers.length);\n          user = registeredUsers.find(u =>\n            u.email === formData.email &&\n            u.password === formData.password &&\n            u.role === formData.role\n          );\n          console.log('User found in registered users:', user);\n        }\n      }\n\n      if (user) {\n        login(user);\n        navigate('/dashboard');\n      } else {\n        setErrors({ submit: 'Invalid email, password, or role' });\n      }\n    } catch (error) {\n      setErrors({ submit: 'Login failed. Please try again.' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div\n      className=\"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden\"\n      style={{\n        backgroundImage: `url(${backgroundImage})`,\n        backgroundSize: 'cover',\n        backgroundPosition: 'center',\n        backgroundRepeat: 'no-repeat',\n        backgroundAttachment: 'fixed'\n      }}\n    >\n      {/* Background Overlay for better contrast */}\n      <div className=\"absolute inset-0 bg-black/40 dark:bg-black/60\"></div>\n      {/* Academic Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        {/* Floating Academic Icons */}\n        <div className=\"absolute top-20 left-20 text-white/40 animate-float\">\n          <svg className=\"w-16 h-16\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z\"/>\n          </svg>\n        </div>\n\n        <div className=\"absolute top-32 right-32 text-white/35 animate-float\" style={{animationDelay: '1s'}}>\n          <svg className=\"w-20 h-20\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z\"/>\n          </svg>\n        </div>\n\n        <div className=\"absolute bottom-32 left-32 text-white/35 animate-float\" style={{animationDelay: '2s'}}>\n          <svg className=\"w-18 h-18\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H18V1h-2v1H8V1H6v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2zm0 16h-15v-2h15v2zm0-5h-15V6h15v7z\"/>\n          </svg>\n        </div>\n\n        <div className=\"absolute top-1/2 right-20 text-white/30 animate-float\" style={{animationDelay: '3s'}}>\n          <svg className=\"w-24 h-24\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n          </svg>\n        </div>\n\n        <div className=\"absolute bottom-20 right-40 text-white/35 animate-float\" style={{animationDelay: '4s'}}>\n          <svg className=\"w-14 h-14\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z\"/>\n          </svg>\n        </div>\n\n        {/* Mathematical Formulas Background */}\n        <div className=\"absolute top-16 left-1/2 transform -translate-x-1/2 text-white/25 font-mono text-sm animate-fade-in-out\">\n          ∫ f(x)dx = F(x) + C\n        </div>\n\n        <div className=\"absolute bottom-40 left-16 text-white/25 font-mono text-xs animate-fade-in-out\" style={{animationDelay: '2s'}}>\n          E = mc²\n        </div>\n\n        <div className=\"absolute top-40 right-16 text-white/25 font-mono text-xs animate-fade-in-out\" style={{animationDelay: '3s'}}>\n          f(x) = ax² + bx + c\n        </div>\n\n        <div className=\"absolute bottom-16 left-1/2 transform -translate-x-1/2 text-white/25 font-mono text-sm animate-fade-in-out\" style={{animationDelay: '1s'}}>\n          lim(x→∞) f(x) = L\n        </div>\n\n        {/* Geometric Patterns */}\n        <div className=\"absolute top-1/4 left-1/4 w-32 h-32 border border-white/20 rotate-45 animate-spin-slow\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-24 h-24 border border-white/20 rotate-12 animate-spin-reverse\"></div>\n\n        {/* Glowing Orbs */}\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-primary-500/20 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-primary-700/20 rounded-full blur-3xl animate-pulse\" style={{animationDelay: '2s'}}></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary-400/10 rounded-full blur-3xl animate-pulse\" style={{animationDelay: '4s'}}></div>\n\n        {/* Academic Grid Pattern */}\n        <div className=\"absolute inset-0 opacity-5\">\n          <div className=\"grid grid-cols-12 gap-4 h-full\">\n            {Array.from({length: 144}).map((_, i) => (\n              <div key={i} className=\"border border-white/10 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-md w-full space-y-8 relative z-10\">\n        <div className=\"text-center animate-fade-in-up\">\n          {/* University Logo */}\n          <div className=\"mx-auto mb-4 animate-bounce\">\n            <UniversityLogo\n              className=\"h-20 w-20 mx-auto animate-float hover-scale\"\n              showUpload={false}\n            />\n          </div>\n\n          {/* University Name */}\n          <h1 className=\"text-3xl font-bold gradient-text mb-2 animate-slide-in-left hover-glow\">\n            NFC IET MULTAN\n          </h1>\n          <p className=\"text-lg text-gray-300 font-semibold mb-1 animate-slide-in-left\" style={{animationDelay: '0.1s'}}>\n            National Fertilizer Corporation\n          </p>\n          <p className=\"text-base text-gray-400 font-medium mb-4 animate-slide-in-left\" style={{animationDelay: '0.2s'}}>\n            Institute of Engineering & Technology\n          </p>\n\n          {/* Welcome Message */}\n          <h2 className=\"text-2xl font-bold text-white mb-2 animate-slide-in-right\" style={{animationDelay: '0.3s'}}>\n            Welcome Back\n          </h2>\n          <p className=\"text-sm text-gray-400 animate-slide-in-right\" style={{animationDelay: '0.4s'}}>\n            Sign in to access your dashboard\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6 glass-dark p-8 rounded-2xl border border-primary-600/20 shadow-2xl animate-scale-in hover-lift\" onSubmit={handleSubmit}>\n          <div className=\"space-y-6\">\n            {/* Role Selection */}\n            <div className=\"animate-fade-in-up\" style={{animationDelay: '0.5s'}}>\n              <label htmlFor=\"role\" className=\"block text-sm font-semibold text-gray-200 mb-2\">\n                Login as\n              </label>\n              <select\n                id=\"role\"\n                name=\"role\"\n                value={formData.role}\n                onChange={handleChange}\n                className=\"modern-input-dark block w-full px-4 py-3 border-2 border-primary-600/30 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300\"\n              >\n                <option value=\"student\" className=\"bg-dark-800 text-white\">Student</option>\n                <option value=\"faculty\" className=\"bg-dark-800 text-white\">Faculty</option>\n                <option value=\"admin\" className=\"bg-dark-800 text-white\">Admin</option>\n              </select>\n            </div>\n\n            {/* Email */}\n            <div className=\"animate-fade-in-up\" style={{animationDelay: '0.6s'}}>\n              <label htmlFor=\"email\" className=\"block text-sm font-semibold text-gray-200 mb-2\">\n                Email address\n              </label>\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <FiUser className=\"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\" />\n                </div>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  className={`modern-input-dark block w-full pl-12 pr-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${\n                    errors.email ? 'border-red-400' : 'border-primary-600/30'\n                  }`}\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n              {errors.email && (\n                <p className=\"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\">{errors.email}</p>\n              )}\n            </div>\n\n            {/* Password */}\n            <div className=\"animate-fade-in-up\" style={{animationDelay: '0.7s'}}>\n              <label htmlFor=\"password\" className=\"block text-sm font-semibold text-gray-200 mb-2\">\n                Password\n              </label>\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <FiLock className=\"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\" />\n                </div>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  className={`modern-input-dark block w-full pl-12 pr-12 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${\n                    errors.password ? 'border-red-400' : 'border-primary-600/30'\n                  }`}\n                  placeholder=\"Enter your password\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-4 flex items-center text-primary-400 hover:text-primary-300 transition-colors duration-200 hover-scale\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <FiEyeOff className=\"h-5 w-5 animate-bounce\" />\n                  ) : (\n                    <FiEye className=\"h-5 w-5\" />\n                  )}\n                </button>\n              </div>\n              {errors.password && (\n                <p className=\"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\">{errors.password}</p>\n              )}\n            </div>\n          </div>\n\n          {errors.submit && (\n            <div className=\"bg-red-500/10 border border-red-400/30 rounded-xl p-4 animate-slide-down\">\n              <p className=\"text-sm text-red-400 font-medium\">{errors.submit}</p>\n            </div>\n          )}\n\n          <div className=\"animate-fade-in-up\" style={{animationDelay: '0.8s'}}>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"btn-primary group relative w-full flex justify-center py-4 px-6 border border-transparent text-sm font-bold rounded-xl text-white focus:outline-none focus-ring disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none transition-all duration-300 hover-lift animate-glow\"\n            >\n              {loading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Signing in...\n                </div>\n              ) : (\n                'Sign in'\n              )}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-300\">\n              Don't have an account?{' '}\n              <Link to=\"/register\" className=\"font-semibold text-primary-400 hover:text-primary-300 transition-colors duration-200\">\n                Register here\n              </Link>\n            </p>\n          </div>\n        </form>\n\n        {/* Demo credentials */}\n        <div className=\"mt-8 glass-dark border border-primary-600/20 rounded-xl p-6 animate-scale-in hover-lift\" style={{animationDelay: '1s'}}>\n          <h3 className=\"text-sm font-bold text-primary-300 mb-3 flex items-center animate-slide-in-left\">\n            <span className=\"w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse\"></span>\n            Demo Credentials:\n          </h3>\n          <div className=\"text-sm text-gray-300 space-y-2\">\n            <div className=\"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\" style={{animationDelay: '1.1s'}}>\n              <span className=\"font-semibold text-primary-400\">Admin:</span>\n              <span className=\"text-xs\"><EMAIL> / admin123</span>\n            </div>\n            <div className=\"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\" style={{animationDelay: '1.2s'}}>\n              <span className=\"font-semibold text-primary-400\">Faculty:</span>\n              <span className=\"text-xs\"><EMAIL> / password123</span>\n            </div>\n            <div className=\"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\" style={{animationDelay: '1.3s'}}>\n              <span className=\"font-semibold text-primary-400\">Student:</span>\n              <span className=\"text-xs\"><EMAIL> / password123</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AAChE,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,eAAe,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAE2B;EAAM,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAC3B,MAAM;IAAEqB,QAAQ;IAAEC;EAAQ,CAAC,GAAGrB,OAAO,CAAC,CAAC;EACvC,MAAMsB,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAE9B,MAAM6B,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClB,WAAW,CAACmB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIX,MAAM,CAACU,IAAI,CAAC,EAAE;MAChBT,SAAS,CAACY,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACtB,QAAQ,CAACE,KAAK,EAAE;MACnBoB,SAAS,CAACpB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAACT,aAAa,CAACO,QAAQ,CAACE,KAAK,CAAC,EAAE;MACzCoB,SAAS,CAACpB,KAAK,GAAG,4BAA4B;IAChD;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBmB,SAAS,CAACnB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIH,QAAQ,CAACG,QAAQ,CAACoB,MAAM,GAAG,CAAC,EAAE;MACvCD,SAAS,CAACnB,QAAQ,GAAG,wCAAwC;IAC/D;IAEAK,SAAS,CAACc,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACC,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOV,CAAC,IAAK;IAChCA,CAAC,CAACW,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;IAErBX,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM,IAAIkB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,IAAIE,IAAI,GAAG,IAAI;;MAEf;MACA,IAAI/B,QAAQ,CAACE,KAAK,KAAK,sBAAsB,IAAIF,QAAQ,CAACG,QAAQ,KAAK,UAAU,EAAE;QACjF4B,IAAI,GAAG;UACLC,EAAE,EAAE,QAAQ;UACZf,IAAI,EAAE,YAAY;UAClBf,KAAK,EAAE,sBAAsB;UAC7BE,IAAI,EAAE,OAAO;UACb6B,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE,oCAAoC;UAC7CC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMC,QAAQ,GAAG,CAAC,GAAGxB,QAAQ,EAAE,GAAGC,OAAO,CAAC;;QAE1C;QACAwB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;UAC5BpC,KAAK,EAAEF,QAAQ,CAACE,KAAK;UACrBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;UAC3BC,IAAI,EAAEJ,QAAQ,CAACI;QACjB,CAAC,CAAC;QACFiC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE1B,QAAQ,CAACW,MAAM,CAAC;QACnDc,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEzB,OAAO,CAACU,MAAM,CAAC;QACjDc,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEtC,QAAQ,CAACE,KAAK,CAAC;;QAE3D;QACA,MAAMqC,UAAU,GAAG3B,QAAQ,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvC,KAAK,KAAK,8BAA8B,CAAC;QACjFmC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,UAAU,CAAC;QAE7CR,IAAI,GAAGK,QAAQ,CAACI,IAAI,CAACE,CAAC,IACpBA,CAAC,CAACxC,KAAK,KAAKF,QAAQ,CAACE,KAAK,IAC1BwC,CAAC,CAACvC,QAAQ,KAAKH,QAAQ,CAACG,QAAQ,IAChCuC,CAAC,CAACtC,IAAI,KAAKJ,QAAQ,CAACI,IACtB,CAAC;QAEDiC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEP,IAAI,CAAC;;QAE7C;QACA,IAAI,CAACA,IAAI,EAAE;UACT,MAAMY,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,IAAI,CAAC;UACxFV,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEK,eAAe,CAACpB,MAAM,CAAC;UACjEQ,IAAI,GAAGY,eAAe,CAACH,IAAI,CAACE,CAAC,IAC3BA,CAAC,CAACxC,KAAK,KAAKF,QAAQ,CAACE,KAAK,IAC1BwC,CAAC,CAACvC,QAAQ,KAAKH,QAAQ,CAACG,QAAQ,IAChCuC,CAAC,CAACtC,IAAI,KAAKJ,QAAQ,CAACI,IACtB,CAAC;UACDiC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEP,IAAI,CAAC;QACtD;MACF;MAEA,IAAIA,IAAI,EAAE;QACRpB,KAAK,CAACoB,IAAI,CAAC;QACXjB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,MAAM;QACLN,SAAS,CAAC;UAAEwC,MAAM,EAAE;QAAmC,CAAC,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdzC,SAAS,CAAC;QAAEwC,MAAM,EAAE;MAAkC,CAAC,CAAC;IAC1D,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEb,OAAA;IACEqD,SAAS,EAAC,mGAAmG;IAC7GC,KAAK,EAAE;MACLxD,eAAe,EAAE,OAAOA,eAAe,GAAG;MAC1CyD,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE,WAAW;MAC7BC,oBAAoB,EAAE;IACxB,CAAE;IAAAC,QAAA,gBAGF3D,OAAA;MAAKqD,SAAS,EAAC;IAA+C;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAErE/D,OAAA;MAAKqD,SAAS,EAAC,kCAAkC;MAAAM,QAAA,gBAE/C3D,OAAA;QAAKqD,SAAS,EAAC,qDAAqD;QAAAM,QAAA,eAClE3D,OAAA;UAAKqD,SAAS,EAAC,WAAW;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAN,QAAA,eAChE3D,OAAA;YAAMkE,CAAC,EAAC;UAAiJ;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/D,OAAA;QAAKqD,SAAS,EAAC,sDAAsD;QAACC,KAAK,EAAE;UAACa,cAAc,EAAE;QAAI,CAAE;QAAAR,QAAA,eAClG3D,OAAA;UAAKqD,SAAS,EAAC,WAAW;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAN,QAAA,eAChE3D,OAAA;YAAMkE,CAAC,EAAC;UAA2H;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/D,OAAA;QAAKqD,SAAS,EAAC,wDAAwD;QAACC,KAAK,EAAE;UAACa,cAAc,EAAE;QAAI,CAAE;QAAAR,QAAA,eACpG3D,OAAA;UAAKqD,SAAS,EAAC,WAAW;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAN,QAAA,eAChE3D,OAAA;YAAMkE,CAAC,EAAC;UAAgN;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/D,OAAA;QAAKqD,SAAS,EAAC,uDAAuD;QAACC,KAAK,EAAE;UAACa,cAAc,EAAE;QAAI,CAAE;QAAAR,QAAA,eACnG3D,OAAA;UAAKqD,SAAS,EAAC,WAAW;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAN,QAAA,eAChE3D,OAAA;YAAMkE,CAAC,EAAC;UAA8F;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/D,OAAA;QAAKqD,SAAS,EAAC,yDAAyD;QAACC,KAAK,EAAE;UAACa,cAAc,EAAE;QAAI,CAAE;QAAAR,QAAA,eACrG3D,OAAA;UAAKqD,SAAS,EAAC,WAAW;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAN,QAAA,eAChE3D,OAAA;YAAMkE,CAAC,EAAC;UAA6E;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/D,OAAA;QAAKqD,SAAS,EAAC,yGAAyG;QAAAM,QAAA,EAAC;MAEzH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEN/D,OAAA;QAAKqD,SAAS,EAAC,gFAAgF;QAACC,KAAK,EAAE;UAACa,cAAc,EAAE;QAAI,CAAE;QAAAR,QAAA,EAAC;MAE/H;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEN/D,OAAA;QAAKqD,SAAS,EAAC,8EAA8E;QAACC,KAAK,EAAE;UAACa,cAAc,EAAE;QAAI,CAAE;QAAAR,QAAA,EAAC;MAE7H;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEN/D,OAAA;QAAKqD,SAAS,EAAC,4GAA4G;QAACC,KAAK,EAAE;UAACa,cAAc,EAAE;QAAI,CAAE;QAAAR,QAAA,EAAC;MAE3J;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGN/D,OAAA;QAAKqD,SAAS,EAAC;MAAwF;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9G/D,OAAA;QAAKqD,SAAS,EAAC;MAA+F;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGrH/D,OAAA;QAAKqD,SAAS,EAAC;MAA4F;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClH/D,OAAA;QAAKqD,SAAS,EAAC,8FAA8F;QAACC,KAAK,EAAE;UAACa,cAAc,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnJ/D,OAAA;QAAKqD,SAAS,EAAC,uIAAuI;QAACC,KAAK,EAAE;UAACa,cAAc,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG5L/D,OAAA;QAAKqD,SAAS,EAAC,4BAA4B;QAAAM,QAAA,eACzC3D,OAAA;UAAKqD,SAAS,EAAC,gCAAgC;UAAAM,QAAA,EAC5CS,KAAK,CAACC,IAAI,CAAC;YAAC3C,MAAM,EAAE;UAAG,CAAC,CAAC,CAAC4C,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAClCxE,OAAA;YAAaqD,SAAS,EAAC;UAAgC,GAA7CmB,CAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/D,OAAA;MAAKqD,SAAS,EAAC,yCAAyC;MAAAM,QAAA,gBACtD3D,OAAA;QAAKqD,SAAS,EAAC,gCAAgC;QAAAM,QAAA,gBAE7C3D,OAAA;UAAKqD,SAAS,EAAC,6BAA6B;UAAAM,QAAA,eAC1C3D,OAAA,CAACH,cAAc;YACbwD,SAAS,EAAC,6CAA6C;YACvDoB,UAAU,EAAE;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/D,OAAA;UAAIqD,SAAS,EAAC,wEAAwE;UAAAM,QAAA,EAAC;QAEvF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/D,OAAA;UAAGqD,SAAS,EAAC,gEAAgE;UAACC,KAAK,EAAE;YAACa,cAAc,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAE/G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ/D,OAAA;UAAGqD,SAAS,EAAC,gEAAgE;UAACC,KAAK,EAAE;YAACa,cAAc,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAE/G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGJ/D,OAAA;UAAIqD,SAAS,EAAC,2DAA2D;UAACC,KAAK,EAAE;YAACa,cAAc,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAE3G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/D,OAAA;UAAGqD,SAAS,EAAC,8CAA8C;UAACC,KAAK,EAAE;YAACa,cAAc,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAE7F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN/D,OAAA;QAAMqD,SAAS,EAAC,+GAA+G;QAACqB,QAAQ,EAAE7C,YAAa;QAAA8B,QAAA,gBACrJ3D,OAAA;UAAKqD,SAAS,EAAC,WAAW;UAAAM,QAAA,gBAExB3D,OAAA;YAAKqD,SAAS,EAAC,oBAAoB;YAACC,KAAK,EAAE;cAACa,cAAc,EAAE;YAAM,CAAE;YAAAR,QAAA,gBAClE3D,OAAA;cAAO2E,OAAO,EAAC,MAAM;cAACtB,SAAS,EAAC,gDAAgD;cAAAM,QAAA,EAAC;YAEjF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA;cACEmC,EAAE,EAAC,MAAM;cACTf,IAAI,EAAC,MAAM;cACXC,KAAK,EAAElB,QAAQ,CAACI,IAAK;cACrBqE,QAAQ,EAAE1D,YAAa;cACvBmC,SAAS,EAAC,iOAAiO;cAAAM,QAAA,gBAE3O3D,OAAA;gBAAQqB,KAAK,EAAC,SAAS;gBAACgC,SAAS,EAAC,wBAAwB;gBAAAM,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3E/D,OAAA;gBAAQqB,KAAK,EAAC,SAAS;gBAACgC,SAAS,EAAC,wBAAwB;gBAAAM,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3E/D,OAAA;gBAAQqB,KAAK,EAAC,OAAO;gBAACgC,SAAS,EAAC,wBAAwB;gBAAAM,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN/D,OAAA;YAAKqD,SAAS,EAAC,oBAAoB;YAACC,KAAK,EAAE;cAACa,cAAc,EAAE;YAAM,CAAE;YAAAR,QAAA,gBAClE3D,OAAA;cAAO2E,OAAO,EAAC,OAAO;cAACtB,SAAS,EAAC,gDAAgD;cAAAM,QAAA,EAAC;YAElF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA;cAAKqD,SAAS,EAAC,gBAAgB;cAAAM,QAAA,gBAC7B3D,OAAA;gBAAKqD,SAAS,EAAC,sEAAsE;gBAAAM,QAAA,eACnF3D,OAAA,CAACR,MAAM;kBAAC6D,SAAS,EAAC;gBAAsF;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC,eACN/D,OAAA;gBACEmC,EAAE,EAAC,OAAO;gBACVf,IAAI,EAAC,OAAO;gBACZyD,IAAI,EAAC,OAAO;gBACZC,YAAY,EAAC,OAAO;gBACpBzD,KAAK,EAAElB,QAAQ,CAACE,KAAM;gBACtBuE,QAAQ,EAAE1D,YAAa;gBACvBmC,SAAS,EAAE,mNACT3C,MAAM,CAACL,KAAK,GAAG,gBAAgB,GAAG,uBAAuB,EACxD;gBACH0E,WAAW,EAAC;cAAkB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACLrD,MAAM,CAACL,KAAK,iBACXL,OAAA;cAAGqD,SAAS,EAAC,6DAA6D;cAAAM,QAAA,EAAEjD,MAAM,CAACL;YAAK;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC7F;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN/D,OAAA;YAAKqD,SAAS,EAAC,oBAAoB;YAACC,KAAK,EAAE;cAACa,cAAc,EAAE;YAAM,CAAE;YAAAR,QAAA,gBAClE3D,OAAA;cAAO2E,OAAO,EAAC,UAAU;cAACtB,SAAS,EAAC,gDAAgD;cAAAM,QAAA,EAAC;YAErF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA;cAAKqD,SAAS,EAAC,gBAAgB;cAAAM,QAAA,gBAC7B3D,OAAA;gBAAKqD,SAAS,EAAC,sEAAsE;gBAAAM,QAAA,eACnF3D,OAAA,CAACP,MAAM;kBAAC4D,SAAS,EAAC;gBAAsF;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC,eACN/D,OAAA;gBACEmC,EAAE,EAAC,UAAU;gBACbf,IAAI,EAAC,UAAU;gBACfyD,IAAI,EAAErE,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCsE,YAAY,EAAC,kBAAkB;gBAC/BzD,KAAK,EAAElB,QAAQ,CAACG,QAAS;gBACzBsE,QAAQ,EAAE1D,YAAa;gBACvBmC,SAAS,EAAE,oNACT3C,MAAM,CAACJ,QAAQ,GAAG,gBAAgB,GAAG,uBAAuB,EAC3D;gBACHyE,WAAW,EAAC;cAAqB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACF/D,OAAA;gBACE6E,IAAI,EAAC,QAAQ;gBACbxB,SAAS,EAAC,sIAAsI;gBAChJ2B,OAAO,EAAEA,CAAA,KAAMvE,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAAmD,QAAA,EAE7CnD,YAAY,gBACXR,OAAA,CAACT,QAAQ;kBAAC8D,SAAS,EAAC;gBAAwB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/C/D,OAAA,CAACV,KAAK;kBAAC+D,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC7B;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLrD,MAAM,CAACJ,QAAQ,iBACdN,OAAA;cAAGqD,SAAS,EAAC,6DAA6D;cAAAM,QAAA,EAAEjD,MAAM,CAACJ;YAAQ;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAChG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELrD,MAAM,CAACyC,MAAM,iBACZnD,OAAA;UAAKqD,SAAS,EAAC,0EAA0E;UAAAM,QAAA,eACvF3D,OAAA;YAAGqD,SAAS,EAAC,kCAAkC;YAAAM,QAAA,EAAEjD,MAAM,CAACyC;UAAM;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACN,eAED/D,OAAA;UAAKqD,SAAS,EAAC,oBAAoB;UAACC,KAAK,EAAE;YAACa,cAAc,EAAE;UAAM,CAAE;UAAAR,QAAA,eAClE3D,OAAA;YACE6E,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAErE,OAAQ;YAClByC,SAAS,EAAC,6RAA6R;YAAAM,QAAA,EAEtS/C,OAAO,gBACNZ,OAAA;cAAKqD,SAAS,EAAC,mBAAmB;cAAAM,QAAA,gBAChC3D,OAAA;gBAAKqD,SAAS,EAAC;cAAgE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,iBAExF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN/D,OAAA;UAAKqD,SAAS,EAAC,aAAa;UAAAM,QAAA,eAC1B3D,OAAA;YAAGqD,SAAS,EAAC,uBAAuB;YAAAM,QAAA,GAAC,wBACb,EAAC,GAAG,eAC1B3D,OAAA,CAACZ,IAAI;cAAC8F,EAAE,EAAC,WAAW;cAAC7B,SAAS,EAAC,sFAAsF;cAAAM,QAAA,EAAC;YAEtH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGP/D,OAAA;QAAKqD,SAAS,EAAC,yFAAyF;QAACC,KAAK,EAAE;UAACa,cAAc,EAAE;QAAI,CAAE;QAAAR,QAAA,gBACrI3D,OAAA;UAAIqD,SAAS,EAAC,iFAAiF;UAAAM,QAAA,gBAC7F3D,OAAA;YAAMqD,SAAS,EAAC;UAAwD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,qBAElF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/D,OAAA;UAAKqD,SAAS,EAAC,iCAAiC;UAAAM,QAAA,gBAC9C3D,OAAA;YAAKqD,SAAS,EAAC,qGAAqG;YAACC,KAAK,EAAE;cAACa,cAAc,EAAE;YAAM,CAAE;YAAAR,QAAA,gBACnJ3D,OAAA;cAAMqD,SAAS,EAAC,gCAAgC;cAAAM,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9D/D,OAAA;cAAMqD,SAAS,EAAC,SAAS;cAAAM,QAAA,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACN/D,OAAA;YAAKqD,SAAS,EAAC,qGAAqG;YAACC,KAAK,EAAE;cAACa,cAAc,EAAE;YAAM,CAAE;YAAAR,QAAA,gBACnJ3D,OAAA;cAAMqD,SAAS,EAAC,gCAAgC;cAAAM,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChE/D,OAAA;cAAMqD,SAAS,EAAC,SAAS;cAAAM,QAAA,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACN/D,OAAA;YAAKqD,SAAS,EAAC,qGAAqG;YAACC,KAAK,EAAE;cAACa,cAAc,EAAE;YAAM,CAAE;YAAAR,QAAA,gBACnJ3D,OAAA;cAAMqD,SAAS,EAAC,gCAAgC;cAAAM,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChE/D,OAAA;cAAMqD,SAAS,EAAC,SAAS;cAAAM,QAAA,EAAC;YAA0C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA3XID,KAAK;EAAA,QAUSP,OAAO,EACKC,OAAO,EACpBN,WAAW;AAAA;AAAA8F,EAAA,GAZxBlF,KAAK;AA6XX,eAAeA,KAAK;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}