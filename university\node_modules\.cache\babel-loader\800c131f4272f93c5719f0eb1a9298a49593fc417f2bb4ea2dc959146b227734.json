{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\nimport { DataProvider } from './context/DataContext';\nimport Layout from './components/common/Layout';\nimport Login from './components/auth/Login';\nimport Register from './components/auth/Register';\nimport Dashboard from './components/dashboard/Dashboard';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport MyCourses from './components/courses/MyCourses';\nimport Courses from './components/courses/Courses';\nimport ComingSoon from './components/common/ComingSoon';\nimport MyGrades from './components/student/MyGrades';\nimport MyAttendance from './components/student/MyAttendance';\nimport MyFees from './components/student/MyFees';\nimport MyExams from './components/student/MyExams';\nimport Library from './components/library/Library';\nimport InstructorManagement from './components/admin/InstructorManagement';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(DataProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App dark min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 40\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                index: true,\n                element: /*#__PURE__*/_jsxDEV(Navigate, {\n                  to: \"/dashboard\",\n                  replace: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 39\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"dashboard\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 35,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-courses\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(MyCourses, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 43,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"courses\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(Courses, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 51,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"students\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Student Management\",\n                    description: \"Manage student records, enrollment, and academic information.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 61,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"faculty\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(InstructorManagement, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 69,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"academic\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Academic Management\",\n                    description: \"Manage academic records, grades, and transcripts.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 77,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"fees\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Fee Management\",\n                    description: \"Manage fee structures, payments, and financial records.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"library\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Library, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"exams\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Examination System\",\n                    description: \"Schedule exams, manage results, and track performance.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"reports\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Reports & Analytics\",\n                    description: \"Generate comprehensive reports and analytics.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"settings\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"System Settings\",\n                    description: \"Configure system settings and preferences.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-students\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"faculty\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"My Students\",\n                    description: \"View and manage students in your courses.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"grades\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"faculty\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Grade Management\",\n                    description: \"Manage student grades and assessments.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"attendance\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"faculty\",\n                  children: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                    title: \"Attendance Management\",\n                    description: \"Track and manage student attendance.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-grades\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"student\",\n                  children: /*#__PURE__*/_jsxDEV(MyGrades, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-attendance\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"student\",\n                  children: /*#__PURE__*/_jsxDEV(MyAttendance, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-fees\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"student\",\n                  children: /*#__PURE__*/_jsxDEV(MyFees, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-exams\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"student\",\n                  children: /*#__PURE__*/_jsxDEV(MyExams, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"*\",\n                element: /*#__PURE__*/_jsxDEV(ComingSoon, {\n                  title: \"Page Not Found\",\n                  description: \"The page you're looking for doesn't exist or is under development.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "DataProvider", "Layout", "<PERSON><PERSON>", "Register", "Dashboard", "ProtectedRoute", "MyCourses", "Courses", "ComingSoon", "MyGrades", "MyAttendance", "MyFees", "MyExams", "Library", "InstructorManagement", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "to", "replace", "requiredRole", "title", "description", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\nimport { DataProvider } from './context/DataContext';\nimport Layout from './components/common/Layout';\nimport Login from './components/auth/Login';\nimport Register from './components/auth/Register';\nimport Dashboard from './components/dashboard/Dashboard';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport MyCourses from './components/courses/MyCourses';\nimport Courses from './components/courses/Courses';\nimport ComingSoon from './components/common/ComingSoon';\nimport MyGrades from './components/student/MyGrades';\nimport MyAttendance from './components/student/MyAttendance';\nimport MyFees from './components/student/MyFees';\nimport MyExams from './components/student/MyExams';\nimport Library from './components/library/Library';\nimport InstructorManagement from './components/admin/InstructorManagement';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <DataProvider>\n        <Router>\n          <div className=\"App dark min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700\">\n            <Routes>\n              <Route path=\"/login\" element={<Login />} />\n              <Route path=\"/register\" element={<Register />} />\n              <Route path=\"/\" element={<Layout />}>\n                <Route index element={<Navigate to=\"/dashboard\" replace />} />\n                <Route\n                  path=\"dashboard\"\n                  element={\n                    <ProtectedRoute>\n                      <Dashboard />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"my-courses\"\n                  element={\n                    <ProtectedRoute>\n                      <MyCourses />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"courses\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <Courses />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Admin Routes */}\n                <Route\n                  path=\"students\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"Student Management\" description=\"Manage student records, enrollment, and academic information.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"faculty\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <InstructorManagement />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"academic\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"Academic Management\" description=\"Manage academic records, grades, and transcripts.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"fees\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"Fee Management\" description=\"Manage fee structures, payments, and financial records.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"library\"\n                  element={\n                    <ProtectedRoute>\n                      <Library />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"exams\"\n                  element={\n                    <ProtectedRoute>\n                      <ComingSoon title=\"Examination System\" description=\"Schedule exams, manage results, and track performance.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"reports\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"Reports & Analytics\" description=\"Generate comprehensive reports and analytics.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"settings\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <ComingSoon title=\"System Settings\" description=\"Configure system settings and preferences.\" />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Faculty Routes */}\n                <Route\n                  path=\"my-students\"\n                  element={\n                    <ProtectedRoute requiredRole=\"faculty\">\n                      <ComingSoon title=\"My Students\" description=\"View and manage students in your courses.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"grades\"\n                  element={\n                    <ProtectedRoute requiredRole=\"faculty\">\n                      <ComingSoon title=\"Grade Management\" description=\"Manage student grades and assessments.\" />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"attendance\"\n                  element={\n                    <ProtectedRoute requiredRole=\"faculty\">\n                      <ComingSoon title=\"Attendance Management\" description=\"Track and manage student attendance.\" />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Student Routes */}\n                <Route\n                  path=\"my-grades\"\n                  element={\n                    <ProtectedRoute requiredRole=\"student\">\n                      <MyGrades />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"my-attendance\"\n                  element={\n                    <ProtectedRoute requiredRole=\"student\">\n                      <MyAttendance />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"my-fees\"\n                  element={\n                    <ProtectedRoute requiredRole=\"student\">\n                      <MyFees />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"my-exams\"\n                  element={\n                    <ProtectedRoute requiredRole=\"student\">\n                      <MyExams />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Catch all route */}\n                <Route\n                  path=\"*\"\n                  element={\n                    <ComingSoon title=\"Page Not Found\" description=\"The page you're looking for doesn't exist or is under development.\" />\n                  }\n                />\n              </Route>\n            </Routes>\n          </div>\n        </Router>\n      </DataProvider>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,SAAS,MAAM,gCAAgC;AACtD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,oBAAoB,MAAM,yCAAyC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACjB,YAAY;IAAAmB,QAAA,eACXF,OAAA,CAAChB,YAAY;MAAAkB,QAAA,eACXF,OAAA,CAACrB,MAAM;QAAAuB,QAAA,eACLF,OAAA;UAAKG,SAAS,EAAC,gFAAgF;UAAAD,QAAA,eAC7FF,OAAA,CAACpB,MAAM;YAAAsB,QAAA,gBACLF,OAAA,CAACnB,KAAK;cAACuB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEL,OAAA,CAACd,KAAK;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CT,OAAA,CAACnB,KAAK;cAACuB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEL,OAAA,CAACb,QAAQ;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDT,OAAA,CAACnB,KAAK;cAACuB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEL,OAAA,CAACf,MAAM;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAP,QAAA,gBAClCF,OAAA,CAACnB,KAAK;gBAAC6B,KAAK;gBAACL,OAAO,eAAEL,OAAA,CAAClB,QAAQ;kBAAC6B,EAAE,EAAC,YAAY;kBAACC,OAAO;gBAAA;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,WAAW;gBAChBC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAAAa,QAAA,eACbF,OAAA,CAACZ,SAAS;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,YAAY;gBACjBC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAAAa,QAAA,eACbF,OAAA,CAACV,SAAS;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACT,OAAO;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,UAAU;gBACfC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACR,UAAU;oBAACsB,KAAK,EAAC,oBAAoB;oBAACC,WAAW,EAAC;kBAA+D;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACF,oBAAoB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,UAAU;gBACfC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACR,UAAU;oBAACsB,KAAK,EAAC,qBAAqB;oBAACC,WAAW,EAAC;kBAAmD;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,MAAM;gBACXC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACR,UAAU;oBAACsB,KAAK,EAAC,gBAAgB;oBAACC,WAAW,EAAC;kBAAyD;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAAAa,QAAA,eACbF,OAAA,CAACH,OAAO;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,OAAO;gBACZC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAAAa,QAAA,eACbF,OAAA,CAACR,UAAU;oBAACsB,KAAK,EAAC,oBAAoB;oBAACC,WAAW,EAAC;kBAAwD;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACR,UAAU;oBAACsB,KAAK,EAAC,qBAAqB;oBAACC,WAAW,EAAC;kBAA+C;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,UAAU;gBACfC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACR,UAAU;oBAACsB,KAAK,EAAC,iBAAiB;oBAACC,WAAW,EAAC;kBAA4C;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,aAAa;gBAClBC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACR,UAAU;oBAACsB,KAAK,EAAC,aAAa;oBAACC,WAAW,EAAC;kBAA2C;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,QAAQ;gBACbC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACR,UAAU;oBAACsB,KAAK,EAAC,kBAAkB;oBAACC,WAAW,EAAC;kBAAwC;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,YAAY;gBACjBC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACR,UAAU;oBAACsB,KAAK,EAAC,uBAAuB;oBAACC,WAAW,EAAC;kBAAsC;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,WAAW;gBAChBC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACP,QAAQ;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,eAAe;gBACpBC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACN,YAAY;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACL,MAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,UAAU;gBACfC,OAAO,eACLL,OAAA,CAACX,cAAc;kBAACwB,YAAY,EAAC,SAAS;kBAAAX,QAAA,eACpCF,OAAA,CAACJ,OAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGFT,OAAA,CAACnB,KAAK;gBACJuB,IAAI,EAAC,GAAG;gBACRC,OAAO,eACLL,OAAA,CAACR,UAAU;kBAACsB,KAAK,EAAC,gBAAgB;kBAACC,WAAW,EAAC;gBAAoE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACtH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEnB;AAACO,EAAA,GAhLQf,GAAG;AAkLZ,eAAeA,GAAG;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}