{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\context\\\\DataContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { mockData } from '../data/mockData';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataContext = /*#__PURE__*/createContext();\nexport const useData = () => {\n  _s();\n  const context = useContext(DataContext);\n  if (!context) {\n    throw new Error('useData must be used within a DataProvider');\n  }\n  return context;\n};\n_s(useData, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const DataProvider = ({\n  children\n}) => {\n  _s2();\n  const [students, setStudents] = useState([]);\n  const [faculty, setFaculty] = useState([]);\n  const [courses, setCourses] = useState([]);\n  const [enrollments, setEnrollments] = useState([]);\n  const [grades, setGrades] = useState([]);\n  const [attendance, setAttendance] = useState([]);\n  const [fees, setFees] = useState([]);\n  const [books, setBooks] = useState([]);\n  const [borrowedBooks, setBorrowedBooks] = useState([]);\n  const [exams, setExams] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Load data from localStorage or use mock data\n    const loadData = () => {\n      try {\n        const savedStudents = localStorage.getItem('ums_students');\n        const savedFaculty = localStorage.getItem('ums_faculty');\n        const savedCourses = localStorage.getItem('ums_courses');\n        const savedEnrollments = localStorage.getItem('ums_enrollments');\n        const savedGrades = localStorage.getItem('ums_grades');\n        const savedAttendance = localStorage.getItem('ums_attendance');\n        const savedFees = localStorage.getItem('ums_fees');\n        const savedBooks = localStorage.getItem('ums_books');\n        const savedBorrowedBooks = localStorage.getItem('ums_borrowed_books');\n        const savedExams = localStorage.getItem('ums_exams');\n        setStudents(savedStudents ? JSON.parse(savedStudents) : mockData.students);\n        setFaculty(savedFaculty ? JSON.parse(savedFaculty) : mockData.faculty);\n        setCourses(savedCourses ? JSON.parse(savedCourses) : mockData.courses);\n        setEnrollments(savedEnrollments ? JSON.parse(savedEnrollments) : mockData.enrollments);\n        setGrades(savedGrades ? JSON.parse(savedGrades) : mockData.grades);\n        setAttendance(savedAttendance ? JSON.parse(savedAttendance) : mockData.attendance);\n        setFees(savedFees ? JSON.parse(savedFees) : mockData.fees);\n        setBooks(savedBooks ? JSON.parse(savedBooks) : mockData.books);\n        setBorrowedBooks(savedBorrowedBooks ? JSON.parse(savedBorrowedBooks) : mockData.borrowedBooks);\n        setExams(savedExams ? JSON.parse(savedExams) : mockData.exams);\n      } catch (error) {\n        console.error('Error loading data:', error);\n        // Fallback to mock data\n        setStudents(mockData.students);\n        setFaculty(mockData.faculty);\n        setCourses(mockData.courses);\n        setEnrollments(mockData.enrollments);\n        setGrades(mockData.grades);\n        setAttendance(mockData.attendance);\n        setFees(mockData.fees);\n        setBooks(mockData.books);\n        setBorrowedBooks(mockData.borrowedBooks);\n        setExams(mockData.exams);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n  }, []);\n\n  // Save data to localStorage whenever state changes\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_students', JSON.stringify(students));\n    }\n  }, [students, loading]);\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_faculty', JSON.stringify(faculty));\n    }\n  }, [faculty, loading]);\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_courses', JSON.stringify(courses));\n    }\n  }, [courses, loading]);\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_enrollments', JSON.stringify(enrollments));\n    }\n  }, [enrollments, loading]);\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_grades', JSON.stringify(grades));\n    }\n  }, [grades, loading]);\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_attendance', JSON.stringify(attendance));\n    }\n  }, [attendance, loading]);\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_fees', JSON.stringify(fees));\n    }\n  }, [fees, loading]);\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_books', JSON.stringify(books));\n    }\n  }, [books, loading]);\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_borrowed_books', JSON.stringify(borrowedBooks));\n    }\n  }, [borrowedBooks, loading]);\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_exams', JSON.stringify(exams));\n    }\n  }, [exams, loading]);\n  const value = {\n    students,\n    setStudents,\n    faculty,\n    setFaculty,\n    courses,\n    setCourses,\n    enrollments,\n    setEnrollments,\n    grades,\n    setGrades,\n    attendance,\n    setAttendance,\n    fees,\n    setFees,\n    books,\n    setBooks,\n    borrowedBooks,\n    setBorrowedBooks,\n    exams,\n    setExams,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(DataContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this);\n};\n_s2(DataProvider, \"l5QE/By/2NUlXRrrI5A3cih+Q3k=\");\n_c = DataProvider;\nvar _c;\n$RefreshReg$(_c, \"DataProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "mockData", "jsxDEV", "_jsxDEV", "DataContext", "useData", "_s", "context", "Error", "DataProvider", "children", "_s2", "students", "setStudents", "faculty", "set<PERSON><PERSON><PERSON><PERSON>", "courses", "setCourses", "enrollments", "setEnrollments", "grades", "setGrades", "attendance", "setAttendance", "fees", "setFees", "books", "setBooks", "borrowedBooks", "setBorrowedBooks", "exams", "setExams", "loading", "setLoading", "loadData", "savedStudents", "localStorage", "getItem", "savedFaculty", "savedCourses", "savedEnrollments", "savedGrades", "savedAttendance", "savedFees", "savedBooks", "savedBorrowedBooks", "savedExams", "JSON", "parse", "error", "console", "setItem", "stringify", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/context/DataContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { mockData } from '../data/mockData';\n\nconst DataContext = createContext();\n\nexport const useData = () => {\n  const context = useContext(DataContext);\n  if (!context) {\n    throw new Error('useData must be used within a DataProvider');\n  }\n  return context;\n};\n\nexport const DataProvider = ({ children }) => {\n  const [students, setStudents] = useState([]);\n  const [faculty, setFaculty] = useState([]);\n  const [courses, setCourses] = useState([]);\n  const [enrollments, setEnrollments] = useState([]);\n  const [grades, setGrades] = useState([]);\n  const [attendance, setAttendance] = useState([]);\n  const [fees, setFees] = useState([]);\n  const [books, setBooks] = useState([]);\n  const [borrowedBooks, setBorrowedBooks] = useState([]);\n  const [exams, setExams] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Load data from localStorage or use mock data\n    const loadData = () => {\n      try {\n        const savedStudents = localStorage.getItem('ums_students');\n        const savedFaculty = localStorage.getItem('ums_faculty');\n        const savedCourses = localStorage.getItem('ums_courses');\n        const savedEnrollments = localStorage.getItem('ums_enrollments');\n        const savedGrades = localStorage.getItem('ums_grades');\n        const savedAttendance = localStorage.getItem('ums_attendance');\n        const savedFees = localStorage.getItem('ums_fees');\n        const savedBooks = localStorage.getItem('ums_books');\n        const savedBorrowedBooks = localStorage.getItem('ums_borrowed_books');\n        const savedExams = localStorage.getItem('ums_exams');\n\n        setStudents(savedStudents ? JSON.parse(savedStudents) : mockData.students);\n        setFaculty(savedFaculty ? JSON.parse(savedFaculty) : mockData.faculty);\n        setCourses(savedCourses ? JSON.parse(savedCourses) : mockData.courses);\n        setEnrollments(savedEnrollments ? JSON.parse(savedEnrollments) : mockData.enrollments);\n        setGrades(savedGrades ? JSON.parse(savedGrades) : mockData.grades);\n        setAttendance(savedAttendance ? JSON.parse(savedAttendance) : mockData.attendance);\n        setFees(savedFees ? JSON.parse(savedFees) : mockData.fees);\n        setBooks(savedBooks ? JSON.parse(savedBooks) : mockData.books);\n        setBorrowedBooks(savedBorrowedBooks ? JSON.parse(savedBorrowedBooks) : mockData.borrowedBooks);\n        setExams(savedExams ? JSON.parse(savedExams) : mockData.exams);\n      } catch (error) {\n        console.error('Error loading data:', error);\n        // Fallback to mock data\n        setStudents(mockData.students);\n        setFaculty(mockData.faculty);\n        setCourses(mockData.courses);\n        setEnrollments(mockData.enrollments);\n        setGrades(mockData.grades);\n        setAttendance(mockData.attendance);\n        setFees(mockData.fees);\n        setBooks(mockData.books);\n        setBorrowedBooks(mockData.borrowedBooks);\n        setExams(mockData.exams);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Save data to localStorage whenever state changes\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_students', JSON.stringify(students));\n    }\n  }, [students, loading]);\n\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_faculty', JSON.stringify(faculty));\n    }\n  }, [faculty, loading]);\n\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_courses', JSON.stringify(courses));\n    }\n  }, [courses, loading]);\n\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_enrollments', JSON.stringify(enrollments));\n    }\n  }, [enrollments, loading]);\n\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_grades', JSON.stringify(grades));\n    }\n  }, [grades, loading]);\n\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_attendance', JSON.stringify(attendance));\n    }\n  }, [attendance, loading]);\n\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_fees', JSON.stringify(fees));\n    }\n  }, [fees, loading]);\n\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_books', JSON.stringify(books));\n    }\n  }, [books, loading]);\n\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_borrowed_books', JSON.stringify(borrowedBooks));\n    }\n  }, [borrowedBooks, loading]);\n\n  useEffect(() => {\n    if (!loading) {\n      localStorage.setItem('ums_exams', JSON.stringify(exams));\n    }\n  }, [exams, loading]);\n\n  const value = {\n    students,\n    setStudents,\n    faculty,\n    setFaculty,\n    courses,\n    setCourses,\n    enrollments,\n    setEnrollments,\n    grades,\n    setGrades,\n    attendance,\n    setAttendance,\n    fees,\n    setFees,\n    books,\n    setBooks,\n    borrowedBooks,\n    setBorrowedBooks,\n    exams,\n    setExams,\n    loading,\n  };\n\n  return (\n    <DataContext.Provider value={value}>\n      {children}\n    </DataContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,QAAQ,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,IAAI,EAAEC,OAAO,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMkC,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAI;QACF,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QAC1D,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QACxD,MAAME,YAAY,GAAGH,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QACxD,MAAMG,gBAAgB,GAAGJ,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;QAChE,MAAMI,WAAW,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;QACtD,MAAMK,eAAe,GAAGN,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;QAC9D,MAAMM,SAAS,GAAGP,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;QAClD,MAAMO,UAAU,GAAGR,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QACpD,MAAMQ,kBAAkB,GAAGT,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACrE,MAAMS,UAAU,GAAGV,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QAEpDxB,WAAW,CAACsB,aAAa,GAAGY,IAAI,CAACC,KAAK,CAACb,aAAa,CAAC,GAAGlC,QAAQ,CAACW,QAAQ,CAAC;QAC1EG,UAAU,CAACuB,YAAY,GAAGS,IAAI,CAACC,KAAK,CAACV,YAAY,CAAC,GAAGrC,QAAQ,CAACa,OAAO,CAAC;QACtEG,UAAU,CAACsB,YAAY,GAAGQ,IAAI,CAACC,KAAK,CAACT,YAAY,CAAC,GAAGtC,QAAQ,CAACe,OAAO,CAAC;QACtEG,cAAc,CAACqB,gBAAgB,GAAGO,IAAI,CAACC,KAAK,CAACR,gBAAgB,CAAC,GAAGvC,QAAQ,CAACiB,WAAW,CAAC;QACtFG,SAAS,CAACoB,WAAW,GAAGM,IAAI,CAACC,KAAK,CAACP,WAAW,CAAC,GAAGxC,QAAQ,CAACmB,MAAM,CAAC;QAClEG,aAAa,CAACmB,eAAe,GAAGK,IAAI,CAACC,KAAK,CAACN,eAAe,CAAC,GAAGzC,QAAQ,CAACqB,UAAU,CAAC;QAClFG,OAAO,CAACkB,SAAS,GAAGI,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC,GAAG1C,QAAQ,CAACuB,IAAI,CAAC;QAC1DG,QAAQ,CAACiB,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG3C,QAAQ,CAACyB,KAAK,CAAC;QAC9DG,gBAAgB,CAACgB,kBAAkB,GAAGE,IAAI,CAACC,KAAK,CAACH,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC2B,aAAa,CAAC;QAC9FG,QAAQ,CAACe,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC,GAAG7C,QAAQ,CAAC6B,KAAK,CAAC;MAChE,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C;QACApC,WAAW,CAACZ,QAAQ,CAACW,QAAQ,CAAC;QAC9BG,UAAU,CAACd,QAAQ,CAACa,OAAO,CAAC;QAC5BG,UAAU,CAAChB,QAAQ,CAACe,OAAO,CAAC;QAC5BG,cAAc,CAAClB,QAAQ,CAACiB,WAAW,CAAC;QACpCG,SAAS,CAACpB,QAAQ,CAACmB,MAAM,CAAC;QAC1BG,aAAa,CAACtB,QAAQ,CAACqB,UAAU,CAAC;QAClCG,OAAO,CAACxB,QAAQ,CAACuB,IAAI,CAAC;QACtBG,QAAQ,CAAC1B,QAAQ,CAACyB,KAAK,CAAC;QACxBG,gBAAgB,CAAC5B,QAAQ,CAAC2B,aAAa,CAAC;QACxCG,QAAQ,CAAC9B,QAAQ,CAAC6B,KAAK,CAAC;MAC1B,CAAC,SAAS;QACRG,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,OAAO,EAAE;MACZI,YAAY,CAACe,OAAO,CAAC,cAAc,EAAEJ,IAAI,CAACK,SAAS,CAACxC,QAAQ,CAAC,CAAC;IAChE;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEoB,OAAO,CAAC,CAAC;EAEvBhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,OAAO,EAAE;MACZI,YAAY,CAACe,OAAO,CAAC,aAAa,EAAEJ,IAAI,CAACK,SAAS,CAACtC,OAAO,CAAC,CAAC;IAC9D;EACF,CAAC,EAAE,CAACA,OAAO,EAAEkB,OAAO,CAAC,CAAC;EAEtBhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,OAAO,EAAE;MACZI,YAAY,CAACe,OAAO,CAAC,aAAa,EAAEJ,IAAI,CAACK,SAAS,CAACpC,OAAO,CAAC,CAAC;IAC9D;EACF,CAAC,EAAE,CAACA,OAAO,EAAEgB,OAAO,CAAC,CAAC;EAEtBhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,OAAO,EAAE;MACZI,YAAY,CAACe,OAAO,CAAC,iBAAiB,EAAEJ,IAAI,CAACK,SAAS,CAAClC,WAAW,CAAC,CAAC;IACtE;EACF,CAAC,EAAE,CAACA,WAAW,EAAEc,OAAO,CAAC,CAAC;EAE1BhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,OAAO,EAAE;MACZI,YAAY,CAACe,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACK,SAAS,CAAChC,MAAM,CAAC,CAAC;IAC5D;EACF,CAAC,EAAE,CAACA,MAAM,EAAEY,OAAO,CAAC,CAAC;EAErBhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,OAAO,EAAE;MACZI,YAAY,CAACe,OAAO,CAAC,gBAAgB,EAAEJ,IAAI,CAACK,SAAS,CAAC9B,UAAU,CAAC,CAAC;IACpE;EACF,CAAC,EAAE,CAACA,UAAU,EAAEU,OAAO,CAAC,CAAC;EAEzBhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,OAAO,EAAE;MACZI,YAAY,CAACe,OAAO,CAAC,UAAU,EAAEJ,IAAI,CAACK,SAAS,CAAC5B,IAAI,CAAC,CAAC;IACxD;EACF,CAAC,EAAE,CAACA,IAAI,EAAEQ,OAAO,CAAC,CAAC;EAEnBhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,OAAO,EAAE;MACZI,YAAY,CAACe,OAAO,CAAC,WAAW,EAAEJ,IAAI,CAACK,SAAS,CAAC1B,KAAK,CAAC,CAAC;IAC1D;EACF,CAAC,EAAE,CAACA,KAAK,EAAEM,OAAO,CAAC,CAAC;EAEpBhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,OAAO,EAAE;MACZI,YAAY,CAACe,OAAO,CAAC,oBAAoB,EAAEJ,IAAI,CAACK,SAAS,CAACxB,aAAa,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAACA,aAAa,EAAEI,OAAO,CAAC,CAAC;EAE5BhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,OAAO,EAAE;MACZI,YAAY,CAACe,OAAO,CAAC,WAAW,EAAEJ,IAAI,CAACK,SAAS,CAACtB,KAAK,CAAC,CAAC;IAC1D;EACF,CAAC,EAAE,CAACA,KAAK,EAAEE,OAAO,CAAC,CAAC;EAEpB,MAAMqB,KAAK,GAAG;IACZzC,QAAQ;IACRC,WAAW;IACXC,OAAO;IACPC,UAAU;IACVC,OAAO;IACPC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,MAAM;IACNC,SAAS;IACTC,UAAU;IACVC,aAAa;IACbC,IAAI;IACJC,OAAO;IACPC,KAAK;IACLC,QAAQ;IACRC,aAAa;IACbC,gBAAgB;IAChBC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC;EAED,oBACE7B,OAAA,CAACC,WAAW,CAACkD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3C,QAAA,EAChCA;EAAQ;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC/C,GAAA,CArJWF,YAAY;AAAAkD,EAAA,GAAZlD,YAAY;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}