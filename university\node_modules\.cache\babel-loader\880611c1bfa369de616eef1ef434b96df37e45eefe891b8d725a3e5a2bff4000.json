{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\nimport { DataProvider } from './context/DataContext';\nimport Layout from './components/common/Layout';\nimport Login from './components/auth/Login';\nimport Dashboard from './components/dashboard/Dashboard';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport MyCourses from './components/courses/MyCourses';\nimport Courses from './components/courses/Courses';\nimport ComingSoon from './components/common/ComingSoon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(DataProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 40\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                index: true,\n                element: /*#__PURE__*/_jsxDEV(Navigate, {\n                  to: \"/dashboard\",\n                  replace: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 22,\n                  columnNumber: 39\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"dashboard\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 27,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 26,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"my-courses\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(MyCourses, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 35,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"courses\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: \"admin\",\n                  children: /*#__PURE__*/_jsxDEV(Courses, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 43,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "DataProvider", "Layout", "<PERSON><PERSON>", "Dashboard", "ProtectedRoute", "MyCourses", "Courses", "ComingSoon", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "to", "replace", "requiredRole", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\nimport { DataProvider } from './context/DataContext';\nimport Layout from './components/common/Layout';\nimport Login from './components/auth/Login';\nimport Dashboard from './components/dashboard/Dashboard';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport MyCourses from './components/courses/MyCourses';\nimport Courses from './components/courses/Courses';\nimport ComingSoon from './components/common/ComingSoon';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <DataProvider>\n        <Router>\n          <div className=\"App\">\n            <Routes>\n              <Route path=\"/login\" element={<Login />} />\n              <Route path=\"/\" element={<Layout />}>\n                <Route index element={<Navigate to=\"/dashboard\" replace />} />\n                <Route\n                  path=\"dashboard\"\n                  element={\n                    <ProtectedRoute>\n                      <Dashboard />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"my-courses\"\n                  element={\n                    <ProtectedRoute>\n                      <MyCourses />\n                    </ProtectedRoute>\n                  }\n                />\n                <Route\n                  path=\"courses\"\n                  element={\n                    <ProtectedRoute requiredRole=\"admin\">\n                      <Courses />\n                    </ProtectedRoute>\n                  }\n                />\n                {/* Add more routes here as we create components */}\n              </Route>\n            </Routes>\n          </div>\n        </Router>\n      </DataProvider>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,SAAS,MAAM,gCAAgC;AACtD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,UAAU,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,YAAY;IAAAY,QAAA,eACXF,OAAA,CAACT,YAAY;MAAAW,QAAA,eACXF,OAAA,CAACd,MAAM;QAAAgB,QAAA,eACLF,OAAA;UAAKG,SAAS,EAAC,KAAK;UAAAD,QAAA,eAClBF,OAAA,CAACb,MAAM;YAAAe,QAAA,gBACLF,OAAA,CAACZ,KAAK;cAACgB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEL,OAAA,CAACP,KAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CT,OAAA,CAACZ,KAAK;cAACgB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEL,OAAA,CAACR,MAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAP,QAAA,gBAClCF,OAAA,CAACZ,KAAK;gBAACsB,KAAK;gBAACL,OAAO,eAAEL,OAAA,CAACX,QAAQ;kBAACsB,EAAE,EAAC,YAAY;kBAACC,OAAO;gBAAA;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DT,OAAA,CAACZ,KAAK;gBACJgB,IAAI,EAAC,WAAW;gBAChBC,OAAO,eACLL,OAAA,CAACL,cAAc;kBAAAO,QAAA,eACbF,OAAA,CAACN,SAAS;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACZ,KAAK;gBACJgB,IAAI,EAAC,YAAY;gBACjBC,OAAO,eACLL,OAAA,CAACL,cAAc;kBAAAO,QAAA,eACbF,OAAA,CAACJ,SAAS;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFT,OAAA,CAACZ,KAAK;gBACJgB,IAAI,EAAC,SAAS;gBACdC,OAAO,eACLL,OAAA,CAACL,cAAc;kBAACkB,YAAY,EAAC,OAAO;kBAAAX,QAAA,eAClCF,OAAA,CAACH,OAAO;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEnB;AAACK,EAAA,GA1CQb,GAAG;AA4CZ,eAAeA,GAAG;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}