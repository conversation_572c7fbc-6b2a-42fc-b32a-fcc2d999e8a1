{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\common\\\\UniversityLogo.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport logoImage from '../logo.webp';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UniversityLogo = ({\n  className = \"h-12 w-12\",\n  customLogo = null,\n  showUpload = false,\n  onLogoChange = null\n}) => {\n  _s();\n  const [uploadedLogo, setUploadedLogo] = useState(customLogo);\n  const handleLogoUpload = event => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        const logoUrl = e.target.result;\n        setUploadedLogo(logoUrl);\n        if (onLogoChange) {\n          onLogoChange(logoUrl);\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Determine which logo to show: uploaded logo, stored logo, or default logo\n  const logoToShow = uploadedLogo || localStorage.getItem('universityLogo') || logoImage;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${className} flex items-center justify-center relative group`,\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: logoToShow,\n      alt: \"NFC IET MULTAN Logo\",\n      className: \"w-full h-full object-contain rounded-xl shadow-lg animate-scale-in hover-glow transition-all duration-300\",\n      onError: e => {\n        // Fallback to default logo if image fails to load\n        e.target.src = logoImage;\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), showUpload && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"cursor-pointer text-white text-xs font-semibold bg-primary-600 hover:bg-primary-700 px-3 py-1 rounded-lg transition-colors duration-200\",\n        children: [\"Change Logo\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleLogoUpload,\n          className: \"hidden\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(UniversityLogo, \"WMYvUteiosfbiHKiyyKrKTKMISk=\");\n_c = UniversityLogo;\nexport default UniversityLogo;\nvar _c;\n$RefreshReg$(_c, \"UniversityLogo\");", "map": {"version": 3, "names": ["React", "useState", "logoImage", "jsxDEV", "_jsxDEV", "UniversityLogo", "className", "customLogo", "showUpload", "onLogoChange", "_s", "uploadedLogo", "setUploadedLogo", "handleLogoUpload", "event", "file", "target", "files", "type", "startsWith", "reader", "FileReader", "onload", "e", "logoUrl", "result", "readAsDataURL", "logoToShow", "localStorage", "getItem", "children", "src", "alt", "onError", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "accept", "onChange", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/common/UniversityLogo.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport logoImage from '../logo.webp';\n\nconst UniversityLogo = ({\n  className = \"h-12 w-12\",\n  customLogo = null,\n  showUpload = false,\n  onLogoChange = null\n}) => {\n  const [uploadedLogo, setUploadedLogo] = useState(customLogo);\n\n  const handleLogoUpload = (event) => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        const logoUrl = e.target.result;\n        setUploadedLogo(logoUrl);\n        if (onLogoChange) {\n          onLogoChange(logoUrl);\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Determine which logo to show: uploaded logo, stored logo, or default logo\n  const logoToShow = uploadedLogo || localStorage.getItem('universityLogo') || logoImage;\n\n  return (\n    <div className={`${className} flex items-center justify-center relative group`}>\n      <img\n        src={logoToShow}\n        alt=\"NFC IET MULTAN Logo\"\n        className=\"w-full h-full object-contain rounded-xl shadow-lg animate-scale-in hover-glow transition-all duration-300\"\n        onError={(e) => {\n          // Fallback to default logo if image fails to load\n          e.target.src = logoImage;\n        }}\n      />\n      {showUpload && (\n        <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl flex items-center justify-center\">\n          <label className=\"cursor-pointer text-white text-xs font-semibold bg-primary-600 hover:bg-primary-700 px-3 py-1 rounded-lg transition-colors duration-200\">\n            Change Logo\n            <input\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleLogoUpload}\n              className=\"hidden\"\n            />\n          </label>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default UniversityLogo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,SAAS,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,cAAc,GAAGA,CAAC;EACtBC,SAAS,GAAG,WAAW;EACvBC,UAAU,GAAG,IAAI;EACjBC,UAAU,GAAG,KAAK;EAClBC,YAAY,GAAG;AACjB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAACM,UAAU,CAAC;EAE5D,MAAMM,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1C,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QACrB,MAAMC,OAAO,GAAGD,CAAC,CAACP,MAAM,CAACS,MAAM;QAC/Bb,eAAe,CAACY,OAAO,CAAC;QACxB,IAAIf,YAAY,EAAE;UAChBA,YAAY,CAACe,OAAO,CAAC;QACvB;MACF,CAAC;MACDJ,MAAM,CAACM,aAAa,CAACX,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMY,UAAU,GAAGhB,YAAY,IAAIiB,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI3B,SAAS;EAEtF,oBACEE,OAAA;IAAKE,SAAS,EAAE,GAAGA,SAAS,kDAAmD;IAAAwB,QAAA,gBAC7E1B,OAAA;MACE2B,GAAG,EAAEJ,UAAW;MAChBK,GAAG,EAAC,qBAAqB;MACzB1B,SAAS,EAAC,2GAA2G;MACrH2B,OAAO,EAAGV,CAAC,IAAK;QACd;QACAA,CAAC,CAACP,MAAM,CAACe,GAAG,GAAG7B,SAAS;MAC1B;IAAE;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACD7B,UAAU,iBACTJ,OAAA;MAAKE,SAAS,EAAC,uJAAuJ;MAAAwB,QAAA,eACpK1B,OAAA;QAAOE,SAAS,EAAC,yIAAyI;QAAAwB,QAAA,GAAC,aAEzJ,eAAA1B,OAAA;UACEc,IAAI,EAAC,MAAM;UACXoB,MAAM,EAAC,SAAS;UAChBC,QAAQ,EAAE1B,gBAAiB;UAC3BP,SAAS,EAAC;QAAQ;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3B,EAAA,CApDIL,cAAc;AAAAmC,EAAA,GAAdnC,cAAc;AAsDpB,eAAeA,cAAc;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}