{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is logged in from localStorage\n    const savedUser = localStorage.getItem('ums_user');\n    if (savedUser) {\n      setUser(JSON.parse(savedUser));\n    }\n    setLoading(false);\n  }, []);\n  const login = userData => {\n    setUser(userData);\n    localStorage.setItem('ums_user', JSON.stringify(userData));\n  };\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('ums_user');\n  };\n  const register = async registrationData => {\n    try {\n      // Get existing users from localStorage\n      const existingUsers = JSON.parse(localStorage.getItem('ums_registered_users') || '[]');\n\n      // Check if email already exists\n      const emailExists = existingUsers.some(user => user.email === registrationData.email);\n      if (emailExists) {\n        throw new Error('Email already registered. Please use a different email.');\n      }\n\n      // Add new user to the list\n      const newUser = {\n        ...registrationData,\n        id: registrationData.id,\n        createdAt: new Date().toISOString()\n      };\n      existingUsers.push(newUser);\n      localStorage.setItem('ums_registered_users', JSON.stringify(existingUsers));\n      return newUser;\n    } catch (error) {\n      throw error;\n    }\n  };\n  const updateUser = updatedData => {\n    const updatedUser = {\n      ...user,\n      ...updatedData\n    };\n    setUser(updatedUser);\n    localStorage.setItem('ums_user', JSON.stringify(updatedUser));\n  };\n  const value = {\n    user,\n    login,\n    logout,\n    register,\n    updateUser,\n    loading,\n    isAuthenticated: !!user,\n    isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin',\n    isFaculty: (user === null || user === void 0 ? void 0 : user.role) === 'faculty',\n    isStudent: (user === null || user === void 0 ? void 0 : user.role) === 'student'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "savedUser", "localStorage", "getItem", "JSON", "parse", "login", "userData", "setItem", "stringify", "logout", "removeItem", "register", "registrationData", "existingUsers", "emailExists", "some", "email", "newUser", "id", "createdAt", "Date", "toISOString", "push", "error", "updateUser", "updatedData", "updatedUser", "value", "isAuthenticated", "isAdmin", "role", "is<PERSON><PERSON>ulty", "isStudent", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is logged in from localStorage\n    const savedUser = localStorage.getItem('ums_user');\n    if (savedUser) {\n      setUser(JSON.parse(savedUser));\n    }\n    setLoading(false);\n  }, []);\n\n  const login = (userData) => {\n    setUser(userData);\n    localStorage.setItem('ums_user', JSON.stringify(userData));\n  };\n\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('ums_user');\n  };\n\n  const register = async (registrationData) => {\n    try {\n      // Get existing users from localStorage\n      const existingUsers = JSON.parse(localStorage.getItem('ums_registered_users') || '[]');\n\n      // Check if email already exists\n      const emailExists = existingUsers.some(user => user.email === registrationData.email);\n      if (emailExists) {\n        throw new Error('Email already registered. Please use a different email.');\n      }\n\n      // Add new user to the list\n      const newUser = {\n        ...registrationData,\n        id: registrationData.id,\n        createdAt: new Date().toISOString()\n      };\n\n      existingUsers.push(newUser);\n      localStorage.setItem('ums_registered_users', JSON.stringify(existingUsers));\n\n      return newUser;\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const updateUser = (updatedData) => {\n    const updatedUser = { ...user, ...updatedData };\n    setUser(updatedUser);\n    localStorage.setItem('ums_user', JSON.stringify(updatedUser));\n  };\n\n  const value = {\n    user,\n    login,\n    logout,\n    register,\n    updateUser,\n    loading,\n    isAuthenticated: !!user,\n    isAdmin: user?.role === 'admin',\n    isFaculty: user?.role === 'faculty',\n    isStudent: user?.role === 'student',\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMe,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAClD,IAAIF,SAAS,EAAE;MACbH,OAAO,CAACM,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC,CAAC;IAChC;IACAD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,KAAK,GAAIC,QAAQ,IAAK;IAC1BT,OAAO,CAACS,QAAQ,CAAC;IACjBL,YAAY,CAACM,OAAO,CAAC,UAAU,EAAEJ,IAAI,CAACK,SAAS,CAACF,QAAQ,CAAC,CAAC;EAC5D,CAAC;EAED,MAAMG,MAAM,GAAGA,CAAA,KAAM;IACnBZ,OAAO,CAAC,IAAI,CAAC;IACbI,YAAY,CAACS,UAAU,CAAC,UAAU,CAAC;EACrC,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOC,gBAAgB,IAAK;IAC3C,IAAI;MACF;MACA,MAAMC,aAAa,GAAGV,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,IAAI,CAAC;;MAEtF;MACA,MAAMY,WAAW,GAAGD,aAAa,CAACE,IAAI,CAACnB,IAAI,IAAIA,IAAI,CAACoB,KAAK,KAAKJ,gBAAgB,CAACI,KAAK,CAAC;MACrF,IAAIF,WAAW,EAAE;QACf,MAAM,IAAItB,KAAK,CAAC,yDAAyD,CAAC;MAC5E;;MAEA;MACA,MAAMyB,OAAO,GAAG;QACd,GAAGL,gBAAgB;QACnBM,EAAE,EAAEN,gBAAgB,CAACM,EAAE;QACvBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAEDR,aAAa,CAACS,IAAI,CAACL,OAAO,CAAC;MAC3BhB,YAAY,CAACM,OAAO,CAAC,sBAAsB,EAAEJ,IAAI,CAACK,SAAS,CAACK,aAAa,CAAC,CAAC;MAE3E,OAAOI,OAAO;IAChB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,WAAW,IAAK;IAClC,MAAMC,WAAW,GAAG;MAAE,GAAG9B,IAAI;MAAE,GAAG6B;IAAY,CAAC;IAC/C5B,OAAO,CAAC6B,WAAW,CAAC;IACpBzB,YAAY,CAACM,OAAO,CAAC,UAAU,EAAEJ,IAAI,CAACK,SAAS,CAACkB,WAAW,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMC,KAAK,GAAG;IACZ/B,IAAI;IACJS,KAAK;IACLI,MAAM;IACNE,QAAQ;IACRa,UAAU;IACV1B,OAAO;IACP8B,eAAe,EAAE,CAAC,CAAChC,IAAI;IACvBiC,OAAO,EAAE,CAAAjC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,IAAI,MAAK,OAAO;IAC/BC,SAAS,EAAE,CAAAnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,IAAI,MAAK,SAAS;IACnCE,SAAS,EAAE,CAAApC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,IAAI,MAAK;EAC5B,CAAC;EAED,oBACE3C,OAAA,CAACC,WAAW,CAAC6C,QAAQ;IAACN,KAAK,EAAEA,KAAM;IAAAjC,QAAA,EAChCA;EAAQ;IAAAwC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC1C,GAAA,CA1EWF,YAAY;AAAA6C,EAAA,GAAZ7C,YAAY;AAAA,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}