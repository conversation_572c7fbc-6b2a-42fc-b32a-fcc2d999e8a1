{"ast": null, "code": "export const mockData = {\n  students: [{\n    id: 'STU001',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+1234567890',\n    address: '123 Main St, City, State',\n    dateOfBirth: '2000-05-15',\n    enrollmentDate: '2022-09-01',\n    semester: 3,\n    department: 'Computer Science',\n    cgpa: 3.75,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'STU002',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+1234567891',\n    address: '456 Oak Ave, City, State',\n    dateOfBirth: '2001-03-22',\n    enrollmentDate: '2022-09-01',\n    semester: 3,\n    department: 'Business Administration',\n    cgpa: 3.92,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'STU003',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'student',\n    phone: '+1234567892',\n    address: '789 Pine St, City, State',\n    dateOfBirth: '1999-11-08',\n    enrollmentDate: '2021-09-01',\n    semester: 5,\n    department: 'Engineering',\n    cgpa: 3.45,\n    status: 'active',\n    profileImage: null\n  }],\n  faculty: [{\n    id: 'FAC001',\n    name: 'Dr. Sarah Wilson',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'faculty',\n    phone: '+1234567893',\n    address: '321 University Blvd, City, State',\n    department: 'Computer Science',\n    designation: 'Professor',\n    qualification: 'Ph.D. in Computer Science',\n    experience: 15,\n    specialization: 'Machine Learning, Data Science',\n    joinDate: '2010-08-15',\n    salary: 85000,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'FAC002',\n    name: 'Dr. Robert Brown',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'faculty',\n    phone: '+1234567894',\n    address: '654 Academic Way, City, State',\n    department: 'Business Administration',\n    designation: 'Associate Professor',\n    qualification: 'Ph.D. in Business Administration',\n    experience: 12,\n    specialization: 'Marketing, Strategic Management',\n    joinDate: '2012-01-10',\n    salary: 75000,\n    status: 'active',\n    profileImage: null\n  }, {\n    id: 'FAC003',\n    name: 'Dr. Emily Davis',\n    email: '<EMAIL>',\n    password: 'password123',\n    role: 'faculty',\n    phone: '+1234567895',\n    address: '987 Scholar Lane, City, State',\n    department: 'Engineering',\n    designation: 'Assistant Professor',\n    qualification: 'Ph.D. in Mechanical Engineering',\n    experience: 8,\n    specialization: 'Thermodynamics, Fluid Mechanics',\n    joinDate: '2016-09-01',\n    salary: 65000,\n    status: 'active',\n    profileImage: null\n  }],\n  courses: [{\n    id: 'CS101',\n    name: 'Introduction to Programming',\n    code: 'CS101',\n    department: 'Computer Science',\n    credits: 3,\n    semester: 1,\n    description: 'Basic programming concepts using Python',\n    facultyId: 'FAC001',\n    schedule: {\n      days: ['Monday', 'Wednesday', 'Friday'],\n      time: '09:00-10:00',\n      room: 'CS-101'\n    },\n    capacity: 50,\n    enrolled: 35,\n    status: 'active'\n  }, {\n    id: 'CS201',\n    name: 'Data Structures',\n    code: 'CS201',\n    department: 'Computer Science',\n    credits: 4,\n    semester: 3,\n    description: 'Advanced data structures and algorithms',\n    facultyId: 'FAC001',\n    schedule: {\n      days: ['Tuesday', 'Thursday'],\n      time: '10:00-12:00',\n      room: 'CS-201'\n    },\n    capacity: 40,\n    enrolled: 28,\n    status: 'active'\n  }, {\n    id: 'BUS101',\n    name: 'Business Fundamentals',\n    code: 'BUS101',\n    department: 'Business Administration',\n    credits: 3,\n    semester: 1,\n    description: 'Introduction to business concepts',\n    facultyId: 'FAC002',\n    schedule: {\n      days: ['Monday', 'Wednesday'],\n      time: '14:00-15:30',\n      room: 'BUS-101'\n    },\n    capacity: 60,\n    enrolled: 45,\n    status: 'active'\n  }, {\n    id: 'ENG101',\n    name: 'Engineering Mathematics',\n    code: 'ENG101',\n    department: 'Engineering',\n    credits: 4,\n    semester: 1,\n    description: 'Mathematical foundations for engineering',\n    facultyId: 'FAC003',\n    schedule: {\n      days: ['Tuesday', 'Thursday', 'Friday'],\n      time: '08:00-09:00',\n      room: 'ENG-101'\n    },\n    capacity: 45,\n    enrolled: 42,\n    status: 'active'\n  }],\n  enrollments: [{\n    id: 'ENR001',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    enrollmentDate: '2022-09-01',\n    status: 'enrolled',\n    grade: null\n  }, {\n    id: 'ENR002',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    enrollmentDate: '2023-01-15',\n    status: 'enrolled',\n    grade: null\n  }, {\n    id: 'ENR003',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    enrollmentDate: '2022-09-01',\n    status: 'enrolled',\n    grade: null\n  }, {\n    id: 'ENR004',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    enrollmentDate: '2021-09-01',\n    status: 'completed',\n    grade: 'B+'\n  }],\n  grades: [\n  // Student 1 (John Doe) - CS Student\n  {\n    id: 'GRD001',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    semester: 1,\n    year: 2022,\n    midterm: 85,\n    final: 88,\n    assignments: 92,\n    quizzes: 90,\n    participation: 95,\n    total: 88.5,\n    grade: 'A-',\n    gpa: 3.7,\n    status: 'completed'\n  }, {\n    id: 'GRD002',\n    studentId: 'STU001',\n    courseId: 'CS201',\n    semester: 3,\n    year: 2023,\n    midterm: 92,\n    final: 89,\n    assignments: 95,\n    quizzes: 88,\n    participation: 92,\n    total: 91.2,\n    grade: 'A',\n    gpa: 4.0,\n    status: 'completed'\n  }, {\n    id: 'GRD003',\n    studentId: 'STU001',\n    courseId: 'ENG101',\n    semester: 2,\n    year: 2023,\n    midterm: 78,\n    final: 82,\n    assignments: 85,\n    quizzes: 80,\n    participation: 88,\n    total: 81.5,\n    grade: 'B+',\n    gpa: 3.3,\n    status: 'completed'\n  },\n  // Student 2 (Jane Smith) - Business Student\n  {\n    id: 'GRD004',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    semester: 1,\n    year: 2022,\n    midterm: 92,\n    final: 95,\n    assignments: 89,\n    quizzes: 94,\n    participation: 96,\n    total: 92.3,\n    grade: 'A',\n    gpa: 4.0,\n    status: 'completed'\n  }, {\n    id: 'GRD005',\n    studentId: 'STU002',\n    courseId: 'CS101',\n    semester: 2,\n    year: 2023,\n    midterm: 88,\n    final: 85,\n    assignments: 90,\n    quizzes: 87,\n    participation: 89,\n    total: 87.8,\n    grade: 'A-',\n    gpa: 3.7,\n    status: 'completed'\n  }, {\n    id: 'GRD006',\n    studentId: 'STU002',\n    courseId: 'ENG101',\n    semester: 3,\n    year: 2023,\n    midterm: 95,\n    final: 93,\n    assignments: 97,\n    quizzes: 92,\n    participation: 98,\n    total: 94.5,\n    grade: 'A',\n    gpa: 4.0,\n    status: 'completed'\n  },\n  // Student 3 (Mike Johnson) - Engineering Student\n  {\n    id: 'GRD007',\n    studentId: 'STU003',\n    courseId: 'ENG101',\n    semester: 1,\n    year: 2021,\n    midterm: 78,\n    final: 82,\n    assignments: 85,\n    quizzes: 75,\n    participation: 80,\n    total: 81.5,\n    grade: 'B+',\n    gpa: 3.3,\n    status: 'completed'\n  }, {\n    id: 'GRD008',\n    studentId: 'STU003',\n    courseId: 'CS101',\n    semester: 2,\n    year: 2022,\n    midterm: 72,\n    final: 75,\n    assignments: 78,\n    quizzes: 70,\n    participation: 85,\n    total: 74.8,\n    grade: 'B',\n    gpa: 3.0,\n    status: 'completed'\n  }, {\n    id: 'GRD009',\n    studentId: 'STU003',\n    courseId: 'CS201',\n    semester: 5,\n    year: 2024,\n    midterm: 85,\n    final: null,\n    assignments: 88,\n    quizzes: 82,\n    participation: 90,\n    total: null,\n    grade: null,\n    gpa: null,\n    status: 'in_progress'\n  }],\n  attendance: [{\n    id: 'ATT001',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-15',\n    status: 'present'\n  }, {\n    id: 'ATT002',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-17',\n    status: 'present'\n  }, {\n    id: 'ATT003',\n    studentId: 'STU001',\n    courseId: 'CS101',\n    date: '2024-01-19',\n    status: 'absent'\n  }, {\n    id: 'ATT004',\n    studentId: 'STU002',\n    courseId: 'BUS101',\n    date: '2024-01-15',\n    status: 'present'\n  }],\n  fees: [{\n    id: 'FEE001',\n    studentId: 'STU001',\n    semester: 3,\n    year: 2024,\n    tuitionFee: 5000,\n    libraryFee: 200,\n    labFee: 300,\n    otherFees: 100,\n    totalAmount: 5600,\n    paidAmount: 5600,\n    dueAmount: 0,\n    dueDate: '2024-01-31',\n    paymentDate: '2024-01-15',\n    status: 'paid',\n    paymentMethod: 'bank_transfer'\n  }, {\n    id: 'FEE002',\n    studentId: 'STU002',\n    semester: 3,\n    year: 2024,\n    tuitionFee: 4800,\n    libraryFee: 200,\n    labFee: 250,\n    otherFees: 100,\n    totalAmount: 5350,\n    paidAmount: 2000,\n    dueAmount: 3350,\n    dueDate: '2024-01-31',\n    paymentDate: null,\n    status: 'partial',\n    paymentMethod: null\n  }],\n  books: [{\n    id: 'BOOK001',\n    title: 'Introduction to Algorithms',\n    author: 'Thomas H. Cormen',\n    isbn: '978-**********',\n    category: 'Computer Science',\n    publisher: 'MIT Press',\n    edition: '3rd',\n    totalCopies: 10,\n    availableCopies: 7,\n    location: 'CS Section - Shelf A1',\n    status: 'available'\n  }, {\n    id: 'BOOK002',\n    title: 'Clean Code',\n    author: 'Robert C. Martin',\n    isbn: '978-**********',\n    category: 'Computer Science',\n    publisher: 'Prentice Hall',\n    edition: '1st',\n    totalCopies: 8,\n    availableCopies: 5,\n    location: 'CS Section - Shelf A2',\n    status: 'available'\n  }, {\n    id: 'BOOK003',\n    title: 'Marketing Management',\n    author: 'Philip Kotler',\n    isbn: '978-0134236933',\n    category: 'Business',\n    publisher: 'Pearson',\n    edition: '15th',\n    totalCopies: 12,\n    availableCopies: 9,\n    location: 'Business Section - Shelf B1',\n    status: 'available'\n  }],\n  borrowedBooks: [{\n    id: 'BOR001',\n    studentId: 'STU001',\n    bookId: 'BOOK001',\n    borrowDate: '2024-01-10',\n    dueDate: '2024-02-10',\n    returnDate: null,\n    status: 'borrowed',\n    fine: 0\n  }, {\n    id: 'BOR002',\n    studentId: 'STU002',\n    bookId: 'BOOK002',\n    borrowDate: '2024-01-05',\n    dueDate: '2024-02-05',\n    returnDate: null,\n    status: 'borrowed',\n    fine: 0\n  }],\n  exams: [{\n    id: 'EXM001',\n    courseId: 'CS101',\n    name: 'Midterm Examination',\n    type: 'midterm',\n    date: '2024-02-15',\n    time: '09:00-12:00',\n    room: 'Exam Hall 1',\n    duration: 180,\n    totalMarks: 100,\n    status: 'scheduled'\n  }, {\n    id: 'EXM002',\n    courseId: 'CS201',\n    name: 'Final Examination',\n    type: 'final',\n    date: '2024-05-20',\n    time: '14:00-17:00',\n    room: 'Exam Hall 2',\n    duration: 180,\n    totalMarks: 100,\n    status: 'scheduled'\n  }],\n  // Admin user\n  admin: {\n    id: 'ADM001',\n    name: 'Admin User',\n    email: '<EMAIL>',\n    password: 'admin123',\n    role: 'admin',\n    phone: '+1234567896',\n    address: 'University Administration Building',\n    status: 'active'\n  }\n};", "map": {"version": 3, "names": ["mockData", "students", "id", "name", "email", "password", "role", "phone", "address", "dateOfBirth", "enrollmentDate", "semester", "department", "cgpa", "status", "profileImage", "faculty", "designation", "qualification", "experience", "specialization", "joinDate", "salary", "courses", "code", "credits", "description", "facultyId", "schedule", "days", "time", "room", "capacity", "enrolled", "enrollments", "studentId", "courseId", "grade", "grades", "year", "midterm", "final", "assignments", "quizzes", "participation", "total", "gpa", "attendance", "date", "fees", "tuitionFee", "libraryFee", "lab<PERSON>ee", "otherFees", "totalAmount", "paidAmount", "dueAmount", "dueDate", "paymentDate", "paymentMethod", "books", "title", "author", "isbn", "category", "publisher", "edition", "totalCopies", "availableCopies", "location", "borrowedBooks", "bookId", "borrowDate", "returnDate", "fine", "exams", "type", "duration", "totalMarks", "admin"], "sources": ["D:/HAMMAD/React/New folder/university/src/data/mockData.js"], "sourcesContent": ["export const mockData = {\n  students: [\n    {\n      id: 'STU001',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+1234567890',\n      address: '123 Main St, City, State',\n      dateOfBirth: '2000-05-15',\n      enrollmentDate: '2022-09-01',\n      semester: 3,\n      department: 'Computer Science',\n      cgpa: 3.75,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'STU002',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+1234567891',\n      address: '456 Oak Ave, City, State',\n      dateOfBirth: '2001-03-22',\n      enrollmentDate: '2022-09-01',\n      semester: 3,\n      department: 'Business Administration',\n      cgpa: 3.92,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'STU003',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'student',\n      phone: '+1234567892',\n      address: '789 Pine St, City, State',\n      dateOfBirth: '1999-11-08',\n      enrollmentDate: '2021-09-01',\n      semester: 5,\n      department: 'Engineering',\n      cgpa: 3.45,\n      status: 'active',\n      profileImage: null,\n    },\n  ],\n\n  faculty: [\n    {\n      id: 'FAC001',\n      name: 'Dr. Sarah Wilson',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'faculty',\n      phone: '+1234567893',\n      address: '321 University Blvd, City, State',\n      department: 'Computer Science',\n      designation: 'Professor',\n      qualification: 'Ph.D. in Computer Science',\n      experience: 15,\n      specialization: 'Machine Learning, Data Science',\n      joinDate: '2010-08-15',\n      salary: 85000,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'FAC002',\n      name: 'Dr. Robert Brown',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'faculty',\n      phone: '+1234567894',\n      address: '654 Academic Way, City, State',\n      department: 'Business Administration',\n      designation: 'Associate Professor',\n      qualification: 'Ph.D. in Business Administration',\n      experience: 12,\n      specialization: 'Marketing, Strategic Management',\n      joinDate: '2012-01-10',\n      salary: 75000,\n      status: 'active',\n      profileImage: null,\n    },\n    {\n      id: 'FAC003',\n      name: 'Dr. Emily Davis',\n      email: '<EMAIL>',\n      password: 'password123',\n      role: 'faculty',\n      phone: '+1234567895',\n      address: '987 Scholar Lane, City, State',\n      department: 'Engineering',\n      designation: 'Assistant Professor',\n      qualification: 'Ph.D. in Mechanical Engineering',\n      experience: 8,\n      specialization: 'Thermodynamics, Fluid Mechanics',\n      joinDate: '2016-09-01',\n      salary: 65000,\n      status: 'active',\n      profileImage: null,\n    },\n  ],\n\n  courses: [\n    {\n      id: 'CS101',\n      name: 'Introduction to Programming',\n      code: 'CS101',\n      department: 'Computer Science',\n      credits: 3,\n      semester: 1,\n      description: 'Basic programming concepts using Python',\n      facultyId: 'FAC001',\n      schedule: {\n        days: ['Monday', 'Wednesday', 'Friday'],\n        time: '09:00-10:00',\n        room: 'CS-101',\n      },\n      capacity: 50,\n      enrolled: 35,\n      status: 'active',\n    },\n    {\n      id: 'CS201',\n      name: 'Data Structures',\n      code: 'CS201',\n      department: 'Computer Science',\n      credits: 4,\n      semester: 3,\n      description: 'Advanced data structures and algorithms',\n      facultyId: 'FAC001',\n      schedule: {\n        days: ['Tuesday', 'Thursday'],\n        time: '10:00-12:00',\n        room: 'CS-201',\n      },\n      capacity: 40,\n      enrolled: 28,\n      status: 'active',\n    },\n    {\n      id: 'BUS101',\n      name: 'Business Fundamentals',\n      code: 'BUS101',\n      department: 'Business Administration',\n      credits: 3,\n      semester: 1,\n      description: 'Introduction to business concepts',\n      facultyId: 'FAC002',\n      schedule: {\n        days: ['Monday', 'Wednesday'],\n        time: '14:00-15:30',\n        room: 'BUS-101',\n      },\n      capacity: 60,\n      enrolled: 45,\n      status: 'active',\n    },\n    {\n      id: 'ENG101',\n      name: 'Engineering Mathematics',\n      code: 'ENG101',\n      department: 'Engineering',\n      credits: 4,\n      semester: 1,\n      description: 'Mathematical foundations for engineering',\n      facultyId: 'FAC003',\n      schedule: {\n        days: ['Tuesday', 'Thursday', 'Friday'],\n        time: '08:00-09:00',\n        room: 'ENG-101',\n      },\n      capacity: 45,\n      enrolled: 42,\n      status: 'active',\n    },\n  ],\n\n  enrollments: [\n    {\n      id: 'ENR001',\n      studentId: 'STU001',\n      courseId: 'CS101',\n      enrollmentDate: '2022-09-01',\n      status: 'enrolled',\n      grade: null,\n    },\n    {\n      id: 'ENR002',\n      studentId: 'STU001',\n      courseId: 'CS201',\n      enrollmentDate: '2023-01-15',\n      status: 'enrolled',\n      grade: null,\n    },\n    {\n      id: 'ENR003',\n      studentId: 'STU002',\n      courseId: 'BUS101',\n      enrollmentDate: '2022-09-01',\n      status: 'enrolled',\n      grade: null,\n    },\n    {\n      id: 'ENR004',\n      studentId: 'STU003',\n      courseId: 'ENG101',\n      enrollmentDate: '2021-09-01',\n      status: 'completed',\n      grade: 'B+',\n    },\n  ],\n\n  grades: [\n    // Student 1 (John Doe) - CS Student\n    {\n      id: 'GRD001',\n      studentId: 'STU001',\n      courseId: 'CS101',\n      semester: 1,\n      year: 2022,\n      midterm: 85,\n      final: 88,\n      assignments: 92,\n      quizzes: 90,\n      participation: 95,\n      total: 88.5,\n      grade: 'A-',\n      gpa: 3.7,\n      status: 'completed',\n    },\n    {\n      id: 'GRD002',\n      studentId: 'STU001',\n      courseId: 'CS201',\n      semester: 3,\n      year: 2023,\n      midterm: 92,\n      final: 89,\n      assignments: 95,\n      quizzes: 88,\n      participation: 92,\n      total: 91.2,\n      grade: 'A',\n      gpa: 4.0,\n      status: 'completed',\n    },\n    {\n      id: 'GRD003',\n      studentId: 'STU001',\n      courseId: 'ENG101',\n      semester: 2,\n      year: 2023,\n      midterm: 78,\n      final: 82,\n      assignments: 85,\n      quizzes: 80,\n      participation: 88,\n      total: 81.5,\n      grade: 'B+',\n      gpa: 3.3,\n      status: 'completed',\n    },\n\n    // Student 2 (Jane Smith) - Business Student\n    {\n      id: 'GRD004',\n      studentId: 'STU002',\n      courseId: 'BUS101',\n      semester: 1,\n      year: 2022,\n      midterm: 92,\n      final: 95,\n      assignments: 89,\n      quizzes: 94,\n      participation: 96,\n      total: 92.3,\n      grade: 'A',\n      gpa: 4.0,\n      status: 'completed',\n    },\n    {\n      id: 'GRD005',\n      studentId: 'STU002',\n      courseId: 'CS101',\n      semester: 2,\n      year: 2023,\n      midterm: 88,\n      final: 85,\n      assignments: 90,\n      quizzes: 87,\n      participation: 89,\n      total: 87.8,\n      grade: 'A-',\n      gpa: 3.7,\n      status: 'completed',\n    },\n    {\n      id: 'GRD006',\n      studentId: 'STU002',\n      courseId: 'ENG101',\n      semester: 3,\n      year: 2023,\n      midterm: 95,\n      final: 93,\n      assignments: 97,\n      quizzes: 92,\n      participation: 98,\n      total: 94.5,\n      grade: 'A',\n      gpa: 4.0,\n      status: 'completed',\n    },\n\n    // Student 3 (Mike Johnson) - Engineering Student\n    {\n      id: 'GRD007',\n      studentId: 'STU003',\n      courseId: 'ENG101',\n      semester: 1,\n      year: 2021,\n      midterm: 78,\n      final: 82,\n      assignments: 85,\n      quizzes: 75,\n      participation: 80,\n      total: 81.5,\n      grade: 'B+',\n      gpa: 3.3,\n      status: 'completed',\n    },\n    {\n      id: 'GRD008',\n      studentId: 'STU003',\n      courseId: 'CS101',\n      semester: 2,\n      year: 2022,\n      midterm: 72,\n      final: 75,\n      assignments: 78,\n      quizzes: 70,\n      participation: 85,\n      total: 74.8,\n      grade: 'B',\n      gpa: 3.0,\n      status: 'completed',\n    },\n    {\n      id: 'GRD009',\n      studentId: 'STU003',\n      courseId: 'CS201',\n      semester: 5,\n      year: 2024,\n      midterm: 85,\n      final: null,\n      assignments: 88,\n      quizzes: 82,\n      participation: 90,\n      total: null,\n      grade: null,\n      gpa: null,\n      status: 'in_progress',\n    },\n  ],\n\n  attendance: [\n    {\n      id: 'ATT001',\n      studentId: 'STU001',\n      courseId: 'CS101',\n      date: '2024-01-15',\n      status: 'present',\n    },\n    {\n      id: 'ATT002',\n      studentId: 'STU001',\n      courseId: 'CS101',\n      date: '2024-01-17',\n      status: 'present',\n    },\n    {\n      id: 'ATT003',\n      studentId: 'STU001',\n      courseId: 'CS101',\n      date: '2024-01-19',\n      status: 'absent',\n    },\n    {\n      id: 'ATT004',\n      studentId: 'STU002',\n      courseId: 'BUS101',\n      date: '2024-01-15',\n      status: 'present',\n    },\n  ],\n\n  fees: [\n    {\n      id: 'FEE001',\n      studentId: 'STU001',\n      semester: 3,\n      year: 2024,\n      tuitionFee: 5000,\n      libraryFee: 200,\n      labFee: 300,\n      otherFees: 100,\n      totalAmount: 5600,\n      paidAmount: 5600,\n      dueAmount: 0,\n      dueDate: '2024-01-31',\n      paymentDate: '2024-01-15',\n      status: 'paid',\n      paymentMethod: 'bank_transfer',\n    },\n    {\n      id: 'FEE002',\n      studentId: 'STU002',\n      semester: 3,\n      year: 2024,\n      tuitionFee: 4800,\n      libraryFee: 200,\n      labFee: 250,\n      otherFees: 100,\n      totalAmount: 5350,\n      paidAmount: 2000,\n      dueAmount: 3350,\n      dueDate: '2024-01-31',\n      paymentDate: null,\n      status: 'partial',\n      paymentMethod: null,\n    },\n  ],\n\n  books: [\n    {\n      id: 'BOOK001',\n      title: 'Introduction to Algorithms',\n      author: 'Thomas H. Cormen',\n      isbn: '978-**********',\n      category: 'Computer Science',\n      publisher: 'MIT Press',\n      edition: '3rd',\n      totalCopies: 10,\n      availableCopies: 7,\n      location: 'CS Section - Shelf A1',\n      status: 'available',\n    },\n    {\n      id: 'BOOK002',\n      title: 'Clean Code',\n      author: 'Robert C. Martin',\n      isbn: '978-**********',\n      category: 'Computer Science',\n      publisher: 'Prentice Hall',\n      edition: '1st',\n      totalCopies: 8,\n      availableCopies: 5,\n      location: 'CS Section - Shelf A2',\n      status: 'available',\n    },\n    {\n      id: 'BOOK003',\n      title: 'Marketing Management',\n      author: 'Philip Kotler',\n      isbn: '978-0134236933',\n      category: 'Business',\n      publisher: 'Pearson',\n      edition: '15th',\n      totalCopies: 12,\n      availableCopies: 9,\n      location: 'Business Section - Shelf B1',\n      status: 'available',\n    },\n  ],\n\n  borrowedBooks: [\n    {\n      id: 'BOR001',\n      studentId: 'STU001',\n      bookId: 'BOOK001',\n      borrowDate: '2024-01-10',\n      dueDate: '2024-02-10',\n      returnDate: null,\n      status: 'borrowed',\n      fine: 0,\n    },\n    {\n      id: 'BOR002',\n      studentId: 'STU002',\n      bookId: 'BOOK002',\n      borrowDate: '2024-01-05',\n      dueDate: '2024-02-05',\n      returnDate: null,\n      status: 'borrowed',\n      fine: 0,\n    },\n  ],\n\n  exams: [\n    {\n      id: 'EXM001',\n      courseId: 'CS101',\n      name: 'Midterm Examination',\n      type: 'midterm',\n      date: '2024-02-15',\n      time: '09:00-12:00',\n      room: 'Exam Hall 1',\n      duration: 180,\n      totalMarks: 100,\n      status: 'scheduled',\n    },\n    {\n      id: 'EXM002',\n      courseId: 'CS201',\n      name: 'Final Examination',\n      type: 'final',\n      date: '2024-05-20',\n      time: '14:00-17:00',\n      room: 'Exam Hall 2',\n      duration: 180,\n      totalMarks: 100,\n      status: 'scheduled',\n    },\n  ],\n\n  // Admin user\n  admin: {\n    id: 'ADM001',\n    name: 'Admin User',\n    email: '<EMAIL>',\n    password: 'admin123',\n    role: 'admin',\n    phone: '+1234567896',\n    address: 'University Administration Building',\n    status: 'active',\n  },\n};\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAG;EACtBC,QAAQ,EAAE,CACR;IACEC,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,yBAAyB;IAChCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,yBAAyB;IACrCC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,6BAA6B;IACpCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,aAAa;IACzBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,CACF;EAEDC,OAAO,EAAE,CACP;IACEd,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,6BAA6B;IACpCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,kCAAkC;IAC3CI,UAAU,EAAE,kBAAkB;IAC9BK,WAAW,EAAE,WAAW;IACxBC,aAAa,EAAE,2BAA2B;IAC1CC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,gCAAgC;IAChDC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,KAAK;IACbR,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,6BAA6B;IACpCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,+BAA+B;IACxCI,UAAU,EAAE,yBAAyB;IACrCK,WAAW,EAAE,qBAAqB;IAClCC,aAAa,EAAE,kCAAkC;IACjDC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,iCAAiC;IACjDC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,KAAK;IACbR,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEb,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,4BAA4B;IACnCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,+BAA+B;IACxCI,UAAU,EAAE,aAAa;IACzBK,WAAW,EAAE,qBAAqB;IAClCC,aAAa,EAAE,iCAAiC;IAChDC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,iCAAiC;IACjDC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,KAAK;IACbR,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE;EAChB,CAAC,CACF;EAEDQ,OAAO,EAAE,CACP;IACErB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,6BAA6B;IACnCqB,IAAI,EAAE,OAAO;IACbZ,UAAU,EAAE,kBAAkB;IAC9Ba,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,yCAAyC;IACtDC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC;MACvCC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,iBAAiB;IACvBqB,IAAI,EAAE,OAAO;IACbZ,UAAU,EAAE,kBAAkB;IAC9Ba,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,yCAAyC;IACtDC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;MAC7BC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,uBAAuB;IAC7BqB,IAAI,EAAE,QAAQ;IACdZ,UAAU,EAAE,yBAAyB;IACrCa,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,mCAAmC;IAChDC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;MAC7BC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,yBAAyB;IAC/BqB,IAAI,EAAE,QAAQ;IACdZ,UAAU,EAAE,aAAa;IACzBa,OAAO,EAAE,CAAC;IACVd,QAAQ,EAAE,CAAC;IACXe,WAAW,EAAE,0CAA0C;IACvDC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;MACvCC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZnB,MAAM,EAAE;EACV,CAAC,CACF;EAEDoB,WAAW,EAAE,CACX;IACEhC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,UAAU;IAClBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClB1B,cAAc,EAAE,YAAY;IAC5BI,MAAM,EAAE,WAAW;IACnBuB,KAAK,EAAE;EACT,CAAC,CACF;EAEDC,MAAM,EAAE;EACN;EACA;IACEpC,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC;EAED;EACA;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC;EAED;EACA;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,GAAG;IACVS,GAAG,EAAE,GAAG;IACRhC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBzB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,IAAI;IACXR,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,IAAI;IACThC,MAAM,EAAE;EACV,CAAC,CACF;EAEDiC,UAAU,EAAE,CACV;IACE7C,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBY,IAAI,EAAE,YAAY;IAClBlC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBY,IAAI,EAAE,YAAY;IAClBlC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBY,IAAI,EAAE,YAAY;IAClBlC,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,QAAQ;IAClBY,IAAI,EAAE,YAAY;IAClBlC,MAAM,EAAE;EACV,CAAC,CACF;EAEDmC,IAAI,EAAE,CACJ;IACE/C,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,YAAY;IACzB5C,MAAM,EAAE,MAAM;IACd6C,aAAa,EAAE;EACjB,CAAC,EACD;IACEzD,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBxB,QAAQ,EAAE,CAAC;IACX4B,IAAI,EAAE,IAAI;IACVW,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,IAAI;IACjB5C,MAAM,EAAE,SAAS;IACjB6C,aAAa,EAAE;EACjB,CAAC,CACF;EAEDC,KAAK,EAAE,CACL;IACE1D,EAAE,EAAE,SAAS;IACb2D,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,kBAAkB;IAC5BC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,uBAAuB;IACjCvD,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb2D,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,kBAAkB;IAC5BC,SAAS,EAAE,eAAe;IAC1BC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,uBAAuB;IACjCvD,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,SAAS;IACb2D,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE,eAAe;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,CAAC;IAClBC,QAAQ,EAAE,6BAA6B;IACvCvD,MAAM,EAAE;EACV,CAAC,CACF;EAEDwD,aAAa,EAAE,CACb;IACEpE,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBoC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBf,OAAO,EAAE,YAAY;IACrBgB,UAAU,EAAE,IAAI;IAChB3D,MAAM,EAAE,UAAU;IAClB4D,IAAI,EAAE;EACR,CAAC,EACD;IACExE,EAAE,EAAE,QAAQ;IACZiC,SAAS,EAAE,QAAQ;IACnBoC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBf,OAAO,EAAE,YAAY;IACrBgB,UAAU,EAAE,IAAI;IAChB3D,MAAM,EAAE,UAAU;IAClB4D,IAAI,EAAE;EACR,CAAC,CACF;EAEDC,KAAK,EAAE,CACL;IACEzE,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,qBAAqB;IAC3ByE,IAAI,EAAE,SAAS;IACf5B,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,aAAa;IACnB8C,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACfhE,MAAM,EAAE;EACV,CAAC,EACD;IACEZ,EAAE,EAAE,QAAQ;IACZkC,QAAQ,EAAE,OAAO;IACjBjC,IAAI,EAAE,mBAAmB;IACzByE,IAAI,EAAE,OAAO;IACb5B,IAAI,EAAE,YAAY;IAClBlB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,aAAa;IACnB8C,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACfhE,MAAM,EAAE;EACV,CAAC,CACF;EAED;EACAiE,KAAK,EAAE;IACL7E,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,sBAAsB;IAC7BC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,oCAAoC;IAC7CM,MAAM,EAAE;EACV;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}