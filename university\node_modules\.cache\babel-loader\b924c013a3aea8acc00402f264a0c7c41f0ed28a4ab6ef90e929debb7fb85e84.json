{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\dashboard\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminDashboard from './AdminDashboard';\nimport FacultyDashboard from './FacultyDashboard';\nimport StudentDashboard from './StudentDashboard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user,\n    isAdmin,\n    isFaculty,\n    isStudent\n  } = useAuth();\n  if (isAdmin) {\n    return /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 12\n    }, this);\n  }\n  if (isFaculty) {\n    return /*#__PURE__*/_jsxDEV(FacultyDashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 12\n    }, this);\n  }\n  if (isStudent) {\n    return /*#__PURE__*/_jsxDEV(StudentDashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center h-64\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-gray-900 mb-2\",\n        children: \"Welcome to UMS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Please contact administrator for access.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"2SJpoqUKdS34X5nLT4rRQ/6xo1I=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useAuth", "AdminDashboard", "FacultyDashboard", "StudentDashboard", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "isAdmin", "is<PERSON><PERSON>ulty", "isStudent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/dashboard/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminDashboard from './AdminDashboard';\nimport FacultyDashboard from './FacultyDashboard';\nimport StudentDashboard from './StudentDashboard';\n\nconst Dashboard = () => {\n  const { user, isAdmin, isFaculty, isStudent } = useAuth();\n\n  if (isAdmin) {\n    return <AdminDashboard />;\n  }\n\n  if (isFaculty) {\n    return <FacultyDashboard />;\n  }\n\n  if (isStudent) {\n    return <StudentDashboard />;\n  }\n\n  return (\n    <div className=\"flex items-center justify-center h-64\">\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Welcome to UMS</h2>\n        <p className=\"text-gray-600\">Please contact administrator for access.</p>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC,OAAO;IAAEC,SAAS;IAAEC;EAAU,CAAC,GAAGX,OAAO,CAAC,CAAC;EAEzD,IAAIS,OAAO,EAAE;IACX,oBAAOJ,OAAA,CAACJ,cAAc;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,IAAIL,SAAS,EAAE;IACb,oBAAOL,OAAA,CAACH,gBAAgB;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7B;EAEA,IAAIJ,SAAS,EAAE;IACb,oBAAON,OAAA,CAACF,gBAAgB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7B;EAEA,oBACEV,OAAA;IAAKW,SAAS,EAAC,uCAAuC;IAAAC,QAAA,eACpDZ,OAAA;MAAKW,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BZ,OAAA;QAAIW,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAc;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzEV,OAAA;QAAGW,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAwC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACR,EAAA,CAvBID,SAAS;EAAA,QACmCN,OAAO;AAAA;AAAAkB,EAAA,GADnDZ,SAAS;AAyBf,eAAeA,SAAS;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}