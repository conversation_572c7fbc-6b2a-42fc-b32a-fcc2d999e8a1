{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\common\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet } from 'react-router-dom';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport { useAuth } from '../../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const {\n    isAuthenticated\n  } = useAuth();\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700\",\n      children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      onClose: () => setSidebarOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        onMenuClick: () => setSidebarOpen(true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-x-hidden overflow-y-auto bg-gradient-to-br from-dark-900/50 via-dark-800/50 to-dark-700/50 p-6 backdrop-blur-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in\",\n          children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"1c2WGl8VJiRyPpb5R2WT5GqVhjU=\", false, function () {\n  return [useAuth];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "Sidebar", "Header", "useAuth", "jsxDEV", "_jsxDEV", "Layout", "_s", "sidebarOpen", "setSidebarOpen", "isAuthenticated", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isOpen", "onClose", "onMenuClick", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/common/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet } from 'react-router-dom';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport { useAuth } from '../../context/AuthContext';\n\nconst Layout = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { isAuthenticated } = useAuth();\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700\">\n        <Outlet />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700\">\n      {/* Sidebar */}\n      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />\n\n      {/* Main content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Header */}\n        <Header onMenuClick={() => setSidebarOpen(true)} />\n\n        {/* Page content */}\n        <main className=\"flex-1 overflow-x-hidden overflow-y-auto bg-gradient-to-br from-dark-900/50 via-dark-800/50 to-dark-700/50 p-6 backdrop-blur-sm\">\n          <div className=\"animate-fade-in\">\n            <Outlet />\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAEW;EAAgB,CAAC,GAAGP,OAAO,CAAC,CAAC;EAErC,IAAI,CAACO,eAAe,EAAE;IACpB,oBACEL,OAAA;MAAKM,SAAS,EAAC,uEAAuE;MAAAC,QAAA,eACpFP,OAAA,CAACL,MAAM;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACEX,OAAA;IAAKM,SAAS,EAAC,wEAAwE;IAAAC,QAAA,gBAErFP,OAAA,CAACJ,OAAO;MAACgB,MAAM,EAAET,WAAY;MAACU,OAAO,EAAEA,CAAA,KAAMT,cAAc,CAAC,KAAK;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtEX,OAAA;MAAKM,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBAEnDP,OAAA,CAACH,MAAM;QAACiB,WAAW,EAAEA,CAAA,KAAMV,cAAc,CAAC,IAAI;MAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGnDX,OAAA;QAAMM,SAAS,EAAC,iIAAiI;QAAAC,QAAA,eAC/IP,OAAA;UAAKM,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BP,OAAA,CAACL,MAAM;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACT,EAAA,CA/BID,MAAM;EAAA,QAEkBH,OAAO;AAAA;AAAAiB,EAAA,GAF/Bd,MAAM;AAiCZ,eAAeA,MAAM;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}