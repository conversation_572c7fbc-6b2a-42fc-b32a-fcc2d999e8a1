{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Lock } from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport { useData } from '../../context/DataContext';\nimport { validateEmail } from '../../utils/helpers';\nimport UniversityLogo from '../common/UniversityLogo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    role: 'student'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const {\n    students,\n    faculty\n  } = useData();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    setLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      let user = null;\n\n      // Check admin credentials\n      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {\n        user = {\n          id: 'ADM001',\n          name: 'Admin User',\n          email: '<EMAIL>',\n          role: 'admin',\n          phone: '+1234567896',\n          address: 'University Administration Building',\n          status: 'active'\n        };\n      } else {\n        // Check student/faculty credentials from mock data\n        const allUsers = [...students, ...faculty];\n        user = allUsers.find(u => u.email === formData.email && u.password === formData.password && u.role === formData.role);\n\n        // If not found in mock data, check registered users\n        if (!user) {\n          const registeredUsers = JSON.parse(localStorage.getItem('ums_registered_users') || '[]');\n          user = registeredUsers.find(u => u.email === formData.email && u.password === formData.password && u.role === formData.role);\n        }\n      }\n      if (user) {\n        login(user);\n        navigate('/dashboard');\n      } else {\n        setErrors({\n          submit: 'Invalid email, password, or role'\n        });\n      }\n    } catch (error) {\n      setErrors({\n        submit: 'Login failed. Please try again.'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-20 left-20 text-primary-300/20 animate-float\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-16 h-16\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-32 right-32 text-primary-400/15 animate-float\",\n        style: {\n          animationDelay: '1s'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-20 h-20\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-32 left-32 text-primary-300/15 animate-float\",\n        style: {\n          animationDelay: '2s'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-18 h-18\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H18V1h-2v1H8V1H6v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2zm0 16h-15v-2h15v2zm0-5h-15V6h15v7z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/2 right-20 text-primary-500/10 animate-float\",\n        style: {\n          animationDelay: '3s'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-24 h-24\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-20 right-40 text-primary-400/12 animate-float\",\n        style: {\n          animationDelay: '4s'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-14 h-14\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-16 left-1/2 transform -translate-x-1/2 text-primary-200/8 font-mono text-sm animate-fade-in-out\",\n        children: \"\\u222B f(x)dx = F(x) + C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-40 left-16 text-primary-300/8 font-mono text-xs animate-fade-in-out\",\n        style: {\n          animationDelay: '2s'\n        },\n        children: \"E = mc\\xB2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-40 right-16 text-primary-400/8 font-mono text-xs animate-fade-in-out\",\n        style: {\n          animationDelay: '3s'\n        },\n        children: \"f(x) = ax\\xB2 + bx + c\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-16 left-1/2 transform -translate-x-1/2 text-primary-300/8 font-mono text-sm animate-fade-in-out\",\n        style: {\n          animationDelay: '1s'\n        },\n        children: \"lim(x\\u2192\\u221E) f(x) = L\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/4 left-1/4 w-32 h-32 border border-primary-400/10 rotate-45 animate-spin-slow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-1/4 right-1/4 w-24 h-24 border border-primary-500/10 rotate-12 animate-spin-reverse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-primary-600/20 rounded-full blur-3xl animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-primary-800/20 rounded-full blur-3xl animate-pulse\",\n        style: {\n          animationDelay: '2s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary-400/5 rounded-full blur-3xl animate-pulse\",\n        style: {\n          animationDelay: '4s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-12 gap-4 h-full\",\n          children: Array.from({\n            length: 144\n          }).map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-primary-300/20 rounded\"\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center animate-fade-in-up\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto mb-4 animate-bounce\",\n          children: /*#__PURE__*/_jsxDEV(UniversityLogo, {\n            className: \"h-20 w-20 mx-auto animate-float hover-scale\",\n            showUpload: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold gradient-text mb-2 animate-slide-in-left hover-glow\",\n          children: \"NFC IET MULTAN\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-300 font-semibold mb-1 animate-slide-in-left\",\n          style: {\n            animationDelay: '0.1s'\n          },\n          children: \"National Fertilizer Corporation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base text-gray-400 font-medium mb-4 animate-slide-in-left\",\n          style: {\n            animationDelay: '0.2s'\n          },\n          children: \"Institute of Engineering & Technology\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-white mb-2 animate-slide-in-right\",\n          style: {\n            animationDelay: '0.3s'\n          },\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 animate-slide-in-right\",\n          style: {\n            animationDelay: '0.4s'\n          },\n          children: \"Sign in to access your dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6 glass-dark p-8 rounded-2xl border border-primary-600/20 shadow-2xl animate-scale-in hover-lift\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            style: {\n              animationDelay: '0.5s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"role\",\n              className: \"block text-sm font-semibold text-gray-200 mb-2\",\n              children: \"Login as\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"role\",\n              name: \"role\",\n              value: formData.role,\n              onChange: handleChange,\n              className: \"modern-input-dark block w-full px-4 py-3 border-2 border-primary-600/30 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"student\",\n                className: \"bg-dark-800 text-white\",\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"faculty\",\n                className: \"bg-dark-800 text-white\",\n                children: \"Faculty\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"admin\",\n                className: \"bg-dark-800 text-white\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            style: {\n              animationDelay: '0.6s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-semibold text-gray-200 mb-2\",\n              children: \"Email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(FiUser, {\n                  className: \"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                autoComplete: \"email\",\n                value: formData.email,\n                onChange: handleChange,\n                className: `modern-input-dark block w-full pl-12 pr-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${errors.email ? 'border-red-400' : 'border-primary-600/30'}`,\n                placeholder: \"Enter your email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\",\n              children: errors.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            style: {\n              animationDelay: '0.7s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-semibold text-gray-200 mb-2\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(FiLock, {\n                  className: \"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"password\",\n                name: \"password\",\n                type: showPassword ? 'text' : 'password',\n                autoComplete: \"current-password\",\n                value: formData.password,\n                onChange: handleChange,\n                className: `modern-input-dark block w-full pl-12 pr-12 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${errors.password ? 'border-red-400' : 'border-primary-600/30'}`,\n                placeholder: \"Enter your password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-primary-400 hover:text-primary-300 transition-colors duration-200 hover-scale\",\n                onClick: () => setShowPassword(!showPassword),\n                children: showPassword ? /*#__PURE__*/_jsxDEV(FiEyeOff, {\n                  className: \"h-5 w-5 animate-bounce\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(FiEye, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\",\n              children: errors.password\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-500/10 border border-red-400/30 rounded-xl p-4 animate-slide-down\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-red-400 font-medium\",\n            children: errors.submit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in-up\",\n          style: {\n            animationDelay: '0.8s'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"btn-primary group relative w-full flex justify-center py-4 px-6 border border-transparent text-sm font-bold rounded-xl text-white focus:outline-none focus-ring disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none transition-all duration-300 hover-lift animate-glow\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), \"Signing in...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this) : 'Sign in'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-300\",\n            children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"font-semibold text-primary-400 hover:text-primary-300 transition-colors duration-200\",\n              children: \"Register here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 glass-dark border border-primary-600/20 rounded-xl p-6 animate-scale-in hover-lift\",\n        style: {\n          animationDelay: '1s'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-bold text-primary-300 mb-3 flex items-center animate-slide-in-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), \"Demo Credentials:\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-300 space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\",\n            style: {\n              animationDelay: '1.1s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-primary-400\",\n              children: \"Admin:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs\",\n              children: \"<EMAIL> / admin123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\",\n            style: {\n              animationDelay: '1.2s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-primary-400\",\n              children: \"Faculty:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs\",\n              children: \"<EMAIL> / password123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\",\n            style: {\n              animationDelay: '1.3s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-primary-400\",\n              children: \"Student:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs\",\n              children: \"<EMAIL> / password123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"Sw1YWCfl0JeiCFcqOSg1RybP8ao=\", false, function () {\n  return [useAuth, useData, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "FiEye", "Fi<PERSON>ye<PERSON>ff", "FiUser", "FiLock", "useAuth", "useData", "validateEmail", "UniversityLogo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "role", "showPassword", "setShowPassword", "errors", "setErrors", "loading", "setLoading", "login", "students", "faculty", "navigate", "handleChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "length", "Object", "keys", "handleSubmit", "preventDefault", "Promise", "resolve", "setTimeout", "user", "id", "phone", "address", "status", "allUsers", "find", "u", "registeredUsers", "JSON", "parse", "localStorage", "getItem", "submit", "error", "className", "children", "fill", "viewBox", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "animationDelay", "Array", "from", "map", "_", "i", "showUpload", "onSubmit", "htmlFor", "onChange", "type", "autoComplete", "placeholder", "onClick", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Lock } from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport { useData } from '../../context/DataContext';\nimport { validateEmail } from '../../utils/helpers';\nimport UniversityLogo from '../common/UniversityLogo';\n\nconst Login = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    role: 'student'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  const { login } = useAuth();\n  const { students, faculty } = useData();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    setLoading(true);\n\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      let user = null;\n\n      // Check admin credentials\n      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {\n        user = {\n          id: 'ADM001',\n          name: 'Admin User',\n          email: '<EMAIL>',\n          role: 'admin',\n          phone: '+1234567896',\n          address: 'University Administration Building',\n          status: 'active',\n        };\n      } else {\n        // Check student/faculty credentials from mock data\n        const allUsers = [...students, ...faculty];\n\n        user = allUsers.find(u =>\n          u.email === formData.email &&\n          u.password === formData.password &&\n          u.role === formData.role\n        );\n\n        // If not found in mock data, check registered users\n        if (!user) {\n          const registeredUsers = JSON.parse(localStorage.getItem('ums_registered_users') || '[]');\n          user = registeredUsers.find(u =>\n            u.email === formData.email &&\n            u.password === formData.password &&\n            u.role === formData.role\n          );\n        }\n      }\n\n      if (user) {\n        login(user);\n        navigate('/dashboard');\n      } else {\n        setErrors({ submit: 'Invalid email, password, or role' });\n      }\n    } catch (error) {\n      setErrors({ submit: 'Login failed. Please try again.' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\n      {/* Academic Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        {/* Floating Academic Icons */}\n        <div className=\"absolute top-20 left-20 text-primary-300/20 animate-float\">\n          <svg className=\"w-16 h-16\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z\"/>\n          </svg>\n        </div>\n\n        <div className=\"absolute top-32 right-32 text-primary-400/15 animate-float\" style={{animationDelay: '1s'}}>\n          <svg className=\"w-20 h-20\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z\"/>\n          </svg>\n        </div>\n\n        <div className=\"absolute bottom-32 left-32 text-primary-300/15 animate-float\" style={{animationDelay: '2s'}}>\n          <svg className=\"w-18 h-18\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H18V1h-2v1H8V1H6v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2zm0 16h-15v-2h15v2zm0-5h-15V6h15v7z\"/>\n          </svg>\n        </div>\n\n        <div className=\"absolute top-1/2 right-20 text-primary-500/10 animate-float\" style={{animationDelay: '3s'}}>\n          <svg className=\"w-24 h-24\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n          </svg>\n        </div>\n\n        <div className=\"absolute bottom-20 right-40 text-primary-400/12 animate-float\" style={{animationDelay: '4s'}}>\n          <svg className=\"w-14 h-14\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z\"/>\n          </svg>\n        </div>\n\n        {/* Mathematical Formulas Background */}\n        <div className=\"absolute top-16 left-1/2 transform -translate-x-1/2 text-primary-200/8 font-mono text-sm animate-fade-in-out\">\n          ∫ f(x)dx = F(x) + C\n        </div>\n\n        <div className=\"absolute bottom-40 left-16 text-primary-300/8 font-mono text-xs animate-fade-in-out\" style={{animationDelay: '2s'}}>\n          E = mc²\n        </div>\n\n        <div className=\"absolute top-40 right-16 text-primary-400/8 font-mono text-xs animate-fade-in-out\" style={{animationDelay: '3s'}}>\n          f(x) = ax² + bx + c\n        </div>\n\n        <div className=\"absolute bottom-16 left-1/2 transform -translate-x-1/2 text-primary-300/8 font-mono text-sm animate-fade-in-out\" style={{animationDelay: '1s'}}>\n          lim(x→∞) f(x) = L\n        </div>\n\n        {/* Geometric Patterns */}\n        <div className=\"absolute top-1/4 left-1/4 w-32 h-32 border border-primary-400/10 rotate-45 animate-spin-slow\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-24 h-24 border border-primary-500/10 rotate-12 animate-spin-reverse\"></div>\n\n        {/* Glowing Orbs */}\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-primary-600/20 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-primary-800/20 rounded-full blur-3xl animate-pulse\" style={{animationDelay: '2s'}}></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary-400/5 rounded-full blur-3xl animate-pulse\" style={{animationDelay: '4s'}}></div>\n\n        {/* Academic Grid Pattern */}\n        <div className=\"absolute inset-0 opacity-5\">\n          <div className=\"grid grid-cols-12 gap-4 h-full\">\n            {Array.from({length: 144}).map((_, i) => (\n              <div key={i} className=\"border border-primary-300/20 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-md w-full space-y-8 relative z-10\">\n        <div className=\"text-center animate-fade-in-up\">\n          {/* University Logo */}\n          <div className=\"mx-auto mb-4 animate-bounce\">\n            <UniversityLogo\n              className=\"h-20 w-20 mx-auto animate-float hover-scale\"\n              showUpload={false}\n            />\n          </div>\n\n          {/* University Name */}\n          <h1 className=\"text-3xl font-bold gradient-text mb-2 animate-slide-in-left hover-glow\">\n            NFC IET MULTAN\n          </h1>\n          <p className=\"text-lg text-gray-300 font-semibold mb-1 animate-slide-in-left\" style={{animationDelay: '0.1s'}}>\n            National Fertilizer Corporation\n          </p>\n          <p className=\"text-base text-gray-400 font-medium mb-4 animate-slide-in-left\" style={{animationDelay: '0.2s'}}>\n            Institute of Engineering & Technology\n          </p>\n\n          {/* Welcome Message */}\n          <h2 className=\"text-2xl font-bold text-white mb-2 animate-slide-in-right\" style={{animationDelay: '0.3s'}}>\n            Welcome Back\n          </h2>\n          <p className=\"text-sm text-gray-400 animate-slide-in-right\" style={{animationDelay: '0.4s'}}>\n            Sign in to access your dashboard\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6 glass-dark p-8 rounded-2xl border border-primary-600/20 shadow-2xl animate-scale-in hover-lift\" onSubmit={handleSubmit}>\n          <div className=\"space-y-6\">\n            {/* Role Selection */}\n            <div className=\"animate-fade-in-up\" style={{animationDelay: '0.5s'}}>\n              <label htmlFor=\"role\" className=\"block text-sm font-semibold text-gray-200 mb-2\">\n                Login as\n              </label>\n              <select\n                id=\"role\"\n                name=\"role\"\n                value={formData.role}\n                onChange={handleChange}\n                className=\"modern-input-dark block w-full px-4 py-3 border-2 border-primary-600/30 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300\"\n              >\n                <option value=\"student\" className=\"bg-dark-800 text-white\">Student</option>\n                <option value=\"faculty\" className=\"bg-dark-800 text-white\">Faculty</option>\n                <option value=\"admin\" className=\"bg-dark-800 text-white\">Admin</option>\n              </select>\n            </div>\n\n            {/* Email */}\n            <div className=\"animate-fade-in-up\" style={{animationDelay: '0.6s'}}>\n              <label htmlFor=\"email\" className=\"block text-sm font-semibold text-gray-200 mb-2\">\n                Email address\n              </label>\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <FiUser className=\"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\" />\n                </div>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  className={`modern-input-dark block w-full pl-12 pr-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${\n                    errors.email ? 'border-red-400' : 'border-primary-600/30'\n                  }`}\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n              {errors.email && (\n                <p className=\"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\">{errors.email}</p>\n              )}\n            </div>\n\n            {/* Password */}\n            <div className=\"animate-fade-in-up\" style={{animationDelay: '0.7s'}}>\n              <label htmlFor=\"password\" className=\"block text-sm font-semibold text-gray-200 mb-2\">\n                Password\n              </label>\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <FiLock className=\"h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200\" />\n                </div>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  className={`modern-input-dark block w-full pl-12 pr-12 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${\n                    errors.password ? 'border-red-400' : 'border-primary-600/30'\n                  }`}\n                  placeholder=\"Enter your password\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-4 flex items-center text-primary-400 hover:text-primary-300 transition-colors duration-200 hover-scale\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <FiEyeOff className=\"h-5 w-5 animate-bounce\" />\n                  ) : (\n                    <FiEye className=\"h-5 w-5\" />\n                  )}\n                </button>\n              </div>\n              {errors.password && (\n                <p className=\"mt-2 text-sm text-red-400 font-medium animate-slide-in-left\">{errors.password}</p>\n              )}\n            </div>\n          </div>\n\n          {errors.submit && (\n            <div className=\"bg-red-500/10 border border-red-400/30 rounded-xl p-4 animate-slide-down\">\n              <p className=\"text-sm text-red-400 font-medium\">{errors.submit}</p>\n            </div>\n          )}\n\n          <div className=\"animate-fade-in-up\" style={{animationDelay: '0.8s'}}>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"btn-primary group relative w-full flex justify-center py-4 px-6 border border-transparent text-sm font-bold rounded-xl text-white focus:outline-none focus-ring disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none transition-all duration-300 hover-lift animate-glow\"\n            >\n              {loading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Signing in...\n                </div>\n              ) : (\n                'Sign in'\n              )}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-300\">\n              Don't have an account?{' '}\n              <Link to=\"/register\" className=\"font-semibold text-primary-400 hover:text-primary-300 transition-colors duration-200\">\n                Register here\n              </Link>\n            </p>\n          </div>\n        </form>\n\n        {/* Demo credentials */}\n        <div className=\"mt-8 glass-dark border border-primary-600/20 rounded-xl p-6 animate-scale-in hover-lift\" style={{animationDelay: '1s'}}>\n          <h3 className=\"text-sm font-bold text-primary-300 mb-3 flex items-center animate-slide-in-left\">\n            <span className=\"w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse\"></span>\n            Demo Credentials:\n          </h3>\n          <div className=\"text-sm text-gray-300 space-y-2\">\n            <div className=\"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\" style={{animationDelay: '1.1s'}}>\n              <span className=\"font-semibold text-primary-400\">Admin:</span>\n              <span className=\"text-xs\"><EMAIL> / admin123</span>\n            </div>\n            <div className=\"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\" style={{animationDelay: '1.2s'}}>\n              <span className=\"font-semibold text-primary-400\">Faculty:</span>\n              <span className=\"text-xs\"><EMAIL> / password123</span>\n            </div>\n            <div className=\"flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left\" style={{animationDelay: '1.3s'}}>\n              <span className=\"font-semibold text-primary-400\">Student:</span>\n              <span className=\"text-xs\"><EMAIL> / password123</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AAChE,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,cAAc,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAE0B;EAAM,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC3B,MAAM;IAAEoB,QAAQ;IAAEC;EAAQ,CAAC,GAAGpB,OAAO,CAAC,CAAC;EACvC,MAAMqB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAE9B,MAAM4B,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClB,WAAW,CAACmB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIX,MAAM,CAACU,IAAI,CAAC,EAAE;MAChBT,SAAS,CAACY,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACtB,QAAQ,CAACE,KAAK,EAAE;MACnBoB,SAAS,CAACpB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAACR,aAAa,CAACM,QAAQ,CAACE,KAAK,CAAC,EAAE;MACzCoB,SAAS,CAACpB,KAAK,GAAG,4BAA4B;IAChD;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBmB,SAAS,CAACnB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIH,QAAQ,CAACG,QAAQ,CAACoB,MAAM,GAAG,CAAC,EAAE;MACvCD,SAAS,CAACnB,QAAQ,GAAG,wCAAwC;IAC/D;IAEAK,SAAS,CAACc,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACC,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOV,CAAC,IAAK;IAChCA,CAAC,CAACW,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;IAErBX,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM,IAAIkB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,IAAIE,IAAI,GAAG,IAAI;;MAEf;MACA,IAAI/B,QAAQ,CAACE,KAAK,KAAK,sBAAsB,IAAIF,QAAQ,CAACG,QAAQ,KAAK,UAAU,EAAE;QACjF4B,IAAI,GAAG;UACLC,EAAE,EAAE,QAAQ;UACZf,IAAI,EAAE,YAAY;UAClBf,KAAK,EAAE,sBAAsB;UAC7BE,IAAI,EAAE,OAAO;UACb6B,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE,oCAAoC;UAC7CC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMC,QAAQ,GAAG,CAAC,GAAGxB,QAAQ,EAAE,GAAGC,OAAO,CAAC;QAE1CkB,IAAI,GAAGK,QAAQ,CAACC,IAAI,CAACC,CAAC,IACpBA,CAAC,CAACpC,KAAK,KAAKF,QAAQ,CAACE,KAAK,IAC1BoC,CAAC,CAACnC,QAAQ,KAAKH,QAAQ,CAACG,QAAQ,IAChCmC,CAAC,CAAClC,IAAI,KAAKJ,QAAQ,CAACI,IACtB,CAAC;;QAED;QACA,IAAI,CAAC2B,IAAI,EAAE;UACT,MAAMQ,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,IAAI,CAAC;UACxFZ,IAAI,GAAGQ,eAAe,CAACF,IAAI,CAACC,CAAC,IAC3BA,CAAC,CAACpC,KAAK,KAAKF,QAAQ,CAACE,KAAK,IAC1BoC,CAAC,CAACnC,QAAQ,KAAKH,QAAQ,CAACG,QAAQ,IAChCmC,CAAC,CAAClC,IAAI,KAAKJ,QAAQ,CAACI,IACtB,CAAC;QACH;MACF;MAEA,IAAI2B,IAAI,EAAE;QACRpB,KAAK,CAACoB,IAAI,CAAC;QACXjB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,MAAM;QACLN,SAAS,CAAC;UAAEoC,MAAM,EAAE;QAAmC,CAAC,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdrC,SAAS,CAAC;QAAEoC,MAAM,EAAE;MAAkC,CAAC,CAAC;IAC1D,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEb,OAAA;IAAKiD,SAAS,EAAC,4JAA4J;IAAAC,QAAA,gBAEzKlD,OAAA;MAAKiD,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAE/ClD,OAAA;QAAKiD,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxElD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAACE,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAF,QAAA,eAChElD,OAAA;YAAMqD,CAAC,EAAC;UAAiJ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAKiD,SAAS,EAAC,4DAA4D;QAACS,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI,CAAE;QAAAT,QAAA,eACxGlD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAACE,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAF,QAAA,eAChElD,OAAA;YAAMqD,CAAC,EAAC;UAA2H;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAKiD,SAAS,EAAC,8DAA8D;QAACS,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI,CAAE;QAAAT,QAAA,eAC1GlD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAACE,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAF,QAAA,eAChElD,OAAA;YAAMqD,CAAC,EAAC;UAAgN;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAKiD,SAAS,EAAC,6DAA6D;QAACS,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI,CAAE;QAAAT,QAAA,eACzGlD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAACE,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAF,QAAA,eAChElD,OAAA;YAAMqD,CAAC,EAAC;UAA8F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAKiD,SAAS,EAAC,+DAA+D;QAACS,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI,CAAE;QAAAT,QAAA,eAC3GlD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAACE,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAF,QAAA,eAChElD,OAAA;YAAMqD,CAAC,EAAC;UAA6E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAKiD,SAAS,EAAC,8GAA8G;QAAAC,QAAA,EAAC;MAE9H;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAENzD,OAAA;QAAKiD,SAAS,EAAC,qFAAqF;QAACS,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI,CAAE;QAAAT,QAAA,EAAC;MAEpI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAENzD,OAAA;QAAKiD,SAAS,EAAC,mFAAmF;QAACS,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI,CAAE;QAAAT,QAAA,EAAC;MAElI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAENzD,OAAA;QAAKiD,SAAS,EAAC,iHAAiH;QAACS,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI,CAAE;QAAAT,QAAA,EAAC;MAEhK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNzD,OAAA;QAAKiD,SAAS,EAAC;MAA8F;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpHzD,OAAA;QAAKiD,SAAS,EAAC;MAAqG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG3HzD,OAAA;QAAKiD,SAAS,EAAC;MAA4F;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClHzD,OAAA;QAAKiD,SAAS,EAAC,8FAA8F;QAACS,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnJzD,OAAA;QAAKiD,SAAS,EAAC,sIAAsI;QAACS,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG3LzD,OAAA;QAAKiD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzClD,OAAA;UAAKiD,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAC5CU,KAAK,CAACC,IAAI,CAAC;YAACnC,MAAM,EAAE;UAAG,CAAC,CAAC,CAACoC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAClChE,OAAA;YAAaiD,SAAS,EAAC;UAAsC,GAAnDe,CAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAwD,CACpE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzD,OAAA;MAAKiD,SAAS,EAAC,yCAAyC;MAAAC,QAAA,gBACtDlD,OAAA;QAAKiD,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAE7ClD,OAAA;UAAKiD,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1ClD,OAAA,CAACF,cAAc;YACbmD,SAAS,EAAC,6CAA6C;YACvDgB,UAAU,EAAE;UAAM;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNzD,OAAA;UAAIiD,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EAAC;QAEvF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzD,OAAA;UAAGiD,SAAS,EAAC,gEAAgE;UAACS,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAE/G;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJzD,OAAA;UAAGiD,SAAS,EAAC,gEAAgE;UAACS,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAE/G;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGJzD,OAAA;UAAIiD,SAAS,EAAC,2DAA2D;UAACS,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAE3G;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzD,OAAA;UAAGiD,SAAS,EAAC,8CAA8C;UAACS,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAE7F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENzD,OAAA;QAAMiD,SAAS,EAAC,+GAA+G;QAACiB,QAAQ,EAAErC,YAAa;QAAAqB,QAAA,gBACrJlD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBlD,OAAA;YAAKiD,SAAS,EAAC,oBAAoB;YAACS,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAT,QAAA,gBAClElD,OAAA;cAAOmE,OAAO,EAAC,MAAM;cAAClB,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAAC;YAEjF;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cACEmC,EAAE,EAAC,MAAM;cACTf,IAAI,EAAC,MAAM;cACXC,KAAK,EAAElB,QAAQ,CAACI,IAAK;cACrB6D,QAAQ,EAAElD,YAAa;cACvB+B,SAAS,EAAC,iOAAiO;cAAAC,QAAA,gBAE3OlD,OAAA;gBAAQqB,KAAK,EAAC,SAAS;gBAAC4B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3EzD,OAAA;gBAAQqB,KAAK,EAAC,SAAS;gBAAC4B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3EzD,OAAA;gBAAQqB,KAAK,EAAC,OAAO;gBAAC4B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNzD,OAAA;YAAKiD,SAAS,EAAC,oBAAoB;YAACS,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAT,QAAA,gBAClElD,OAAA;cAAOmE,OAAO,EAAC,OAAO;cAAClB,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAAC;YAElF;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cAAKiD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlD,OAAA;gBAAKiD,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnFlD,OAAA,CAACP,MAAM;kBAACwD,SAAS,EAAC;gBAAsF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC,eACNzD,OAAA;gBACEmC,EAAE,EAAC,OAAO;gBACVf,IAAI,EAAC,OAAO;gBACZiD,IAAI,EAAC,OAAO;gBACZC,YAAY,EAAC,OAAO;gBACpBjD,KAAK,EAAElB,QAAQ,CAACE,KAAM;gBACtB+D,QAAQ,EAAElD,YAAa;gBACvB+B,SAAS,EAAE,mNACTvC,MAAM,CAACL,KAAK,GAAG,gBAAgB,GAAG,uBAAuB,EACxD;gBACHkE,WAAW,EAAC;cAAkB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACL/C,MAAM,CAACL,KAAK,iBACXL,OAAA;cAAGiD,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAAExC,MAAM,CAACL;YAAK;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC7F;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzD,OAAA;YAAKiD,SAAS,EAAC,oBAAoB;YAACS,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAT,QAAA,gBAClElD,OAAA;cAAOmE,OAAO,EAAC,UAAU;cAAClB,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAAC;YAErF;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cAAKiD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlD,OAAA;gBAAKiD,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnFlD,OAAA,CAACN,MAAM;kBAACuD,SAAS,EAAC;gBAAsF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC,eACNzD,OAAA;gBACEmC,EAAE,EAAC,UAAU;gBACbf,IAAI,EAAC,UAAU;gBACfiD,IAAI,EAAE7D,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzC8D,YAAY,EAAC,kBAAkB;gBAC/BjD,KAAK,EAAElB,QAAQ,CAACG,QAAS;gBACzB8D,QAAQ,EAAElD,YAAa;gBACvB+B,SAAS,EAAE,oNACTvC,MAAM,CAACJ,QAAQ,GAAG,gBAAgB,GAAG,uBAAuB,EAC3D;gBACHiE,WAAW,EAAC;cAAqB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACFzD,OAAA;gBACEqE,IAAI,EAAC,QAAQ;gBACbpB,SAAS,EAAC,sIAAsI;gBAChJuB,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAA0C,QAAA,EAE7C1C,YAAY,gBACXR,OAAA,CAACR,QAAQ;kBAACyD,SAAS,EAAC;gBAAwB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/CzD,OAAA,CAACT,KAAK;kBAAC0D,SAAS,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC7B;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL/C,MAAM,CAACJ,QAAQ,iBACdN,OAAA;cAAGiD,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAAExC,MAAM,CAACJ;YAAQ;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAChG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL/C,MAAM,CAACqC,MAAM,iBACZ/C,OAAA;UAAKiD,SAAS,EAAC,0EAA0E;UAAAC,QAAA,eACvFlD,OAAA;YAAGiD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAExC,MAAM,CAACqC;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACN,eAEDzD,OAAA;UAAKiD,SAAS,EAAC,oBAAoB;UAACS,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAAAT,QAAA,eAClElD,OAAA;YACEqE,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAE7D,OAAQ;YAClBqC,SAAS,EAAC,6RAA6R;YAAAC,QAAA,EAEtStC,OAAO,gBACNZ,OAAA;cAAKiD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClD,OAAA;gBAAKiD,SAAS,EAAC;cAAgE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,iBAExF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENzD,OAAA;UAAKiD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BlD,OAAA;YAAGiD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,wBACb,EAAC,GAAG,eAC1BlD,OAAA,CAACX,IAAI;cAACqF,EAAE,EAAC,WAAW;cAACzB,SAAS,EAAC,sFAAsF;cAAAC,QAAA,EAAC;YAEtH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPzD,OAAA;QAAKiD,SAAS,EAAC,yFAAyF;QAACS,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI,CAAE;QAAAT,QAAA,gBACrIlD,OAAA;UAAIiD,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAC7FlD,OAAA;YAAMiD,SAAS,EAAC;UAAwD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,qBAElF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzD,OAAA;UAAKiD,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9ClD,OAAA;YAAKiD,SAAS,EAAC,qGAAqG;YAACS,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAT,QAAA,gBACnJlD,OAAA;cAAMiD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DzD,OAAA;cAAMiD,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAA+B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNzD,OAAA;YAAKiD,SAAS,EAAC,qGAAqG;YAACS,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAT,QAAA,gBACnJlD,OAAA;cAAMiD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEzD,OAAA;cAAMiD,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAyC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNzD,OAAA;YAAKiD,SAAS,EAAC,qGAAqG;YAACS,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAT,QAAA,gBACnJlD,OAAA;cAAMiD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEzD,OAAA;cAAMiD,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAA0C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CA9VID,KAAK;EAAA,QAUSN,OAAO,EACKC,OAAO,EACpBN,WAAW;AAAA;AAAAqF,EAAA,GAZxB1E,KAAK;AAgWX,eAAeA,KAAK;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}