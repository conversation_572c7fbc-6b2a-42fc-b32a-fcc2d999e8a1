{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\common\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRole = null\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 12\n    }, this);\n  }\n  if (requiredRole && (user === null || user === void 0 ? void 0 : user.role) !== requiredRole) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-2\",\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"You don't have permission to access this page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"7q0flPEpi0wJibJzdagPdtYRDq8=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requiredRole", "_s", "isAuthenticated", "user", "loading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "role", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/common/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\n\nconst ProtectedRoute = ({ children, requiredRole = null }) => {\n  const { isAuthenticated, user, loading } = useAuth();\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (requiredRole && user?.role !== requiredRole) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Access Denied</h2>\n          <p className=\"text-gray-600\">You don't have permission to access this page.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGT,OAAO,CAAC,CAAC;EAEpD,IAAIS,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKQ,SAAS,EAAC,uCAAuC;MAAAN,QAAA,eACpDF,OAAA;QAAKQ,SAAS,EAAC;MAAmE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,IAAI,CAACP,eAAe,EAAE;IACpB,oBAAOL,OAAA,CAACH,QAAQ;MAACgB,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEA,IAAIT,YAAY,IAAI,CAAAG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,MAAKZ,YAAY,EAAE;IAC/C,oBACEH,OAAA;MAAKQ,SAAS,EAAC,uCAAuC;MAAAN,QAAA,eACpDF,OAAA;QAAKQ,SAAS,EAAC,aAAa;QAAAN,QAAA,gBAC1BF,OAAA;UAAIQ,SAAS,EAAC,uCAAuC;UAAAN,QAAA,EAAC;QAAa;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEZ,OAAA;UAAGQ,SAAS,EAAC,eAAe;UAAAN,QAAA,EAAC;QAA8C;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAOV,QAAQ;AACjB,CAAC;AAACE,EAAA,CA3BIH,cAAc;EAAA,QACyBH,OAAO;AAAA;AAAAkB,EAAA,GAD9Cf,cAAc;AA6BpB,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}