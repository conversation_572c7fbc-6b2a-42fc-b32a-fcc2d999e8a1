{"ast": null, "code": "var _jsxFileName = \"D:\\\\HAMMAD\\\\React\\\\New folder\\\\university\\\\src\\\\components\\\\common\\\\ComingSoon.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { FiClock, FiArrowLeft } from 'react-icons/fi';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComingSoon = ({\n  title,\n  description\n}) => {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-96 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-100 p-4 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(FiClock, {\n          className: \"h-10 w-10 text-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-gray-900 mb-2\",\n        children: title || 'Coming Soon'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-6 max-w-md mx-auto\",\n        children: description || 'This feature is currently under development. We\\'re working hard to bring it to you soon!'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/dashboard'),\n        className: \"inline-flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700\",\n        children: [/*#__PURE__*/_jsxDEV(FiArrowLeft, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Back to Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_s(ComingSoon, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = ComingSoon;\nexport default ComingSoon;\nvar _c;\n$RefreshReg$(_c, \"ComingSoon\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "FiArrowLeft", "useNavigate", "jsxDEV", "_jsxDEV", "ComingSoon", "title", "description", "_s", "navigate", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/HAMMAD/React/New folder/university/src/components/common/ComingSoon.js"], "sourcesContent": ["import React from 'react';\nimport { FiClock, FiArrowLeft } from 'react-icons/fi';\nimport { useNavigate } from 'react-router-dom';\n\nconst ComingSoon = ({ title, description }) => {\n  const navigate = useNavigate();\n\n  return (\n    <div className=\"min-h-96 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"bg-blue-100 p-4 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\">\n          <FiClock className=\"h-10 w-10 text-blue-600\" />\n        </div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n          {title || 'Coming Soon'}\n        </h2>\n        <p className=\"text-gray-600 mb-6 max-w-md mx-auto\">\n          {description || 'This feature is currently under development. We\\'re working hard to bring it to you soon!'}\n        </p>\n        <button\n          onClick={() => navigate('/dashboard')}\n          className=\"inline-flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700\"\n        >\n          <FiArrowLeft className=\"h-4 w-4\" />\n          <span>Back to Dashboard</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ComingSoon;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,WAAW,QAAQ,gBAAgB;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,UAAU,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,oBACEE,OAAA;IAAKM,SAAS,EAAC,2CAA2C;IAAAC,QAAA,eACxDP,OAAA;MAAKM,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BP,OAAA;QAAKM,SAAS,EAAC,sFAAsF;QAAAC,QAAA,eACnGP,OAAA,CAACJ,OAAO;UAACU,SAAS,EAAC;QAAyB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACNX,OAAA;QAAIM,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAClDL,KAAK,IAAI;MAAa;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACLX,OAAA;QAAGM,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAC/CJ,WAAW,IAAI;MAA2F;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G,CAAC,eACJX,OAAA;QACEY,OAAO,EAAEA,CAAA,KAAMP,QAAQ,CAAC,YAAY,CAAE;QACtCC,SAAS,EAAC,wGAAwG;QAAAC,QAAA,gBAElHP,OAAA,CAACH,WAAW;UAACS,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnCX,OAAA;UAAAO,QAAA,EAAM;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACP,EAAA,CAzBIH,UAAU;EAAA,QACGH,WAAW;AAAA;AAAAe,EAAA,GADxBZ,UAAU;AA2BhB,eAAeA,UAAU;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}