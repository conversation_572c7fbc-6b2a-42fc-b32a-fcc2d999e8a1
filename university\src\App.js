import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { DataProvider } from './context/DataContext';
import Layout from './components/common/Layout';
import Login from './components/auth/Login';
import Dashboard from './components/dashboard/Dashboard';
import ProtectedRoute from './components/common/ProtectedRoute';
import MyCourses from './components/courses/MyCourses';
import Courses from './components/courses/Courses';
import ComingSoon from './components/common/ComingSoon';

function App() {
  return (
    <AuthProvider>
      <DataProvider>
        <Router>
          <div className="App">
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/" element={<Layout />}>
                <Route index element={<Navigate to="/dashboard" replace />} />
                <Route
                  path="dashboard"
                  element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="my-courses"
                  element={
                    <ProtectedRoute>
                      <MyCourses />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="courses"
                  element={
                    <ProtectedRoute requiredRole="admin">
                      <Courses />
                    </ProtectedRoute>
                  }
                />

                {/* Admin Routes */}
                <Route
                  path="students"
                  element={
                    <ProtectedRoute requiredRole="admin">
                      <ComingSoon title="Student Management" description="Manage student records, enrollment, and academic information." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="faculty"
                  element={
                    <ProtectedRoute requiredRole="admin">
                      <ComingSoon title="Faculty Management" description="Manage faculty members, assignments, and professional information." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="academic"
                  element={
                    <ProtectedRoute requiredRole="admin">
                      <ComingSoon title="Academic Management" description="Manage academic records, grades, and transcripts." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="fees"
                  element={
                    <ProtectedRoute requiredRole="admin">
                      <ComingSoon title="Fee Management" description="Manage fee structures, payments, and financial records." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="library"
                  element={
                    <ProtectedRoute>
                      <ComingSoon title="Library Management" description="Manage books, borrowing, and library resources." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="exams"
                  element={
                    <ProtectedRoute>
                      <ComingSoon title="Examination System" description="Schedule exams, manage results, and track performance." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="reports"
                  element={
                    <ProtectedRoute requiredRole="admin">
                      <ComingSoon title="Reports & Analytics" description="Generate comprehensive reports and analytics." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="settings"
                  element={
                    <ProtectedRoute requiredRole="admin">
                      <ComingSoon title="System Settings" description="Configure system settings and preferences." />
                    </ProtectedRoute>
                  }
                />

                {/* Faculty Routes */}
                <Route
                  path="my-students"
                  element={
                    <ProtectedRoute requiredRole="faculty">
                      <ComingSoon title="My Students" description="View and manage students in your courses." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="grades"
                  element={
                    <ProtectedRoute requiredRole="faculty">
                      <ComingSoon title="Grade Management" description="Manage student grades and assessments." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="attendance"
                  element={
                    <ProtectedRoute requiredRole="faculty">
                      <ComingSoon title="Attendance Management" description="Track and manage student attendance." />
                    </ProtectedRoute>
                  }
                />

                {/* Student Routes */}
                <Route
                  path="my-grades"
                  element={
                    <ProtectedRoute requiredRole="student">
                      <ComingSoon title="My Grades" description="View your academic performance and grades." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="my-attendance"
                  element={
                    <ProtectedRoute requiredRole="student">
                      <ComingSoon title="My Attendance" description="View your attendance records and statistics." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="my-fees"
                  element={
                    <ProtectedRoute requiredRole="student">
                      <ComingSoon title="My Fees" description="View fee details, payment history, and make payments." />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="my-exams"
                  element={
                    <ProtectedRoute requiredRole="student">
                      <ComingSoon title="My Exams" description="View exam schedules, results, and performance." />
                    </ProtectedRoute>
                  }
                />

                {/* Catch all route */}
                <Route
                  path="*"
                  element={
                    <ComingSoon title="Page Not Found" description="The page you're looking for doesn't exist or is under development." />
                  }
                />
              </Route>
            </Routes>
          </div>
        </Router>
      </DataProvider>
    </AuthProvider>
  );
}

export default App;
