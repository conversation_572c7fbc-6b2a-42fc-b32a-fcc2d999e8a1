import React, { useState } from 'react';
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiSearch,
  FiFilter,
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiCalendar,
  FiBookOpen,
  FiCheck,
  FiX,
  FiEye
} from 'react-icons/fi';
import { useData } from '../../context/DataContext';

const InstructorManagement = () => {
  const { faculty, setFaculty, courses } = useData();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedInstructor, setSelectedInstructor] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    phone: '',
    address: '',
    dateOfBirth: '',
    department: '',
    qualification: '',
    experience: '',
    specialization: '',
    salary: '',
    joiningDate: '',
    status: 'active'
  });

  // Filter instructors based on search and status
  const filteredInstructors = faculty.filter(instructor => {
    const matchesSearch = instructor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         instructor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         instructor.department?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || instructor.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  // Get instructor's courses
  const getInstructorCourses = (instructorId) => {
    return courses.filter(course => course.instructorId === instructorId);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      password: '',
      phone: '',
      address: '',
      dateOfBirth: '',
      department: '',
      qualification: '',
      experience: '',
      specialization: '',
      salary: '',
      joiningDate: '',
      status: 'active'
    });
  };

  // Add new instructor
  const handleAddInstructor = (e) => {
    e.preventDefault();
    
    const newInstructor = {
      id: `FAC${String(faculty.length + 1).padStart(3, '0')}`,
      ...formData,
      role: 'faculty',
      profileImage: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setFaculty([...faculty, newInstructor]);
    setShowAddModal(false);
    resetForm();
  };

  // Edit instructor
  const handleEditInstructor = (instructor) => {
    setSelectedInstructor(instructor);
    setFormData({
      name: instructor.name,
      email: instructor.email,
      password: instructor.password,
      phone: instructor.phone,
      address: instructor.address,
      dateOfBirth: instructor.dateOfBirth,
      department: instructor.department || '',
      qualification: instructor.qualification || '',
      experience: instructor.experience || '',
      specialization: instructor.specialization || '',
      salary: instructor.salary || '',
      joiningDate: instructor.joiningDate || '',
      status: instructor.status
    });
    setShowEditModal(true);
  };

  // Update instructor
  const handleUpdateInstructor = (e) => {
    e.preventDefault();
    
    const updatedInstructor = {
      ...selectedInstructor,
      ...formData,
      updatedAt: new Date().toISOString()
    };

    setFaculty(faculty.map(f => f.id === selectedInstructor.id ? updatedInstructor : f));
    setShowEditModal(false);
    resetForm();
    setSelectedInstructor(null);
  };

  // Delete instructor
  const handleDeleteInstructor = (instructorId) => {
    if (window.confirm('Are you sure you want to delete this instructor?')) {
      setFaculty(faculty.filter(f => f.id !== instructorId));
    }
  };

  // View instructor details
  const handleViewInstructor = (instructor) => {
    setSelectedInstructor(instructor);
    setShowViewModal(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold gradient-text">Instructor Management</h1>
          <p className="text-gray-300 mt-1">Manage faculty members and instructors</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="btn-primary mt-4 sm:mt-0 flex items-center space-x-2"
        >
          <FiPlus className="h-4 w-4" />
          <span>Add Instructor</span>
        </button>
      </div>

      {/* Search and Filter */}
      <div className="modern-card-dark p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search instructors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="modern-input-dark pl-10 w-full"
            />
          </div>
          <div className="relative">
            <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="modern-input-dark pl-10 pr-8"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="on_leave">On Leave</option>
            </select>
          </div>
        </div>
      </div>

      {/* Instructors Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredInstructors.map((instructor) => {
          const instructorCourses = getInstructorCourses(instructor.id);
          
          return (
            <div key={instructor.id} className="modern-card-dark p-6 hover:scale-105 transition-all duration-300">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="h-12 w-12 rounded-full bg-gradient-primary flex items-center justify-center">
                    <span className="text-white font-semibold text-lg">
                      {instructor.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">{instructor.name}</h3>
                    <p className="text-sm text-gray-400">{instructor.department || 'No Department'}</p>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  instructor.status === 'active' ? 'bg-green-500/20 text-green-400' :
                  instructor.status === 'inactive' ? 'bg-red-500/20 text-red-400' :
                  'bg-yellow-500/20 text-yellow-400'
                }`}>
                  {instructor.status}
                </span>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-300">
                  <FiMail className="h-4 w-4 mr-2 text-gray-400" />
                  {instructor.email}
                </div>
                <div className="flex items-center text-sm text-gray-300">
                  <FiPhone className="h-4 w-4 mr-2 text-gray-400" />
                  {instructor.phone}
                </div>
                <div className="flex items-center text-sm text-gray-300">
                  <FiBookOpen className="h-4 w-4 mr-2 text-gray-400" />
                  {instructorCourses.length} Course{instructorCourses.length !== 1 ? 's' : ''}
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => handleViewInstructor(instructor)}
                  className="flex-1 bg-blue-600/20 text-blue-400 hover:bg-blue-600/30 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center justify-center space-x-1"
                >
                  <FiEye className="h-4 w-4" />
                  <span>View</span>
                </button>
                <button
                  onClick={() => handleEditInstructor(instructor)}
                  className="flex-1 bg-yellow-600/20 text-yellow-400 hover:bg-yellow-600/30 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center justify-center space-x-1"
                >
                  <FiEdit2 className="h-4 w-4" />
                  <span>Edit</span>
                </button>
                <button
                  onClick={() => handleDeleteInstructor(instructor.id)}
                  className="bg-red-600/20 text-red-400 hover:bg-red-600/30 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                >
                  <FiTrash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {filteredInstructors.length === 0 && (
        <div className="modern-card-dark p-12 text-center">
          <FiUser className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No instructors found</h3>
          <p className="text-gray-400">Try adjusting your search or filter criteria</p>
        </div>
      )}

      {/* Add Instructor Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-dark-800 rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Add New Instructor</h2>
              <button
                onClick={() => {
                  setShowAddModal(false);
                  resetForm();
                }}
                className="text-gray-400 hover:text-white transition-colors duration-200"
              >
                <FiX className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleAddInstructor} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="modern-input-dark w-full"
                    placeholder="Enter full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="modern-input-dark w-full"
                    placeholder="Enter email address"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Password *
                  </label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    className="modern-input-dark w-full"
                    placeholder="Enter password"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                    placeholder="Enter phone number"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Department
                  </label>
                  <select
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                  >
                    <option value="">Select Department</option>
                    <option value="Computer Science">Computer Science</option>
                    <option value="Software Engineering">Software Engineering</option>
                    <option value="Information Technology">Information Technology</option>
                    <option value="Electrical Engineering">Electrical Engineering</option>
                    <option value="Mechanical Engineering">Mechanical Engineering</option>
                    <option value="Civil Engineering">Civil Engineering</option>
                    <option value="Mathematics">Mathematics</option>
                    <option value="Physics">Physics</option>
                    <option value="Chemistry">Chemistry</option>
                    <option value="English">English</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Qualification
                  </label>
                  <input
                    type="text"
                    name="qualification"
                    value={formData.qualification}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                    placeholder="e.g., PhD in Computer Science"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Experience (Years)
                  </label>
                  <input
                    type="number"
                    name="experience"
                    value={formData.experience}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                    placeholder="Years of experience"
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Specialization
                  </label>
                  <input
                    type="text"
                    name="specialization"
                    value={formData.specialization}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                    placeholder="Area of specialization"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Date of Birth
                  </label>
                  <input
                    type="date"
                    name="dateOfBirth"
                    value={formData.dateOfBirth}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Joining Date
                  </label>
                  <input
                    type="date"
                    name="joiningDate"
                    value={formData.joiningDate}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Salary
                  </label>
                  <input
                    type="number"
                    name="salary"
                    value={formData.salary}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                    placeholder="Monthly salary"
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Status
                  </label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="on_leave">On Leave</option>
                  </select>
                </div>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Address
                </label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  rows="3"
                  className="modern-input-dark w-full"
                  placeholder="Enter complete address"
                />
              </div>

              <div className="flex space-x-4 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddModal(false);
                    resetForm();
                  }}
                  className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-xl font-medium hover:bg-gray-700 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 btn-primary py-3 px-4 rounded-xl font-medium"
                >
                  Add Instructor
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Instructor Modal */}
      {showEditModal && selectedInstructor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-dark-800 rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Edit Instructor</h2>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  resetForm();
                  setSelectedInstructor(null);
                }}
                className="text-gray-400 hover:text-white transition-colors duration-200"
              >
                <FiX className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleUpdateInstructor} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="modern-input-dark w-full"
                    placeholder="Enter full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="modern-input-dark w-full"
                    placeholder="Enter email address"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                    placeholder="Enter phone number"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Department
                  </label>
                  <select
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                  >
                    <option value="">Select Department</option>
                    <option value="Computer Science">Computer Science</option>
                    <option value="Software Engineering">Software Engineering</option>
                    <option value="Information Technology">Information Technology</option>
                    <option value="Electrical Engineering">Electrical Engineering</option>
                    <option value="Mechanical Engineering">Mechanical Engineering</option>
                    <option value="Civil Engineering">Civil Engineering</option>
                    <option value="Mathematics">Mathematics</option>
                    <option value="Physics">Physics</option>
                    <option value="Chemistry">Chemistry</option>
                    <option value="English">English</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Qualification
                  </label>
                  <input
                    type="text"
                    name="qualification"
                    value={formData.qualification}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                    placeholder="e.g., PhD in Computer Science"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Experience (Years)
                  </label>
                  <input
                    type="number"
                    name="experience"
                    value={formData.experience}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                    placeholder="Years of experience"
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Specialization
                  </label>
                  <input
                    type="text"
                    name="specialization"
                    value={formData.specialization}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                    placeholder="Area of specialization"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Salary
                  </label>
                  <input
                    type="number"
                    name="salary"
                    value={formData.salary}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                    placeholder="Monthly salary"
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Status
                  </label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="modern-input-dark w-full"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="on_leave">On Leave</option>
                  </select>
                </div>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Address
                </label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  rows="3"
                  className="modern-input-dark w-full"
                  placeholder="Enter complete address"
                />
              </div>

              <div className="flex space-x-4 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowEditModal(false);
                    resetForm();
                    setSelectedInstructor(null);
                  }}
                  className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-xl font-medium hover:bg-gray-700 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 btn-primary py-3 px-4 rounded-xl font-medium"
                >
                  Update Instructor
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* View Instructor Modal */}
      {showViewModal && selectedInstructor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-dark-800 rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Instructor Details</h2>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setSelectedInstructor(null);
                }}
                className="text-gray-400 hover:text-white transition-colors duration-200"
              >
                <FiX className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Profile Section */}
              <div className="flex items-center space-x-4 p-4 bg-primary-600/10 rounded-xl">
                <div className="h-16 w-16 rounded-full bg-gradient-primary flex items-center justify-center">
                  <span className="text-white font-bold text-2xl">
                    {selectedInstructor.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">{selectedInstructor.name}</h3>
                  <p className="text-gray-300">{selectedInstructor.department || 'No Department'}</p>
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${
                    selectedInstructor.status === 'active' ? 'bg-green-500/20 text-green-400' :
                    selectedInstructor.status === 'inactive' ? 'bg-red-500/20 text-red-400' :
                    'bg-yellow-500/20 text-yellow-400'
                  }`}>
                    {selectedInstructor.status}
                  </span>
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white border-b border-primary-600/20 pb-2">
                    Contact Information
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <FiMail className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-300">{selectedInstructor.email}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <FiPhone className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-300">{selectedInstructor.phone || 'Not provided'}</span>
                    </div>
                    <div className="flex items-start space-x-3">
                      <FiMapPin className="h-4 w-4 text-gray-400 mt-1" />
                      <span className="text-gray-300">{selectedInstructor.address || 'Not provided'}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white border-b border-primary-600/20 pb-2">
                    Professional Details
                  </h4>
                  <div className="space-y-3">
                    <div>
                      <span className="text-gray-400 text-sm">Qualification:</span>
                      <p className="text-gray-300">{selectedInstructor.qualification || 'Not specified'}</p>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Experience:</span>
                      <p className="text-gray-300">{selectedInstructor.experience ? `${selectedInstructor.experience} years` : 'Not specified'}</p>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Specialization:</span>
                      <p className="text-gray-300">{selectedInstructor.specialization || 'Not specified'}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-gray-400 text-sm">Date of Birth:</span>
                  <p className="text-gray-300">{selectedInstructor.dateOfBirth || 'Not provided'}</p>
                </div>
                <div>
                  <span className="text-gray-400 text-sm">Joining Date:</span>
                  <p className="text-gray-300">{selectedInstructor.joiningDate || 'Not provided'}</p>
                </div>
                <div>
                  <span className="text-gray-400 text-sm">Salary:</span>
                  <p className="text-gray-300">{selectedInstructor.salary ? `$${selectedInstructor.salary}` : 'Not specified'}</p>
                </div>
                <div>
                  <span className="text-gray-400 text-sm">Employee ID:</span>
                  <p className="text-gray-300">{selectedInstructor.id}</p>
                </div>
              </div>

              {/* Courses Teaching */}
              <div>
                <h4 className="text-lg font-semibold text-white border-b border-primary-600/20 pb-2 mb-4">
                  Courses Teaching
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {getInstructorCourses(selectedInstructor.id).map((course) => (
                    <div key={course.id} className="bg-primary-600/10 p-3 rounded-lg">
                      <h5 className="font-medium text-white">{course.name}</h5>
                      <p className="text-sm text-gray-400">{course.code}</p>
                      <p className="text-xs text-gray-500">{course.credits} Credits</p>
                    </div>
                  ))}
                  {getInstructorCourses(selectedInstructor.id).length === 0 && (
                    <p className="text-gray-400 col-span-2">No courses assigned</p>
                  )}
                </div>
              </div>

              <div className="flex space-x-4 pt-4">
                <button
                  onClick={() => {
                    setShowViewModal(false);
                    handleEditInstructor(selectedInstructor);
                  }}
                  className="flex-1 btn-primary py-3 px-4 rounded-xl font-medium"
                >
                  Edit Instructor
                </button>
                <button
                  onClick={() => {
                    setShowViewModal(false);
                    setSelectedInstructor(null);
                  }}
                  className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-xl font-medium hover:bg-gray-700 transition-colors duration-200"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstructorManagement;
