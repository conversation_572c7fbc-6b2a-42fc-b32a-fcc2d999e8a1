import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Lock } from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { validateEmail } from '../../utils/helpers';
import UniversityLogo from '../common/UniversityLogo';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    role: 'student'
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const { login } = useAuth();
  const { students, faculty } = useData();
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      let user = null;

      // Check admin credentials
      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {
        user = {
          id: 'ADM001',
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          phone: '+1234567896',
          address: 'University Administration Building',
          status: 'active',
        };
      } else {
        // Check student/faculty credentials
        const allUsers = [...students, ...faculty];
        user = allUsers.find(u => 
          u.email === formData.email && 
          u.password === formData.password &&
          u.role === formData.role
        );
      }

      if (user) {
        login(user);
        navigate('/dashboard');
      } else {
        setErrors({ submit: 'Invalid email, password, or role' });
      }
    } catch (error) {
      setErrors({ submit: 'Login failed. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-600/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-800/20 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-md w-full space-y-8 relative z-10">
        <div className="text-center">
          {/* University Logo */}
          <div className="mx-auto mb-4">
            <UniversityLogo className="h-20 w-20 mx-auto animate-pulse-slow" />
          </div>

          {/* University Name */}
          <h1 className="text-3xl font-bold gradient-text mb-2">
            NFC IET MULTAN
          </h1>
          <p className="text-lg text-gray-300 font-semibold mb-1">
            National Fertilizer Corporation
          </p>
          <p className="text-base text-gray-400 font-medium mb-4">
            Institute of Engineering & Technology
          </p>

          {/* Welcome Message */}
          <h2 className="text-2xl font-bold text-white mb-2">
            Welcome Back
          </h2>
          <p className="text-sm text-gray-400">
            Sign in to access your dashboard
          </p>
        </div>

        <form className="mt-8 space-y-6 glass-dark p-8 rounded-2xl border border-primary-600/20 shadow-2xl animate-slide-up" onSubmit={handleSubmit}>
          <div className="space-y-6">
            {/* Role Selection */}
            <div>
              <label htmlFor="role" className="block text-sm font-semibold text-gray-200 mb-2">
                Login as
              </label>
              <select
                id="role"
                name="role"
                value={formData.role}
                onChange={handleChange}
                className="modern-input block w-full px-4 py-3 border-2 border-primary-600/30 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-dark-800/50 text-white font-medium"
              >
                <option value="student" className="bg-dark-800 text-white">Student</option>
                <option value="faculty" className="bg-dark-800 text-white">Faculty</option>
                <option value="admin" className="bg-dark-800 text-white">Admin</option>
              </select>
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-semibold text-gray-200 mb-2">
                Email address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FiUser className="h-5 w-5 text-primary-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`modern-input block w-full pl-12 pr-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-dark-800/50 text-white font-medium placeholder-gray-400 ${
                    errors.email ? 'border-red-400' : 'border-primary-600/30'
                  }`}
                  placeholder="Enter your email"
                />
              </div>
              {errors.email && (
                <p className="mt-2 text-sm text-red-400 font-medium">{errors.email}</p>
              )}
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="block text-sm font-semibold text-gray-200 mb-2">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FiLock className="h-5 w-5 text-primary-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  value={formData.password}
                  onChange={handleChange}
                  className={`modern-input block w-full pl-12 pr-12 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-dark-800/50 text-white font-medium placeholder-gray-400 ${
                    errors.password ? 'border-red-400' : 'border-primary-600/30'
                  }`}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-4 flex items-center text-primary-400 hover:text-primary-300 transition-colors duration-200"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <FiEyeOff className="h-5 w-5" />
                  ) : (
                    <FiEye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-2 text-sm text-red-400 font-medium">{errors.password}</p>
              )}
            </div>
          </div>

          {errors.submit && (
            <div className="bg-red-500/10 border border-red-400/30 rounded-xl p-4 animate-slide-down">
              <p className="text-sm text-red-400 font-medium">{errors.submit}</p>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary group relative w-full flex justify-center py-4 px-6 border border-transparent text-sm font-bold rounded-xl text-white focus:outline-none focus-ring disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none transition-all duration-300"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Signing in...
                </div>
              ) : (
                'Sign in'
              )}
            </button>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-300">
              Don't have an account?{' '}
              <Link to="/register" className="font-semibold text-primary-400 hover:text-primary-300 transition-colors duration-200">
                Register here
              </Link>
            </p>
          </div>
        </form>

        {/* Demo credentials */}
        <div className="mt-8 glass-dark border border-primary-600/20 rounded-xl p-6 animate-slide-up">
          <h3 className="text-sm font-bold text-primary-300 mb-3 flex items-center">
            <span className="w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse"></span>
            Demo Credentials:
          </h3>
          <div className="text-sm text-gray-300 space-y-2">
            <div className="flex justify-between items-center p-2 bg-primary-600/10 rounded-lg">
              <span className="font-semibold text-primary-400">Admin:</span>
              <span className="text-xs"><EMAIL> / admin123</span>
            </div>
            <div className="flex justify-between items-center p-2 bg-primary-600/10 rounded-lg">
              <span className="font-semibold text-primary-400">Faculty:</span>
              <span className="text-xs"><EMAIL> / password123</span>
            </div>
            <div className="flex justify-between items-center p-2 bg-primary-600/10 rounded-lg">
              <span className="font-semibold text-primary-400">Student:</span>
              <span className="text-xs"><EMAIL> / password123</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
