import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Lock } from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { validateEmail } from '../../utils/helpers';
import UniversityLogo from '../common/UniversityLogo';
import backgroundImage from '../DefaultBackground.jpg';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    role: 'student'
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const { login } = useAuth();
  const { students, faculty, loading: dataLoading } = useData();
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Check if data is still loading
    if (dataLoading) {
      setErrors({ submit: 'Please wait, loading user data...' });
      return;
    }

    setLoading(true);
    setErrors({}); // Clear any previous errors

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Debug: Log all available data
      console.log('=== LOGIN DEBUG INFO ===');
      console.log('Form data:', formData);
      console.log('Students array:', students);
      console.log('Faculty array:', faculty);
      console.log('Data loading status:', dataLoading);

      let user = null;

      // Check admin credentials
      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {
        user = {
          id: 'ADM001',
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          phone: '+1234567896',
          address: 'University Administration Building',
          status: 'active',
        };
      } else {
        // Check student/faculty credentials from mock data
        const allUsers = [...students, ...faculty];

        console.log('All users count:', allUsers.length);
        console.log('Looking for user with:', {
          email: formData.email,
          password: formData.password,
          role: formData.role
        });

        // Find Hammad specifically for debugging
        const hammadUser = students.find(s => s.email === '<EMAIL>');
        console.log('Hammad user in students array:', hammadUser);

        // Check if any user matches the email
        const emailMatch = allUsers.find(u => u.email === formData.email);
        console.log('User with matching email:', emailMatch);

        user = allUsers.find(u =>
          u.email === formData.email &&
          u.password === formData.password &&
          u.role === formData.role
        );

        console.log('Final user match:', user);

        // If not found in mock data, check registered users
        if (!user) {
          const registeredUsers = JSON.parse(localStorage.getItem('ums_registered_users') || '[]');
          console.log('Checking registered users:', registeredUsers);
          user = registeredUsers.find(u =>
            u.email === formData.email &&
            u.password === formData.password &&
            u.role === formData.role
          );
        }
      }

      if (user) {
        // Successful login
        console.log('Login successful for user:', user.name);
        await login(user);
        navigate('/dashboard');
      } else {
        console.log('Login failed - user not found');
        console.log('Available students count:', students.length);
        console.log('Available faculty count:', faculty.length);
        setErrors({ submit: 'Invalid email, password, or role. Please check your credentials and try again.' });
      }
    } catch (error) {
      setErrors({ submit: 'Login failed. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Background Overlay for better contrast */}
      <div className="absolute inset-0 bg-black/40 dark:bg-black/60"></div>
      {/* Academic Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating Academic Icons */}
        <div className="absolute top-20 left-20 text-white/40 animate-float">
          <svg className="w-16 h-16" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z"/>
          </svg>
        </div>

        <div className="absolute top-32 right-32 text-white/35 animate-float" style={{animationDelay: '1s'}}>
          <svg className="w-20 h-20" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
          </svg>
        </div>

        <div className="absolute bottom-32 left-32 text-white/35 animate-float" style={{animationDelay: '2s'}}>
          <svg className="w-18 h-18" fill="currentColor" viewBox="0 0 24 24">
            <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H18V1h-2v1H8V1H6v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2zm0 16h-15v-2h15v2zm0-5h-15V6h15v7z"/>
          </svg>
        </div>

        <div className="absolute top-1/2 right-20 text-white/30 animate-float" style={{animationDelay: '3s'}}>
          <svg className="w-24 h-24" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        </div>

        <div className="absolute bottom-20 right-40 text-white/35 animate-float" style={{animationDelay: '4s'}}>
          <svg className="w-14 h-14" fill="currentColor" viewBox="0 0 24 24">
            <path d="M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z"/>
          </svg>
        </div>

        {/* Mathematical Formulas Background */}
        <div className="absolute top-16 left-1/2 transform -translate-x-1/2 text-white/25 font-mono text-sm animate-fade-in-out">
          ∫ f(x)dx = F(x) + C
        </div>

        <div className="absolute bottom-40 left-16 text-white/25 font-mono text-xs animate-fade-in-out" style={{animationDelay: '2s'}}>
          E = mc²
        </div>

        <div className="absolute top-40 right-16 text-white/25 font-mono text-xs animate-fade-in-out" style={{animationDelay: '3s'}}>
          f(x) = ax² + bx + c
        </div>

        <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-white/25 font-mono text-sm animate-fade-in-out" style={{animationDelay: '1s'}}>
          lim(x→∞) f(x) = L
        </div>

        {/* Geometric Patterns */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 border border-white/20 rotate-45 animate-spin-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-24 h-24 border border-white/20 rotate-12 animate-spin-reverse"></div>

        {/* Glowing Orbs */}
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-700/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary-400/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '4s'}}></div>

        {/* Academic Grid Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="grid grid-cols-12 gap-4 h-full">
            {Array.from({length: 144}).map((_, i) => (
              <div key={i} className="border border-white/10 rounded"></div>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-md w-full space-y-8 relative z-10">
        <div className="text-center animate-fade-in-up">
          {/* University Logo */}
          <div className="mx-auto mb-4 animate-bounce">
            <UniversityLogo
              className="h-20 w-20 mx-auto animate-float hover-scale"
              showUpload={false}
            />
          </div>

          {/* University Name */}
          <h1 className="text-3xl font-bold gradient-text mb-2 animate-slide-in-left hover-glow">
            NFC IET MULTAN
          </h1>
          <p className="text-lg text-gray-300 font-semibold mb-1 animate-slide-in-left" style={{animationDelay: '0.1s'}}>
            National Fertilizer Corporation
          </p>
          <p className="text-base text-gray-400 font-medium mb-4 animate-slide-in-left" style={{animationDelay: '0.2s'}}>
            Institute of Engineering & Technology
          </p>

          {/* Welcome Message */}
          <h2 className="text-2xl font-bold text-white mb-2 animate-slide-in-right" style={{animationDelay: '0.3s'}}>
            Welcome Back
          </h2>
          <p className="text-sm text-gray-400 animate-slide-in-right" style={{animationDelay: '0.4s'}}>
            Sign in to access your dashboard
          </p>
        </div>

        <form className="mt-8 space-y-6 glass-dark p-8 rounded-2xl border border-primary-600/20 shadow-2xl animate-scale-in hover-lift" onSubmit={handleSubmit}>
          <div className="space-y-6">
            {/* Role Selection */}
            <div className="animate-fade-in-up" style={{animationDelay: '0.5s'}}>
              <label htmlFor="role" className="block text-sm font-semibold text-gray-200 mb-2">
                Login as
              </label>
              <select
                id="role"
                name="role"
                value={formData.role}
                onChange={handleChange}
                className="modern-input-dark block w-full px-4 py-3 border-2 border-primary-600/30 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300"
              >
                <option value="student" className="bg-dark-800 text-white">Student</option>
                <option value="faculty" className="bg-dark-800 text-white">Faculty</option>
                <option value="admin" className="bg-dark-800 text-white">Admin</option>
              </select>
            </div>

            {/* Email */}
            <div className="animate-fade-in-up" style={{animationDelay: '0.6s'}}>
              <label htmlFor="email" className="block text-sm font-semibold text-gray-200 mb-2">
                Email address
              </label>
              <div className="relative group">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FiUser className="h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`modern-input-dark block w-full pl-12 pr-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${
                    errors.email ? 'border-red-400' : 'border-primary-600/30'
                  }`}
                  placeholder="Enter your email"
                />
              </div>
              {errors.email && (
                <p className="mt-2 text-sm text-red-400 font-medium animate-slide-in-left">{errors.email}</p>
              )}
            </div>

            {/* Password */}
            <div className="animate-fade-in-up" style={{animationDelay: '0.7s'}}>
              <label htmlFor="password" className="block text-sm font-semibold text-gray-200 mb-2">
                Password
              </label>
              <div className="relative group">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FiLock className="h-5 w-5 text-primary-400 group-hover:text-primary-300 transition-colors duration-200" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  value={formData.password}
                  onChange={handleChange}
                  className={`modern-input-dark block w-full pl-12 pr-12 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-medium hover-lift transition-all duration-300 ${
                    errors.password ? 'border-red-400' : 'border-primary-600/30'
                  }`}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-4 flex items-center text-primary-400 hover:text-primary-300 transition-colors duration-200 hover-scale"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <FiEyeOff className="h-5 w-5 animate-bounce" />
                  ) : (
                    <FiEye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-2 text-sm text-red-400 font-medium animate-slide-in-left">{errors.password}</p>
              )}
            </div>
          </div>

          {errors.submit && (
            <div className="bg-red-500/10 border border-red-400/30 rounded-xl p-4 animate-slide-down">
              <p className="text-sm text-red-400 font-medium">{errors.submit}</p>
            </div>
          )}

          <div className="animate-fade-in-up" style={{animationDelay: '0.8s'}}>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary group relative w-full flex justify-center py-4 px-6 border border-transparent text-sm font-bold rounded-xl text-white focus:outline-none focus-ring disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none transition-all duration-300 hover-lift animate-glow"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Signing in...
                </div>
              ) : (
                'Sign in'
              )}
            </button>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-300">
              Don't have an account?{' '}
              <Link to="/register" className="font-semibold text-primary-400 hover:text-primary-300 transition-colors duration-200">
                Register here
              </Link>
            </p>
          </div>
        </form>

        {/* Demo credentials */}
        <div className="mt-8 glass-dark border border-primary-600/20 rounded-xl p-6 animate-scale-in hover-lift" style={{animationDelay: '1s'}}>
          <h3 className="text-sm font-bold text-primary-300 mb-3 flex items-center animate-slide-in-left">
            <span className="w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse"></span>
            Demo Credentials:
          </h3>
          <div className="text-sm text-gray-300 space-y-2">
            <div className="flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left" style={{animationDelay: '1.1s'}}>
              <span className="font-semibold text-primary-400">Admin:</span>
              <span className="text-xs"><EMAIL> / admin123</span>
            </div>
            <div className="flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left" style={{animationDelay: '1.2s'}}>
              <span className="font-semibold text-primary-400">Faculty:</span>
              <span className="text-xs"><EMAIL> / password123</span>
            </div>
            <div className="flex justify-between items-center p-2 bg-primary-600/10 rounded-lg hover-lift animate-slide-in-left" style={{animationDelay: '1.3s'}}>
              <span className="font-semibold text-primary-400">Student:</span>
              <span className="text-xs"><EMAIL> / password123</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
