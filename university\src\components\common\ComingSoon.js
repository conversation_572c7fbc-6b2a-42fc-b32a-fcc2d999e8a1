import React from 'react';
import { FiClock, FiArrowLeft } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';

const ComingSoon = ({ title, description }) => {
  const navigate = useNavigate();

  return (
    <div className="min-h-96 flex items-center justify-center">
      <div className="text-center modern-card-dark p-12 max-w-lg mx-auto">
        <div className="bg-gradient-primary p-6 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center shadow-purple-lg animate-pulse-slow">
          <FiClock className="h-12 w-12 text-white" />
        </div>
        <h2 className="text-3xl font-bold gradient-text mb-4">
          {title || 'Coming Soon'}
        </h2>
        <p className="text-gray-300 mb-8 max-w-md mx-auto text-lg">
          {description || 'This feature is currently under development. We\'re working hard to bring it to you soon!'}
        </p>
        <button
          onClick={() => navigate('/dashboard')}
          className="btn-primary inline-flex items-center space-x-2 px-6 py-3 rounded-xl font-semibold"
        >
          <FiArrowLeft className="h-4 w-4" />
          <span>Back to Dashboard</span>
        </button>
      </div>
    </div>
  );
};

export default ComingSoon;
