import React, { useState } from 'react';
import { 
  FiMenu, 
  FiBell, 
  FiUser, 
  FiSettings, 
  FiLogOut,
  FiChevronDown 
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';

const Header = ({ onMenuClick }) => {
  const { user, logout } = useAuth();
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const handleLogout = () => {
    logout();
    setDropdownOpen(false);
  };

  return (
    <header className="glass-dark border-b border-primary-600/20 backdrop-blur-xl">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side */}
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="p-2 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring transition-all duration-200 lg:hidden"
          >
            <FiMenu className="h-6 w-6" />
          </button>

          <div className="ml-4 lg:ml-0">
            <h1 className="text-2xl font-bold gradient-text">
              University Management System
            </h1>
            <p className="text-sm text-gray-400 font-medium">Modern Education Platform</p>
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <button className="p-3 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring relative transition-all duration-200 group">
            <FiBell className="h-5 w-5 group-hover:animate-pulse" />
            <span className="absolute top-2 right-2 block h-2 w-2 rounded-full bg-accent-red animate-pulse"></span>
            <span className="absolute top-2 right-2 block h-2 w-2 rounded-full bg-accent-red animate-ping"></span>
          </button>

          {/* User dropdown */}
          <div className="relative">
            <button
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className="flex items-center space-x-3 p-2 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring transition-all duration-200"
            >
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-purple ring-2 ring-primary-600/30">
                  <span className="text-white text-sm font-semibold">
                    {user?.name?.charAt(0)?.toUpperCase()}
                  </span>
                </div>
                <div className="hidden md:block text-left">
                  <p className="text-sm font-semibold text-white">{user?.name}</p>
                  <p className="text-xs text-primary-300 capitalize font-medium">{user?.role}</p>
                </div>
              </div>
              <FiChevronDown className="h-4 w-4 transition-transform duration-200" />
            </button>

            {/* Dropdown menu */}
            {dropdownOpen && (
              <div className="absolute right-0 mt-3 w-56 glass-dark rounded-xl shadow-2xl py-2 z-50 border border-primary-600/20 animate-slide-down">
                <div className="px-4 py-3 border-b border-primary-600/20 bg-gradient-to-r from-primary-600/10 to-primary-700/10">
                  <p className="text-sm font-semibold text-white">{user?.name}</p>
                  <p className="text-xs text-primary-300">{user?.email}</p>
                </div>

                <button className="flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:text-white hover:bg-primary-600/20 transition-all duration-200 group">
                  <FiUser className="mr-3 h-4 w-4 group-hover:text-primary-300" />
                  Profile
                </button>

                <button className="flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:text-white hover:bg-primary-600/20 transition-all duration-200 group">
                  <FiSettings className="mr-3 h-4 w-4 group-hover:text-primary-300" />
                  Settings
                </button>

                <div className="border-t border-primary-600/20 mt-2">
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-3 text-sm text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-200 group"
                  >
                    <FiLogOut className="mr-3 h-4 w-4 group-hover:text-red-300" />
                    Sign out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
