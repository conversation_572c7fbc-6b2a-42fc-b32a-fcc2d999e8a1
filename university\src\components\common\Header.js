import React, { useState } from 'react';
import {
  FiMenu,
  FiBell,
  FiUser,
  FiSettings,
  FiLogOut,
  FiChevronDown
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import UniversityLogo from './UniversityLogo';

const Header = ({ onMenuClick }) => {
  const { user, logout } = useAuth();
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const handleLogout = () => {
    logout();
    setDropdownOpen(false);
  };

  return (
    <header className="glass-dark border-b border-primary-600/20 backdrop-blur-xl">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side */}
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="p-2 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring transition-all duration-200 lg:hidden"
          >
            <FiMenu className="h-6 w-6" />
          </button>

          <div className="flex items-center space-x-4 ml-4 lg:ml-0 animate-slide-in-left">
            {/* University Logo */}
            <div className="flex-shrink-0">
              <UniversityLogo
                className="h-14 w-14 animate-float hover-scale"
                showUpload={user?.role === 'admin'}
                onLogoChange={(logoUrl) => {
                  localStorage.setItem('universityLogo', logoUrl);
                }}
              />
            </div>

            {/* University Name and System Title */}
            <div className="animate-fade-in-up">
              <div className="flex items-center space-x-2 mb-1">
                <h1 className="text-xl font-bold gradient-text hover-glow transition-all duration-300">
                  NFC IET MULTAN
                </h1>
                <span className="hidden sm:block text-gray-400 text-lg animate-pulse-slow">|</span>
                <span className="hidden sm:block text-lg font-semibold text-white hover:text-primary-300 transition-colors duration-300">
                  Management System
                </span>
              </div>
              <p className="text-sm text-gray-400 font-medium hover:text-gray-300 transition-colors duration-300">
                National Fertilizer Corporation Institute of Engineering & Technology
              </p>
            </div>
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4 animate-slide-in-right">
          {/* Notifications */}
          <button className="p-3 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring relative transition-all duration-200 group hover-lift animate-bounce">
            <FiBell className="h-5 w-5 group-hover:animate-bounce" />
            <span className="absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-500 animate-pulse"></span>
            <span className="absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-400 animate-ping"></span>
          </button>

          {/* User dropdown */}
          <div className="relative">
            <button
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className="flex items-center space-x-3 p-2 rounded-xl text-gray-400 hover:text-white hover:bg-primary-600/20 focus:outline-none focus-ring transition-all duration-200 hover-lift group"
            >
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-purple ring-2 ring-primary-600/30 animate-glow hover-scale">
                  <span className="text-white text-sm font-semibold animate-scale-in">
                    {user?.name?.charAt(0)?.toUpperCase()}
                  </span>
                </div>
                <div className="hidden md:block text-left animate-fade-in-up">
                  <p className="text-sm font-semibold text-white group-hover:text-primary-200 transition-colors duration-200">{user?.name}</p>
                  <p className="text-xs text-primary-300 capitalize font-medium group-hover:text-primary-200 transition-colors duration-200">{user?.role}</p>
                </div>
              </div>
              <FiChevronDown className={`h-4 w-4 transition-transform duration-200 ${dropdownOpen ? 'rotate-180' : ''}`} />
            </button>

            {/* Dropdown menu */}
            {dropdownOpen && (
              <div className="absolute right-0 mt-3 w-56 glass-dark rounded-xl shadow-2xl py-2 z-50 border border-primary-600/20 animate-fade-in-down hover-lift">
                <div className="px-4 py-3 border-b border-primary-600/20 bg-gradient-to-r from-primary-600/10 to-primary-700/10 animate-shimmer">
                  <p className="text-sm font-semibold text-white animate-slide-in-left">{user?.name}</p>
                  <p className="text-xs text-primary-300 animate-slide-in-left" style={{animationDelay: '0.1s'}}>{user?.email}</p>
                </div>

                <button className="flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:text-white hover:bg-primary-600/20 transition-all duration-200 group hover-lift animate-slide-in-left" style={{animationDelay: '0.2s'}}>
                  <FiUser className="mr-3 h-4 w-4 group-hover:text-primary-300 group-hover:animate-bounce" />
                  Profile
                </button>

                <button className="flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:text-white hover:bg-primary-600/20 transition-all duration-200 group hover-lift animate-slide-in-left" style={{animationDelay: '0.3s'}}>
                  <FiSettings className="mr-3 h-4 w-4 group-hover:text-primary-300 group-hover:animate-spin" />
                  Settings
                </button>

                <div className="border-t border-primary-600/20 mt-2">
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-3 text-sm text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-200 group hover-lift animate-slide-in-left"
                    style={{animationDelay: '0.4s'}}
                  >
                    <FiLogOut className="mr-3 h-4 w-4 group-hover:text-red-300 group-hover:animate-bounce" />
                    Sign out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
