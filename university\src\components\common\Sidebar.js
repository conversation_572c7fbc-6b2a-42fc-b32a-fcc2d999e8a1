import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  FiHome,
  FiUsers,
  FiBookOpen,
  FiCalendar,
  FiDollarSign,
  FiBook,
  FiFileText,
  FiBarChart2,
  FiSettings,
  FiX,
  FiUser,
  FiClipboard
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';

const Sidebar = ({ isOpen, onClose }) => {
  const { user, isAdmin, isFaculty, isStudent } = useAuth();
  const location = useLocation();

  const getMenuItems = () => {
    const commonItems = [
      { name: 'Dashboard', href: '/dashboard', icon: FiHome },
    ];

    if (isAdmin) {
      return [
        ...commonItems,
        { name: 'Students', href: '/students', icon: FiUsers },
        { name: 'Faculty', href: '/faculty', icon: FiUser },
        { name: 'Courses', href: '/courses', icon: FiBookO<PERSON> },
        { name: 'Academic', href: '/academic', icon: FiClipboard },
        { name: 'Fees', href: '/fees', icon: FiDollarSign },
        { name: 'Library', href: '/library', icon: FiBook },
        { name: 'Exams', href: '/exams', icon: FiFileText },
        { name: 'Reports', href: '/reports', icon: FiBarChart2 },
        { name: 'Settings', href: '/settings', icon: FiSettings },
      ];
    }

    if (isFaculty) {
      return [
        ...commonItems,
        { name: 'My Courses', href: '/my-courses', icon: FiBookOpen },
        { name: 'Students', href: '/my-students', icon: FiUsers },
        { name: 'Grades', href: '/grades', icon: FiClipboard },
        { name: 'Attendance', href: '/attendance', icon: FiCalendar },
        { name: 'Exams', href: '/exams', icon: FiFileText },
        { name: 'Library', href: '/library', icon: FiBook },
      ];
    }

    if (isStudent) {
      return [
        ...commonItems,
        { name: 'My Courses', href: '/my-courses', icon: FiBookOpen },
        { name: 'Grades', href: '/my-grades', icon: FiClipboard },
        { name: 'Attendance', href: '/my-attendance', icon: FiCalendar },
        { name: 'Fees', href: '/my-fees', icon: FiDollarSign },
        { name: 'Library', href: '/library', icon: FiBook },
        { name: 'Exams', href: '/my-exams', icon: FiFileText },
      ];
    }

    return commonItems;
  };

  const menuItems = getMenuItems();

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 glass-dark shadow-2xl transform transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        {/* Sidebar header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-primary-600/20 bg-gradient-to-r from-primary-600/10 to-primary-700/10">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 bg-gradient-primary rounded-xl flex items-center justify-center shadow-purple-lg">
                <span className="text-white font-bold text-xl">U</span>
              </div>
            </div>
            <div className="ml-3">
              <h2 className="text-xl font-bold gradient-text">UMS</h2>
              <p className="text-xs text-gray-400">University System</p>
            </div>
          </div>

          <button
            onClick={onClose}
            className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-primary-600/20 transition-all duration-200 lg:hidden focus-ring"
          >
            <FiX className="h-5 w-5" />
          </button>
        </div>

        {/* User info */}
        <div className="p-6 border-b border-primary-600/20 bg-gradient-to-r from-primary-600/5 to-primary-700/5">
          <div className="flex items-center">
            <div className="h-12 w-12 rounded-full bg-gradient-primary flex items-center justify-center shadow-purple ring-2 ring-primary-600/30">
              <span className="text-white font-semibold text-lg">
                {user?.name?.charAt(0)?.toUpperCase()}
              </span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-white">{user?.name}</p>
              <p className="text-xs text-primary-300 capitalize font-medium">{user?.role}</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="mt-6 px-3 flex-1">
          <div className="space-y-2">
            {menuItems.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <NavLink
                  key={item.name}
                  to={item.href}
                  onClick={onClose}
                  className={`
                    group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 relative overflow-hidden
                    ${isActive
                      ? 'bg-gradient-primary text-white shadow-purple-lg transform scale-105'
                      : 'text-gray-300 hover:text-white hover:bg-primary-600/20 hover:transform hover:scale-105'
                    }
                  `}
                >
                  <item.icon
                    className={`
                      mr-3 h-5 w-5 transition-all duration-300
                      ${isActive ? 'text-white' : 'text-gray-400 group-hover:text-primary-300'}
                    `}
                  />
                  <span className="relative z-10">{item.name}</span>
                  {isActive && (
                    <div className="absolute right-2 w-2 h-2 bg-white rounded-full animate-pulse"></div>
                  )}
                </NavLink>
              );
            })}
          </div>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-primary-600/20 bg-gradient-to-r from-primary-600/5 to-primary-700/5">
          <div className="text-center">
            <p className="text-xs text-gray-400 font-medium">
              © 2024 University Management System
            </p>
            <div className="mt-2 flex justify-center space-x-1">
              <div className="w-1 h-1 bg-primary-500 rounded-full animate-pulse"></div>
              <div className="w-1 h-1 bg-primary-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-1 h-1 bg-primary-300 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
