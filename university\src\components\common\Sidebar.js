import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  FiHome,
  FiUsers,
  FiBookOpen,
  FiCalendar,
  FiDollarSign,
  FiBook,
  FiFileText,
  FiBarChart2,
  FiSettings,
  FiX,
  FiUser,
  FiClipboard
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';

const Sidebar = ({ isOpen, onClose }) => {
  const { user, isAdmin, isFaculty, isStudent } = useAuth();
  const location = useLocation();

  const getMenuItems = () => {
    const commonItems = [
      { name: 'Dashboard', href: '/dashboard', icon: FiHome },
    ];

    if (isAdmin) {
      return [
        ...commonItems,
        { name: 'Students', href: '/students', icon: FiUsers },
        { name: 'Faculty', href: '/faculty', icon: FiUser },
        { name: 'Courses', href: '/courses', icon: FiBookO<PERSON> },
        { name: 'Academic', href: '/academic', icon: FiClipboard },
        { name: 'Fees', href: '/fees', icon: FiDollarSign },
        { name: 'Library', href: '/library', icon: FiBook },
        { name: 'Exams', href: '/exams', icon: FiFileText },
        { name: 'Reports', href: '/reports', icon: FiBarChart2 },
        { name: 'Settings', href: '/settings', icon: FiSettings },
      ];
    }

    if (isFaculty) {
      return [
        ...commonItems,
        { name: 'My Courses', href: '/my-courses', icon: FiBookOpen },
        { name: 'Students', href: '/my-students', icon: FiUsers },
        { name: 'Grades', href: '/grades', icon: FiClipboard },
        { name: 'Attendance', href: '/attendance', icon: FiCalendar },
        { name: 'Exams', href: '/exams', icon: FiFileText },
        { name: 'Library', href: '/library', icon: FiBook },
      ];
    }

    if (isStudent) {
      return [
        ...commonItems,
        { name: 'My Courses', href: '/my-courses', icon: FiBookOpen },
        { name: 'Grades', href: '/my-grades', icon: FiClipboard },
        { name: 'Attendance', href: '/my-attendance', icon: FiCalendar },
        { name: 'Fees', href: '/my-fees', icon: FiDollarSign },
        { name: 'Library', href: '/library', icon: FiBook },
        { name: 'Exams', href: '/my-exams', icon: FiFileText },
      ];
    }

    return commonItems;
  };

  const menuItems = getMenuItems();

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        {/* Sidebar header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">U</span>
              </div>
            </div>
            <div className="ml-3">
              <h2 className="text-lg font-semibold text-gray-900">UMS</h2>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 lg:hidden"
          >
            <FiX className="h-5 w-5" />
          </button>
        </div>

        {/* User info */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center">
              <span className="text-white font-medium">
                {user?.name?.charAt(0)?.toUpperCase()}
              </span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">{user?.name}</p>
              <p className="text-xs text-gray-500 capitalize">{user?.role}</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="mt-6 px-3">
          <div className="space-y-1">
            {menuItems.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <NavLink
                  key={item.name}
                  to={item.href}
                  onClick={onClose}
                  className={`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200
                    ${isActive 
                      ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-700' 
                      : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                    }
                  `}
                >
                  <item.icon 
                    className={`
                      mr-3 h-5 w-5 transition-colors duration-200
                      ${isActive ? 'text-primary-700' : 'text-gray-500 group-hover:text-gray-700'}
                    `}
                  />
                  {item.name}
                </NavLink>
              );
            })}
          </div>
        </nav>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
          <div className="text-center">
            <p className="text-xs text-gray-500">
              © 2024 University Management System
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
