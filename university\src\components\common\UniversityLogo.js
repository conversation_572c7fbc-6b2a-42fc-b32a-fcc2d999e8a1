import React, { useState } from 'react';
import logoImage from '../logo.webp';

const UniversityLogo = ({
  className = "h-12 w-12",
  customLogo = null,
  showUpload = false,
  onLogoChange = null
}) => {
  const [uploadedLogo, setUploadedLogo] = useState(customLogo);

  const handleLogoUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const logoUrl = e.target.result;
        setUploadedLogo(logoUrl);
        if (onLogoChange) {
          onLogoChange(logoUrl);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Determine which logo to show: uploaded logo, stored logo, or default logo
  const logoToShow = uploadedLogo || localStorage.getItem('universityLogo') || logoImage;

  return (
    <div className={`${className} flex items-center justify-center relative group`}>
      <img
        src={logoToShow}
        alt="NFC IET MULTAN Logo"
        className="w-full h-full object-contain rounded-xl shadow-lg animate-scale-in hover-glow transition-all duration-300"
        onError={(e) => {
          // Fallback to default logo if image fails to load
          e.target.src = logoImage;
        }}
      />
      {showUpload && (
        <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl flex items-center justify-center">
          <label className="cursor-pointer text-white text-xs font-semibold bg-primary-600 hover:bg-primary-700 px-3 py-1 rounded-lg transition-colors duration-200">
            Change Logo
            <input
              type="file"
              accept="image/*"
              onChange={handleLogoUpload}
              className="hidden"
            />
          </label>
        </div>
      )}
    </div>
};

export default UniversityLogo;
