import React, { useState } from 'react';

const UniversityLogo = ({
  className = "h-12 w-12",
  customLogo = null,
  showUpload = false,
  onLogoChange = null
}) => {
  const [uploadedLogo, setUploadedLogo] = useState(customLogo);

  const handleLogoUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const logoUrl = e.target.result;
        setUploadedLogo(logoUrl);
        if (onLogoChange) {
          onLogoChange(logoUrl);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // If there's an uploaded logo, show it
  if (uploadedLogo) {
    return (
      <div className={`${className} flex items-center justify-center relative group`}>
        <img
          src={uploadedLogo}
          alt="University Logo"
          className="w-full h-full object-contain rounded-xl shadow-lg animate-scale-in hover-glow"
        />
        {showUpload && (
          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl flex items-center justify-center">
            <label className="cursor-pointer text-white text-xs font-semibold">
              Change Logo
              <input
                type="file"
                accept="image/*"
                onChange={handleLogoUpload}
                className="hidden"
              />
            </label>
          </div>
        )}
      </div>
    );
  }

  // Default SVG logo
  return (
    <div className={`${className} flex items-center justify-center relative group`}>
      {showUpload && (
        <div className="absolute -top-2 -right-2 z-10">
          <label className="cursor-pointer bg-primary-600 hover:bg-primary-700 text-white p-1 rounded-full text-xs transition-colors duration-200">
            📁
            <input
              type="file"
              accept="image/*"
              onChange={handleLogoUpload}
              className="hidden"
            />
          </label>
        </div>
      )}
      <svg
        viewBox="0 0 100 100"
        className="w-full h-full animate-float hover-glow"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Outer Circle with Gradient */}
        <defs>
          <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#7c3aed" />
            <stop offset="50%" stopColor="#a855f7" />
            <stop offset="100%" stopColor="#c084fc" />
          </linearGradient>
          <linearGradient id="innerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#ffffff" />
            <stop offset="100%" stopColor="#e2e8f0" />
          </linearGradient>
        </defs>
        
        {/* Outer Ring */}
        <circle
          cx="50"
          cy="50"
          r="48"
          fill="url(#logoGradient)"
          stroke="#ffffff"
          strokeWidth="2"
        />
        
        {/* Inner Circle */}
        <circle
          cx="50"
          cy="50"
          r="38"
          fill="url(#innerGradient)"
          stroke="url(#logoGradient)"
          strokeWidth="1"
        />
        
        {/* Book Symbol */}
        <g transform="translate(50, 50)">
          {/* Open Book */}
          <path
            d="M-15,-8 L-15,12 L-2,10 L0,12 L2,10 L15,12 L15,-8 L0,-6 Z"
            fill="url(#logoGradient)"
            stroke="#ffffff"
            strokeWidth="0.5"
          />
          
          {/* Book Pages */}
          <line x1="-12" y1="-4" x2="-4" y2="-2" stroke="#ffffff" strokeWidth="0.8" />
          <line x1="-12" y1="0" x2="-4" y2="2" stroke="#ffffff" strokeWidth="0.8" />
          <line x1="-12" y1="4" x2="-4" y2="6" stroke="#ffffff" strokeWidth="0.8" />
          
          <line x1="4" y1="-2" x2="12" y2="-4" stroke="#ffffff" strokeWidth="0.8" />
          <line x1="4" y1="2" x2="12" y2="0" stroke="#ffffff" strokeWidth="0.8" />
          <line x1="4" y1="6" x2="12" y2="4" stroke="#ffffff" strokeWidth="0.8" />
          
          {/* Graduation Cap */}
          <g transform="translate(0, -20)">
            <ellipse cx="0" cy="0" rx="12" ry="3" fill="url(#logoGradient)" />
            <rect x="-1" y="-2" width="2" height="8" fill="url(#logoGradient)" />
            <circle cx="0" cy="6" r="1.5" fill="#ffffff" />
          </g>
        </g>
        
        {/* University Initials */}
        <text
          x="50"
          y="75"
          textAnchor="middle"
          fontSize="8"
          fontWeight="bold"
          fill="url(#logoGradient)"
          fontFamily="Arial, sans-serif"
        >
          NFC
        </text>
        
        <text
          x="50"
          y="85"
          textAnchor="middle"
          fontSize="6"
          fontWeight="bold"
          fill="url(#logoGradient)"
          fontFamily="Arial, sans-serif"
        >
          IET
        </text>
      </svg>
    </div>
  );
};

export default UniversityLogo;
