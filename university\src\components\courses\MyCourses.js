import React, { useState } from 'react';
import { 
  FiBookOpen, 
  FiUsers, 
  FiCalendar, 
  FiClock,
  FiMapPin,
  FiSearch,
  FiFilter,
  FiEye,
  FiEdit,
  FiPlus
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { getStatusColor } from '../../utils/helpers';

const MyCourses = () => {
  const { user, isStudent, isFaculty } = useAuth();
  const { courses, enrollments, students } = useData();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Get user's courses based on role
  const getUserCourses = () => {
    if (isStudent) {
      const studentEnrollments = enrollments.filter(e => e.studentId === user.id);
      return courses.filter(c => 
        studentEnrollments.some(e => e.courseId === c.id)
      );
    } else if (isFaculty) {
      return courses.filter(c => c.facultyId === user.id);
    }
    return [];
  };

  const userCourses = getUserCourses();

  // Filter courses based on search and status
  const filteredCourses = userCourses.filter(course => {
    const matchesSearch = course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.department.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || course.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  const getEnrolledStudentsCount = (courseId) => {
    return enrollments.filter(e => e.courseId === courseId).length;
  };

  const CourseCard = ({ course }) => {
    const enrolledCount = getEnrolledStudentsCount(course.id);
    
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{course.name}</h3>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(course.status)}`}>
                {course.status}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-2">{course.code} • {course.department}</p>
            <p className="text-sm text-gray-500 mb-3">{course.description}</p>
          </div>
          <div className="flex space-x-2">
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg">
              <FiEye className="h-4 w-4" />
            </button>
            {isFaculty && (
              <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg">
                <FiEdit className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="flex items-center space-x-2">
            <FiBookOpen className="h-4 w-4 text-blue-500" />
            <span className="text-sm text-gray-600">{course.credits} Credits</span>
          </div>
          <div className="flex items-center space-x-2">
            <FiUsers className="h-4 w-4 text-green-500" />
            <span className="text-sm text-gray-600">{enrolledCount}/{course.capacity}</span>
          </div>
          <div className="flex items-center space-x-2">
            <FiClock className="h-4 w-4 text-purple-500" />
            <span className="text-sm text-gray-600">{course.schedule.time}</span>
          </div>
          <div className="flex items-center space-x-2">
            <FiMapPin className="h-4 w-4 text-red-500" />
            <span className="text-sm text-gray-600">{course.schedule.room}</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FiCalendar className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-500">
              {course.schedule.days.join(', ')}
            </span>
          </div>
          <div className="flex space-x-2">
            {isStudent && (
              <button className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200">
                View Details
              </button>
            )}
            {isFaculty && (
              <>
                <button className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200">
                  Manage
                </button>
                <button className="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200">
                  Students
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Courses</h1>
          <p className="text-gray-600">
            {isStudent ? 'Your enrolled courses' : 'Courses you are teaching'}
          </p>
        </div>
        {isFaculty && (
          <button className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
            <FiPlus className="h-4 w-4" />
            <span>Add Course</span>
          </button>
        )}
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search courses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <FiFilter className="h-4 w-4 text-gray-400" />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg">
              <FiBookOpen className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Courses</p>
              <p className="text-2xl font-bold text-gray-900">{userCourses.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-lg">
              <FiUsers className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                {isStudent ? 'Credits' : 'Students'}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {isStudent 
                  ? userCourses.reduce((sum, course) => sum + course.credits, 0)
                  : userCourses.reduce((sum, course) => sum + getEnrolledStudentsCount(course.id), 0)
                }
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-lg">
              <FiCalendar className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active</p>
              <p className="text-2xl font-bold text-gray-900">
                {userCourses.filter(c => c.status === 'active').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-lg">
              <FiClock className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">This Semester</p>
              <p className="text-2xl font-bold text-gray-900">
                {userCourses.filter(c => c.semester === user.semester).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Courses Grid */}
      <div className="space-y-6">
        {filteredCourses.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredCourses.map((course) => (
              <CourseCard key={course.id} course={course} />
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <FiBookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No courses found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || filterStatus !== 'all' 
                ? 'Try adjusting your search or filter criteria.'
                : isStudent 
                  ? 'You are not enrolled in any courses yet.'
                  : 'You are not assigned to any courses yet.'
              }
            </p>
            {isFaculty && (
              <button className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
                Add Your First Course
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyCourses;
