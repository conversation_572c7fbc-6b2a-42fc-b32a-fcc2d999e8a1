import React, { useState } from 'react';
import { 
  FiBookOpen, 
  FiUsers, 
  FiCalendar, 
  FiClock,
  FiMapPin,
  FiSearch,
  FiFilter,
  FiEye,
  FiEdit,
  FiPlus
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { getStatusColor } from '../../utils/helpers';

const MyCourses = () => {
  const { user, isStudent, isFaculty } = useAuth();
  const { courses, enrollments, students } = useData();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Get user's courses based on role
  const getUserCourses = () => {
    if (isStudent) {
      const studentEnrollments = enrollments.filter(e => e.studentId === user.id);
      return courses.filter(c => 
        studentEnrollments.some(e => e.courseId === c.id)
      );
    } else if (isFaculty) {
      return courses.filter(c => c.facultyId === user.id);
    }
    return [];
  };

  const userCourses = getUserCourses();

  // Filter courses based on search and status
  const filteredCourses = userCourses.filter(course => {
    const matchesSearch = course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.department.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || course.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  const getEnrolledStudentsCount = (courseId) => {
    return enrollments.filter(e => e.courseId === courseId).length;
  };

  const CourseCard = ({ course }) => {
    const enrolledCount = getEnrolledStudentsCount(course.id);

    return (
      <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-lg font-semibold text-white">{course.name}</h3>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(course.status)}`}>
                {course.status}
              </span>
            </div>
            <p className="text-sm text-gray-300 mb-2">{course.code} • {course.department}</p>
            <p className="text-sm text-gray-400 mb-3">{course.description}</p>
          </div>
          <div className="flex space-x-2">
            <button className="p-2 text-gray-400 hover:text-white hover:bg-primary-600/20 rounded-xl transition-colors duration-200">
              <FiEye className="h-4 w-4" />
            </button>
            {isFaculty && (
              <button className="p-2 text-gray-400 hover:text-white hover:bg-primary-600/20 rounded-xl transition-colors duration-200">
                <FiEdit className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="flex items-center space-x-2">
            <FiBookOpen className="h-4 w-4 text-blue-400" />
            <span className="text-sm text-gray-300">{course.credits} Credits</span>
          </div>
          <div className="flex items-center space-x-2">
            <FiUsers className="h-4 w-4 text-green-400" />
            <span className="text-sm text-gray-300">{enrolledCount}/{course.capacity}</span>
          </div>
          <div className="flex items-center space-x-2">
            <FiClock className="h-4 w-4 text-purple-400" />
            <span className="text-sm text-gray-300">{course.schedule.time}</span>
          </div>
          <div className="flex items-center space-x-2">
            <FiMapPin className="h-4 w-4 text-red-400" />
            <span className="text-sm text-gray-300">{course.schedule.room}</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FiCalendar className="h-4 w-4 text-primary-400" />
            <span className="text-sm text-gray-400">
              {course.schedule.days.join(', ')}
            </span>
          </div>
          <div className="flex space-x-2">
            {isStudent && (
              <button className="px-4 py-2 text-sm bg-blue-500/20 text-blue-400 rounded-xl hover:bg-blue-500/30 transition-colors duration-200 font-semibold">
                View Details
              </button>
            )}
            {isFaculty && (
              <>
                <button className="px-4 py-2 text-sm bg-green-500/20 text-green-400 rounded-xl hover:bg-green-500/30 transition-colors duration-200 font-semibold">
                  Manage
                </button>
                <button className="px-4 py-2 text-sm bg-purple-500/20 text-purple-400 rounded-xl hover:bg-purple-500/30 transition-colors duration-200 font-semibold">
                  Students
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold gradient-text">My Courses</h1>
          <p className="text-gray-300 text-lg">
            {isStudent ? 'Your enrolled courses' : 'Courses you are teaching'}
          </p>
        </div>
        {isFaculty && (
          <button className="btn-primary flex items-center space-x-2 px-6 py-3 rounded-xl font-semibold">
            <FiPlus className="h-4 w-4" />
            <span>Add Course</span>
          </button>
        )}
      </div>

      {/* Filters */}
      <div className="modern-card-dark p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search courses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="modern-input w-full pl-10 pr-4 py-3 border-2 border-primary-600/30 rounded-xl bg-dark-800/50 text-white placeholder-gray-400"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <FiFilter className="h-4 w-4 text-primary-400" />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="modern-input border-2 border-primary-600/30 rounded-xl px-4 py-3 bg-dark-800/50 text-white"
              >
                <option value="all" className="bg-dark-800 text-white">All Status</option>
                <option value="active" className="bg-dark-800 text-white">Active</option>
                <option value="inactive" className="bg-dark-800 text-white">Inactive</option>
                <option value="completed" className="bg-dark-800 text-white">Completed</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-blue-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiBookOpen className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Total Courses</p>
              <p className="text-3xl font-bold text-white">{userCourses.length}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-green-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiUsers className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">
                {isStudent ? 'Credits' : 'Students'}
              </p>
              <p className="text-3xl font-bold text-white">
                {isStudent
                  ? userCourses.reduce((sum, course) => sum + course.credits, 0)
                  : userCourses.reduce((sum, course) => sum + getEnrolledStudentsCount(course.id), 0)
                }
              </p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-purple-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiCalendar className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Active</p>
              <p className="text-3xl font-bold text-white">
                {userCourses.filter(c => c.status === 'active').length}
              </p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-yellow-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiClock className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">This Semester</p>
              <p className="text-3xl font-bold text-white">
                {userCourses.filter(c => c.semester === user.semester).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Courses Grid */}
      <div className="space-y-6">
        {filteredCourses.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredCourses.map((course) => (
              <CourseCard key={course.id} course={course} />
            ))}
          </div>
        ) : (
          <div className="modern-card-dark p-12 text-center">
            <FiBookOpen className="h-12 w-12 text-primary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">No courses found</h3>
            <p className="text-gray-400 mb-4">
              {searchTerm || filterStatus !== 'all'
                ? 'Try adjusting your search or filter criteria.'
                : isStudent
                  ? 'You are not enrolled in any courses yet.'
                  : 'You are not assigned to any courses yet.'
              }
            </p>
            {isFaculty && (
              <button className="btn-primary px-6 py-3 rounded-xl font-semibold">
                Add Your First Course
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyCourses;
