import React from 'react';
import {
  FiUsers,
  FiBookOpen,
  FiDollarSign,
  FiUser,
  FiCalendar,
  FiBook,
  FiFileText
} from 'react-icons/fi';
import { useData } from '../../context/DataContext';
import { formatCurrency, calculatePercentage } from '../../utils/helpers';

const AdminDashboard = () => {
  const {
    students,
    faculty,
    courses,
    fees,
    books,
    borrowedBooks,
    exams
  } = useData();

  // Calculate statistics
  const totalStudents = students.length;
  const activeStudents = students.filter(s => s.status === 'active').length;
  const totalFaculty = faculty.length;
  const activeFaculty = faculty.filter(f => f.status === 'active').length;
  const totalCourses = courses.length;
  const activeCourses = courses.filter(c => c.status === 'active').length;
  
  const totalFees = fees.reduce((sum, fee) => sum + fee.totalAmount, 0);
  const paidFees = fees.reduce((sum, fee) => sum + fee.paidAmount, 0);
  const pendingFees = totalFees - paidFees;
  
  const totalBooks = books.reduce((sum, book) => sum + book.totalCopies, 0);
  const borrowedBooksCount = borrowedBooks.filter(b => b.status === 'borrowed').length;
  const availableBooks = totalBooks - borrowedBooksCount;

  const upcomingExams = exams.filter(exam => {
    const examDate = new Date(exam.date);
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    return examDate >= today && examDate <= nextWeek;
  }).length;

  const stats = [
    {
      name: 'Total Students',
      value: totalStudents,
      subValue: `${activeStudents} active`,
      icon: FiUsers,
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'increase'
    },
    {
      name: 'Faculty Members',
      value: totalFaculty,
      subValue: `${activeFaculty} active`,
      icon: FiUser,
      color: 'bg-green-500',
      change: '+3%',
      changeType: 'increase'
    },
    {
      name: 'Active Courses',
      value: activeCourses,
      subValue: `${totalCourses} total`,
      icon: FiBookOpen,
      color: 'bg-purple-500',
      change: '+8%',
      changeType: 'increase'
    },
    {
      name: 'Fee Collection',
      value: formatCurrency(paidFees),
      subValue: `${formatCurrency(pendingFees)} pending`,
      icon: FiDollarSign,
      color: 'bg-yellow-500',
      change: '+15%',
      changeType: 'increase'
    },
    {
      name: 'Library Books',
      value: totalBooks,
      subValue: `${availableBooks} available`,
      icon: FiBook,
      color: 'bg-indigo-500',
      change: '+5%',
      changeType: 'increase'
    },
    {
      name: 'Upcoming Exams',
      value: upcomingExams,
      subValue: 'This week',
      icon: FiFileText,
      color: 'bg-red-500',
      change: '3 scheduled',
      changeType: 'neutral'
    }
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'enrollment',
      message: 'New student John Doe enrolled in Computer Science',
      time: '2 hours ago',
      icon: FiUsers,
      color: 'text-blue-600'
    },
    {
      id: 2,
      type: 'course',
      message: 'Dr. Sarah Wilson created new course "Advanced Algorithms"',
      time: '4 hours ago',
      icon: FiBookOpen,
      color: 'text-green-600'
    },
    {
      id: 3,
      type: 'payment',
      message: 'Fee payment received from Jane Smith - $5,350',
      time: '6 hours ago',
      icon: FiDollarSign,
      color: 'text-yellow-600'
    },
    {
      id: 4,
      type: 'exam',
      message: 'Midterm exam scheduled for CS101 on Feb 15',
      time: '1 day ago',
      icon: FiCalendar,
      color: 'text-purple-600'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center lg:text-left">
        <h1 className="text-4xl font-bold gradient-text mb-2">Admin Dashboard</h1>
        <p className="text-gray-300 text-lg">Welcome back! Here's what's happening at your university.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
            <div className="flex items-center">
              <div className={`${stat.color} p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-semibold text-gray-300">{stat.name}</p>
                <p className="text-3xl font-bold text-white mb-1">{stat.value}</p>
                <p className="text-sm text-gray-400">{stat.subValue}</p>
              </div>
              <div className="text-right">
                <span className={`text-sm font-semibold px-2 py-1 rounded-lg ${
                  stat.changeType === 'increase' ? 'text-green-400 bg-green-500/20' :
                  stat.changeType === 'decrease' ? 'text-red-400 bg-red-500/20' : 'text-gray-400 bg-gray-500/20'
                }`}>
                  {stat.change}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-4">
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
              <FiUsers className="h-6 w-6 text-blue-600 mb-2" />
              <p className="font-medium text-gray-900">Add Student</p>
              <p className="text-sm text-gray-600">Register new student</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
              <FiUser className="h-6 w-6 text-green-600 mb-2" />
              <p className="font-medium text-gray-900">Add Faculty</p>
              <p className="text-sm text-gray-600">Add faculty member</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
              <FiBookOpen className="h-6 w-6 text-purple-600 mb-2" />
              <p className="font-medium text-gray-900">Create Course</p>
              <p className="text-sm text-gray-600">Add new course</p>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
              <FiFileText className="h-6 w-6 text-red-600 mb-2" />
              <p className="font-medium text-gray-900">Schedule Exam</p>
              <p className="text-sm text-gray-600">Create exam schedule</p>
            </button>
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg bg-gray-100`}>
                  <activity.icon className={`h-4 w-4 ${activity.color}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4">
            <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
              View all activities →
            </button>
          </div>
        </div>
      </div>

      {/* Performance Overview */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Overview</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{calculatePercentage(activeStudents, totalStudents)}%</div>
            <div className="text-sm text-gray-600">Student Retention</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{calculatePercentage(paidFees, totalFees)}%</div>
            <div className="text-sm text-gray-600">Fee Collection</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{calculatePercentage(activeCourses, totalCourses)}%</div>
            <div className="text-sm text-gray-600">Course Completion</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{calculatePercentage(availableBooks, totalBooks)}%</div>
            <div className="text-sm text-gray-600">Library Utilization</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
