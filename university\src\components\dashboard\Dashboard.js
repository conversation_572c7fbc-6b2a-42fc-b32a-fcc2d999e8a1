import React from 'react';
import { useAuth } from '../../context/AuthContext';
import AdminDashboard from './AdminDashboard';
import FacultyDashboard from './FacultyDashboard';
import StudentDashboard from './StudentDashboard';

const Dashboard = () => {
  const { isAdmin, isFaculty, isStudent } = useAuth();

  if (isAdmin) {
    return <AdminDashboard />;
  }

  if (isFaculty) {
    return <FacultyDashboard />;
  }

  if (isStudent) {
    return <StudentDashboard />;
  }

  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to UMS</h2>
        <p className="text-gray-600">Please contact administrator for access.</p>
      </div>
    </div>
  );
};

export default Dashboard;
