import React from 'react';
import { 
  FiBookOpen, 
  FiUsers, 
  FiCalendar, 
  FiFileText,
  FiClock,
  FiTrendingUp,
  FiCheckCircle,
  FiAlertCircle
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { formatDate } from '../../utils/helpers';

const FacultyDashboard = () => {
  const { user } = useAuth();
  const {
    courses,
    students,
    enrollments,
    exams
  } = useData();

  // Get faculty's data
  const facultyCourses = courses.filter(c => c.facultyId === user.id);
  const facultyStudents = enrollments
    .filter(e => facultyCourses.some(c => c.id === e.courseId))
    .map(e => students.find(s => s.id === e.studentId))
    .filter(Boolean);
  
  const facultyExams = exams.filter(exam => 
    facultyCourses.some(c => c.id === exam.courseId)
  );

  // Get upcoming exams
  const upcomingExams = facultyExams.filter(exam => {
    const examDate = new Date(exam.date);
    const today = new Date();
    return examDate >= today;
  }).slice(0, 3);

  // Calculate statistics
  const totalStudents = facultyStudents.length;
  const activeCourses = facultyCourses.filter(c => c.status === 'active').length;
  const pendingGrades = facultyExams.filter(e => e.status === 'completed').length;
  const todayClasses = facultyCourses.filter(course => {
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
    return course.schedule.days.includes(today);
  }).length;

  const stats = [
    {
      name: 'Active Courses',
      value: activeCourses,
      subValue: `${facultyCourses.length} total`,
      icon: FiBookOpen,
      color: 'bg-blue-500'
    },
    {
      name: 'Total Students',
      value: totalStudents,
      subValue: 'Across all courses',
      icon: FiUsers,
      color: 'bg-green-500'
    },
    {
      name: 'Today\'s Classes',
      value: todayClasses,
      subValue: 'Scheduled today',
      icon: FiClock,
      color: 'bg-purple-500'
    },
    {
      name: 'Pending Grades',
      value: pendingGrades,
      subValue: 'To be graded',
      icon: FiFileText,
      color: 'bg-yellow-500'
    }
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'class',
      message: 'CS101 class completed - Introduction to Programming',
      time: '2 hours ago',
      icon: FiCheckCircle,
      color: 'text-green-600'
    },
    {
      id: 2,
      type: 'assignment',
      message: 'New assignment submitted by 15 students',
      time: '4 hours ago',
      icon: FiFileText,
      color: 'text-blue-600'
    },
    {
      id: 3,
      type: 'exam',
      message: 'Midterm exam scheduled for next week',
      time: '1 day ago',
      icon: FiCalendar,
      color: 'text-purple-600'
    },
    {
      id: 4,
      type: 'alert',
      message: '5 students have low attendance in CS201',
      time: '2 days ago',
      icon: FiAlertCircle,
      color: 'text-red-600'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Faculty Dashboard</h1>
        <p className="text-gray-600">Welcome back, {user.name}! Here's your teaching overview.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className={`${stat.color} p-3 rounded-lg`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-sm text-gray-500">{stat.subValue}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Courses */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">My Courses</h3>
          <div className="space-y-4">
            {facultyCourses.slice(0, 4).map((course) => {
              const enrolledCount = enrollments.filter(e => e.courseId === course.id).length;
              return (
                <div key={course.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">{course.name}</h4>
                    <p className="text-sm text-gray-600">{course.code} • {course.credits} credits</p>
                    <p className="text-xs text-gray-500">
                      {course.schedule.days.join(', ')} • {course.schedule.time}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">{enrolledCount} students</div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      course.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {course.status}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
          {facultyCourses.length > 4 && (
            <div className="mt-4">
              <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                View all courses →
              </button>
            </div>
          )}
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg bg-gray-100`}>
                  <activity.icon className={`h-4 w-4 ${activity.color}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4">
            <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
              View all activities →
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Upcoming Exams */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming Exams</h3>
          <div className="space-y-3">
            {upcomingExams.length > 0 ? (
              upcomingExams.map((exam) => {
                const course = facultyCourses.find(c => c.id === exam.courseId);
                return (
                  <div key={exam.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                    <div className="bg-red-100 p-2 rounded-lg">
                      <FiFileText className="h-4 w-4 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{exam.name}</h4>
                      <p className="text-sm text-gray-600">{course?.name}</p>
                      <p className="text-xs text-gray-500">{formatDate(exam.date)} • {exam.time}</p>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-gray-500 text-center py-4">No upcoming exams</p>
            )}
          </div>
        </div>

        {/* Today's Schedule */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Schedule</h3>
          <div className="space-y-3">
            {facultyCourses
              .filter(course => {
                const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
                return course.schedule.days.includes(today);
              })
              .map((course) => (
                <div key={course.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <FiClock className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{course.name}</h4>
                    <p className="text-sm text-gray-600">{course.schedule.time}</p>
                    <p className="text-xs text-gray-500">Room: {course.schedule.room}</p>
                  </div>
                </div>
              ))}
            {todayClasses === 0 && (
              <p className="text-gray-500 text-center py-4">No classes today</p>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <FiFileText className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium">Grade Assignments</span>
            </button>
            <button className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <FiCalendar className="h-5 w-5 text-green-600" />
              <span className="text-sm font-medium">Take Attendance</span>
            </button>
            <button className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <FiUsers className="h-5 w-5 text-purple-600" />
              <span className="text-sm font-medium">View Students</span>
            </button>
            <button className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <FiTrendingUp className="h-5 w-5 text-yellow-600" />
              <span className="text-sm font-medium">Class Analytics</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FacultyDashboard;
