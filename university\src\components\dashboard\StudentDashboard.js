import React from 'react';
import {
  FiBookOpen,
  FiCalendar,
  FiDollarSign,
  FiFileText,
  FiBook,
  FiAward
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { formatCurrency, calculateAttendancePercentage, getGradeColor } from '../../utils/helpers';

const StudentDashboard = () => {
  const { user } = useAuth();
  const { 
    courses, 
    enrollments, 
    grades, 
    attendance, 
    fees, 
    borrowedBooks,
    exams 
  } = useData();

  // Get student's data
  const studentEnrollments = enrollments.filter(e => e.studentId === user.id);
  const studentCourses = courses.filter(c => 
    studentEnrollments.some(e => e.courseId === c.id)
  );
  const studentGrades = grades.filter(g => g.studentId === user.id);
  const studentAttendance = attendance.filter(a => a.studentId === user.id);
  const studentFees = fees.filter(f => f.studentId === user.id);
  const studentBorrowedBooks = borrowedBooks.filter(b => b.studentId === user.id && b.status === 'borrowed');
  
  // Get upcoming exams for student's courses
  const upcomingExams = exams.filter(exam => {
    const examDate = new Date(exam.date);
    const today = new Date();
    return examDate >= today && studentCourses.some(c => c.id === exam.courseId);
  }).slice(0, 3);

  // Calculate statistics
  const currentSemesterFees = studentFees.find(f => f.semester === user.semester);
  const totalFeesDue = currentSemesterFees ? currentSemesterFees.dueAmount : 0;
  const attendancePercentage = calculateAttendancePercentage(studentAttendance);
  const currentGPA = studentGrades.length > 0 
    ? (studentGrades.reduce((sum, g) => sum + g.gpa, 0) / studentGrades.length).toFixed(2)
    : '0.00';

  const stats = [
    {
      name: 'Enrolled Courses',
      value: studentCourses.length,
      subValue: 'This semester',
      icon: FiBookOpen,
      color: 'bg-blue-500'
    },
    {
      name: 'Current GPA',
      value: currentGPA,
      subValue: 'Overall performance',
      icon: FiAward,
      color: 'bg-green-500'
    },
    {
      name: 'Attendance',
      value: `${attendancePercentage}%`,
      subValue: 'This semester',
      icon: FiCalendar,
      color: 'bg-purple-500'
    },
    {
      name: 'Fees Due',
      value: formatCurrency(totalFeesDue),
      subValue: 'Current semester',
      icon: FiDollarSign,
      color: totalFeesDue > 0 ? 'bg-red-500' : 'bg-green-500'
    }
  ];

  const recentGrades = studentGrades.slice(-3).reverse();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Student Dashboard</h1>
        <p className="text-gray-600">Welcome back, {user.name}! Here's your academic overview.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className={`${stat.color} p-3 rounded-lg`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-sm text-gray-500">{stat.subValue}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Courses */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Courses</h3>
          <div className="space-y-4">
            {studentCourses.slice(0, 4).map((course) => (
              <div key={course.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">{course.name}</h4>
                  <p className="text-sm text-gray-600">{course.code} • {course.credits} credits</p>
                  <p className="text-xs text-gray-500">
                    {course.schedule.days.join(', ')} • {course.schedule.time}
                  </p>
                </div>
                <div className="text-right">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Enrolled
                  </span>
                </div>
              </div>
            ))}
          </div>
          {studentCourses.length > 4 && (
            <div className="mt-4">
              <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                View all courses →
              </button>
            </div>
          )}
        </div>

        {/* Recent Grades */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Grades</h3>
          <div className="space-y-4">
            {recentGrades.length > 0 ? (
              recentGrades.map((grade) => {
                const course = courses.find(c => c.id === grade.courseId);
                return (
                  <div key={grade.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">{course?.name}</h4>
                      <p className="text-sm text-gray-600">{course?.code}</p>
                      <p className="text-xs text-gray-500">Semester {grade.semester}, {grade.year}</p>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${getGradeColor(grade.grade)}`}>
                        {grade.grade}
                      </div>
                      <div className="text-sm text-gray-600">GPA: {grade.gpa}</div>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-gray-500 text-center py-4">No grades available yet</p>
            )}
          </div>
          <div className="mt-4">
            <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
              View all grades →
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Upcoming Exams */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming Exams</h3>
          <div className="space-y-3">
            {upcomingExams.length > 0 ? (
              upcomingExams.map((exam) => {
                const course = courses.find(c => c.id === exam.courseId);
                return (
                  <div key={exam.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                    <div className="bg-red-100 p-2 rounded-lg">
                      <FiFileText className="h-4 w-4 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{exam.name}</h4>
                      <p className="text-sm text-gray-600">{course?.name}</p>
                      <p className="text-xs text-gray-500">{exam.date} • {exam.time}</p>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-gray-500 text-center py-4">No upcoming exams</p>
            )}
          </div>
        </div>

        {/* Library Books */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Borrowed Books</h3>
          <div className="space-y-3">
            {studentBorrowedBooks.length > 0 ? (
              studentBorrowedBooks.slice(0, 3).map((borrowedBook) => (
                <div key={borrowedBook.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <FiBook className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">Book Title</h4>
                    <p className="text-sm text-gray-600">Due: {borrowedBook.dueDate}</p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 text-center py-4">No borrowed books</p>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <FiCalendar className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium">View Schedule</span>
            </button>
            <button className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <FiDollarSign className="h-5 w-5 text-green-600" />
              <span className="text-sm font-medium">Pay Fees</span>
            </button>
            <button className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <FiBook className="h-5 w-5 text-purple-600" />
              <span className="text-sm font-medium">Library</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
