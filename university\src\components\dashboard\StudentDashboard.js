import React from 'react';
import {
  FiBookOpen,
  FiCalendar,
  FiDollarSign,
  FiFileText,
  FiBook,
  FiAward
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { formatCurrency, calculateAttendancePercentage, getGradeColor } from '../../utils/helpers';

const StudentDashboard = () => {
  const { user } = useAuth();
  const {
    courses,
    enrollments,
    grades,
    attendance,
    fees,
    books,
    borrowedBooks,
    exams
  } = useData();

  // Get student's data
  const studentEnrollments = enrollments.filter(e => e.studentId === user.id);
  const studentCourses = courses.filter(c => 
    studentEnrollments.some(e => e.courseId === c.id)
  );
  const studentGrades = grades.filter(g => g.studentId === user.id);
  const studentAttendance = attendance.filter(a => a.studentId === user.id);
  const studentFees = fees.filter(f => f.studentId === user.id);
  const studentBorrowedBooks = borrowedBooks.filter(b => b.studentId === user.id && b.status === 'borrowed');
  
  // Get upcoming exams for student's courses
  const upcomingExams = exams.filter(exam => {
    const examDate = new Date(exam.date);
    const today = new Date();
    return examDate >= today && studentCourses.some(c => c.id === exam.courseId);
  }).slice(0, 3);

  // Calculate statistics
  const currentSemesterFees = studentFees.find(f => f.semester === user.semester);
  const totalFeesDue = currentSemesterFees ? currentSemesterFees.dueAmount : 0;
  const attendancePercentage = calculateAttendancePercentage(studentAttendance);
  const currentGPA = studentGrades.length > 0 
    ? (studentGrades.reduce((sum, g) => sum + g.gpa, 0) / studentGrades.length).toFixed(2)
    : '0.00';

  const stats = [
    {
      name: 'Enrolled Courses',
      value: studentCourses.length,
      subValue: 'This semester',
      icon: FiBookOpen,
      color: 'bg-blue-500'
    },
    {
      name: 'Current GPA',
      value: currentGPA,
      subValue: 'Overall performance',
      icon: FiAward,
      color: 'bg-green-500'
    },
    {
      name: 'Attendance',
      value: `${attendancePercentage}%`,
      subValue: 'This semester',
      icon: FiCalendar,
      color: 'bg-purple-500'
    },
    {
      name: 'Fees Due',
      value: formatCurrency(totalFeesDue),
      subValue: 'Current semester',
      icon: FiDollarSign,
      color: totalFeesDue > 0 ? 'bg-red-500' : 'bg-green-500'
    }
  ];

  const recentGrades = studentGrades.slice(-3).reverse();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center lg:text-left">
        <h1 className="text-4xl font-bold gradient-text mb-2">Student Dashboard</h1>
        <p className="text-gray-300 text-lg">Welcome back, {user.name}! Here's your academic overview.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
            <div className="flex items-center">
              <div className={`${stat.color} p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-gray-300">{stat.name}</p>
                <p className="text-3xl font-bold text-white mb-1">{stat.value}</p>
                <p className="text-sm text-gray-400">{stat.subValue}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Courses */}
        <div className="modern-card-dark p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Current Courses</h3>
          <div className="space-y-4">
            {studentCourses.slice(0, 4).map((course) => (
              <div key={course.id} className="flex items-center justify-between p-4 border border-primary-600/20 rounded-xl bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200">
                <div>
                  <h4 className="font-semibold text-white">{course.name}</h4>
                  <p className="text-sm text-gray-300">{course.code} • {course.credits} credits</p>
                  <p className="text-xs text-gray-400">
                    {course.schedule.days.join(', ')} • {course.schedule.time}
                  </p>
                </div>
                <div className="text-right">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-500/20 text-green-400 border border-green-500/30">
                    Enrolled
                  </span>
                </div>
              </div>
            ))}
          </div>
          {studentCourses.length > 4 && (
            <div className="mt-4">
              <button className="text-sm text-primary-400 hover:text-primary-300 font-semibold transition-colors duration-200">
                View all courses →
              </button>
            </div>
          )}
        </div>

        {/* Recent Grades */}
        <div className="modern-card-dark p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Grades</h3>
          <div className="space-y-4">
            {recentGrades.length > 0 ? (
              recentGrades.map((grade) => {
                const course = courses.find(c => c.id === grade.courseId);
                return (
                  <div key={grade.id} className="flex items-center justify-between p-4 border border-primary-600/20 rounded-xl bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200">
                    <div>
                      <h4 className="font-semibold text-white">{course?.name}</h4>
                      <p className="text-sm text-gray-300">{course?.code}</p>
                      <p className="text-xs text-gray-400">Semester {grade.semester}, {grade.year}</p>
                    </div>
                    <div className="text-right">
                      <div className={`text-xl font-bold ${getGradeColor(grade.grade)}`}>
                        {grade.grade}
                      </div>
                      <div className="text-sm text-gray-300">GPA: {grade.gpa}</div>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-gray-400 text-center py-4">No grades available yet</p>
            )}
          </div>
          <div className="mt-4">
            <button className="text-sm text-primary-400 hover:text-primary-300 font-semibold transition-colors duration-200">
              View all grades →
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Exam Results */}
        <div className="modern-card-dark p-6 animate-scale-in hover-lift">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <FiFileText className="h-5 w-5 text-green-400 mr-2" />
            Recent Exam Results
          </h3>
          <div className="space-y-3">
            {(() => {
              // Get completed exams with results for this student
              const completedExamsWithResults = exams
                .filter(exam => exam.status === 'completed' && exam.results)
                .map(exam => {
                  const studentResult = exam.results.find(r => r.studentId === user.id);
                  if (studentResult) {
                    const course = courses.find(c => c.id === exam.courseId);
                    return { ...exam, studentResult, course };
                  }
                  return null;
                })
                .filter(Boolean)
                .slice(0, 3);

              return completedExamsWithResults.length > 0 ? (
                completedExamsWithResults.map((exam) => (
                  <div key={exam.id} className="flex items-center justify-between p-3 border border-primary-600/20 rounded-xl bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${
                        exam.studentResult.percentage >= 90 ? 'bg-green-500/20 border border-green-500/30' :
                        exam.studentResult.percentage >= 80 ? 'bg-blue-500/20 border border-blue-500/30' :
                        exam.studentResult.percentage >= 70 ? 'bg-yellow-500/20 border border-yellow-500/30' :
                        'bg-red-500/20 border border-red-500/30'
                      }`}>
                        <FiFileText className={`h-4 w-4 ${
                          exam.studentResult.percentage >= 90 ? 'text-green-400' :
                          exam.studentResult.percentage >= 80 ? 'text-blue-400' :
                          exam.studentResult.percentage >= 70 ? 'text-yellow-400' :
                          'text-red-400'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-white text-sm">{exam.name}</h4>
                        <p className="text-xs text-gray-300">{exam.course?.name}</p>
                        <p className="text-xs text-gray-400">{new Date(exam.date).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${getGradeColor(exam.studentResult.grade)}`}>
                        {exam.studentResult.grade}
                      </div>
                      <div className="text-xs text-gray-400">{exam.studentResult.percentage}%</div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-6">
                  <FiFileText className="h-8 w-8 text-green-400 mx-auto mb-2" />
                  <p className="text-gray-400">No exam results yet</p>
                  <p className="text-sm text-gray-500">Results will appear here after exams</p>
                </div>
              );
            })()}
          </div>
          <div className="mt-4">
            <button className="w-full btn-secondary py-2 rounded-xl font-semibold text-sm hover-lift">
              View All Results
            </button>
          </div>
        </div>

        {/* Library Books */}
        <div className="modern-card-dark p-6 animate-scale-in hover-lift">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <FiBook className="h-5 w-5 text-primary-400 mr-2" />
            Borrowed Books
          </h3>
          <div className="space-y-3">
            {studentBorrowedBooks.length > 0 ? (
              studentBorrowedBooks.slice(0, 3).map((borrowedBook) => {
                const book = books.find(b => b.id === borrowedBook.bookId);
                return (
                  <div key={borrowedBook.id} className="flex items-center space-x-3 p-3 border border-primary-600/20 rounded-lg bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200">
                    <div className="bg-primary-600/20 p-2 rounded-lg">
                      <FiBook className="h-4 w-4 text-primary-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-white">{book?.title || 'Unknown Book'}</h4>
                      <p className="text-sm text-gray-400">Due: {new Date(borrowedBook.dueDate).toLocaleDateString()}</p>
                      <p className="text-xs text-primary-300">Author: {book?.author || 'Unknown'}</p>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-6">
                <FiBook className="h-8 w-8 text-primary-400 mx-auto mb-2" />
                <p className="text-gray-400">No borrowed books</p>
                <p className="text-sm text-gray-500">Visit the library to borrow books</p>
              </div>
            )}
          </div>
          <div className="mt-4">
            <button className="w-full btn-primary py-2 rounded-xl font-semibold text-sm hover-lift">
              Browse Library
            </button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="modern-card-dark p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full flex items-center space-x-3 p-4 border border-primary-600/20 rounded-xl hover:bg-primary-600/10 transition-colors duration-200 group">
              <FiCalendar className="h-5 w-5 text-blue-400 group-hover:text-blue-300" />
              <span className="text-sm font-semibold text-gray-300 group-hover:text-white">View Schedule</span>
            </button>
            <button className="w-full flex items-center space-x-3 p-4 border border-primary-600/20 rounded-xl hover:bg-primary-600/10 transition-colors duration-200 group">
              <FiDollarSign className="h-5 w-5 text-green-400 group-hover:text-green-300" />
              <span className="text-sm font-semibold text-gray-300 group-hover:text-white">Pay Fees</span>
            </button>
            <button className="w-full flex items-center space-x-3 p-4 border border-primary-600/20 rounded-xl hover:bg-primary-600/10 transition-colors duration-200 group">
              <FiBook className="h-5 w-5 text-purple-400 group-hover:text-purple-300" />
              <span className="text-sm font-semibold text-gray-300 group-hover:text-white">Library</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
