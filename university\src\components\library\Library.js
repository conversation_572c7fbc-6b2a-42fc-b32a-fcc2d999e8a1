import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { FiBook, FiSearch, FiFilter, FiCalendar, FiUser, FiMapPin, FiAlertTriangle } from 'react-icons/fi';

const Library = () => {
  const { user } = useAuth();
  const { books, borrowedBooks } = useData();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [activeTab, setActiveTab] = useState('browse');

  // Get unique categories
  const categories = [...new Set(books.map(book => book.category))];

  // Filter books
  const filteredBooks = books.filter(book => {
    const matchesSearch = book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         book.isbn.includes(searchTerm);
    const matchesCategory = selectedCategory === 'all' || book.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Get user's borrowed books
  const userBorrowedBooks = borrowedBooks.filter(b => b.studentId === user.id);
  const currentlyBorrowed = userBorrowedBooks.filter(b => b.status === 'borrowed' || b.status === 'overdue');
  const borrowHistory = userBorrowedBooks.filter(b => b.status === 'returned');

  const getStatusColor = (status) => {
    switch (status) {
      case 'available':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'borrowed':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'overdue':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDaysUntilDue = (dueDateString) => {
    const dueDate = new Date(dueDateString);
    const today = new Date();
    const diffTime = dueDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return `${Math.abs(diffDays)} days overdue`;
    if (diffDays === 0) return 'Due today';
    if (diffDays === 1) return 'Due tomorrow';
    return `${diffDays} days left`;
  };

  const BookCard = ({ book }) => (
    <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-bold text-white mb-2">{book.title}</h3>
          <p className="text-gray-300 mb-1">by {book.author}</p>
          <p className="text-sm text-gray-400 mb-2">{book.publisher} • {book.edition}</p>
          <div className="flex items-center space-x-2 mb-2">
            <FiMapPin className="h-4 w-4 text-primary-400" />
            <span className="text-sm text-gray-400">{book.location}</span>
          </div>
        </div>
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(book.status)}`}>
          {book.status}
        </span>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-2 bg-dark-800/30 rounded-lg">
          <p className="text-xs text-gray-400">Available</p>
          <p className="text-lg font-semibold text-white">{book.availableCopies}</p>
        </div>
        <div className="text-center p-2 bg-dark-800/30 rounded-lg">
          <p className="text-xs text-gray-400">Total</p>
          <p className="text-lg font-semibold text-white">{book.totalCopies}</p>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <button 
          className="btn-primary flex-1 py-2 rounded-xl font-semibold text-sm"
          disabled={book.availableCopies === 0}
        >
          {book.availableCopies > 0 ? 'Borrow Book' : 'Not Available'}
        </button>
        <button className="px-3 py-2 border border-primary-600/30 text-primary-400 rounded-xl hover:bg-primary-600/10 transition-colors duration-200">
          Details
        </button>
      </div>
    </div>
  );

  const BorrowedBookCard = ({ borrowedBook }) => {
    const book = books.find(b => b.id === borrowedBook.bookId);
    if (!book) return null;

    return (
      <div className="border border-primary-600/20 rounded-xl p-6 bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h4 className="text-lg font-bold text-white">{book.title}</h4>
            <p className="text-gray-300">by {book.author}</p>
          </div>
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(borrowedBook.status)}`}>
            {borrowedBook.status}
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <p className="text-xs text-gray-400">Borrowed Date</p>
            <p className="text-white font-semibold">{formatDate(borrowedBook.borrowDate)}</p>
          </div>
          <div>
            <p className="text-xs text-gray-400">Due Date</p>
            <p className="text-white font-semibold">{formatDate(borrowedBook.dueDate)}</p>
          </div>
          <div>
            <p className="text-xs text-gray-400">Status</p>
            <p className={`font-semibold ${borrowedBook.status === 'overdue' ? 'text-red-400' : 'text-green-400'}`}>
              {borrowedBook.status === 'borrowed' ? getDaysUntilDue(borrowedBook.dueDate) : borrowedBook.status}
            </p>
          </div>
        </div>

        {borrowedBook.fine > 0 && (
          <div className="flex items-center space-x-2 mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
            <FiAlertTriangle className="h-4 w-4 text-red-400" />
            <span className="text-red-400 font-semibold">Fine: ${borrowedBook.fine}</span>
          </div>
        )}

        <div className="flex items-center space-x-2">
          {borrowedBook.status === 'borrowed' && borrowedBook.renewalCount < borrowedBook.maxRenewals && (
            <button className="btn-primary px-4 py-2 rounded-xl font-semibold text-sm">
              Renew Book
            </button>
          )}
          <button className="px-4 py-2 border border-primary-600/30 text-primary-400 rounded-xl hover:bg-primary-600/10 transition-colors duration-200 text-sm">
            Return Book
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center lg:text-left animate-fade-in-up">
        <h1 className="text-4xl font-bold gradient-text mb-2 hover-glow">Library</h1>
        <p className="text-gray-300 text-lg animate-slide-in-left" style={{animationDelay: '0.2s'}}>Browse books, manage borrowings, and track your reading</p>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-blue-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiBook className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Total Books</p>
              <p className="text-3xl font-bold text-white">{books.length}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-green-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiUser className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Currently Borrowed</p>
              <p className="text-3xl font-bold text-white">{currentlyBorrowed.length}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-purple-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiCalendar className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Reading History</p>
              <p className="text-3xl font-bold text-white">{borrowHistory.length}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-red-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiAlertTriangle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Overdue</p>
              <p className="text-3xl font-bold text-white">{userBorrowedBooks.filter(b => b.status === 'overdue').length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="modern-card-dark p-6">
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveTab('browse')}
            className={`px-4 py-2 rounded-xl font-semibold transition-colors duration-200 ${
              activeTab === 'browse' 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-400 hover:text-white hover:bg-primary-600/20'
            }`}
          >
            Browse Books
          </button>
          <button
            onClick={() => setActiveTab('borrowed')}
            className={`px-4 py-2 rounded-xl font-semibold transition-colors duration-200 ${
              activeTab === 'borrowed' 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-400 hover:text-white hover:bg-primary-600/20'
            }`}
          >
            My Books ({currentlyBorrowed.length})
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`px-4 py-2 rounded-xl font-semibold transition-colors duration-200 ${
              activeTab === 'history' 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-400 hover:text-white hover:bg-primary-600/20'
            }`}
          >
            History
          </button>
        </div>
      </div>

      {/* Browse Books Tab */}
      {activeTab === 'browse' && (
        <>
          {/* Search and Filters */}
          <div className="modern-card-dark p-6 animate-scale-in hover-lift">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative group animate-slide-in-left">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-primary-400 group-hover:text-primary-300 transition-colors duration-200" />
                <input
                  type="text"
                  placeholder="Search books, authors, or ISBN..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="modern-input-dark w-full pl-10 pr-4 py-3 border-2 border-primary-600/30 rounded-xl font-medium hover-lift transition-all duration-300"
                />
              </div>
              <div className="flex items-center space-x-2 animate-slide-in-right">
                <FiFilter className="h-4 w-4 text-primary-400" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="modern-input-dark border-2 border-primary-600/30 rounded-xl px-4 py-3 font-medium flex-1 hover-lift transition-all duration-300"
                >
                  <option value="all" className="bg-dark-800 text-white">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category} className="bg-dark-800 text-white">
                      {category}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Books Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredBooks.map(book => (
              <BookCard key={book.id} book={book} />
            ))}
          </div>
        </>
      )}

      {/* My Books Tab */}
      {activeTab === 'borrowed' && (
        <div className="space-y-6">
          {currentlyBorrowed.length > 0 ? (
            currentlyBorrowed.map(borrowedBook => (
              <BorrowedBookCard key={borrowedBook.id} borrowedBook={borrowedBook} />
            ))
          ) : (
            <div className="text-center py-8 modern-card-dark">
              <FiBook className="h-12 w-12 text-primary-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">No books borrowed</h3>
              <p className="text-gray-400">You haven't borrowed any books yet.</p>
            </div>
          )}
        </div>
      )}

      {/* History Tab */}
      {activeTab === 'history' && (
        <div className="space-y-6">
          {borrowHistory.length > 0 ? (
            borrowHistory.map(borrowedBook => (
              <BorrowedBookCard key={borrowedBook.id} borrowedBook={borrowedBook} />
            ))
          ) : (
            <div className="text-center py-8 modern-card-dark">
              <FiCalendar className="h-12 w-12 text-primary-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">No reading history</h3>
              <p className="text-gray-400">Your reading history will appear here.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Library;
