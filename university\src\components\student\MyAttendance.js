import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { FiCalendar, FiClock, FiCheckCircle, FiXCircle, FiAlertCircle, FiBarChart2 } from 'react-icons/fi';

const MyAttendance = () => {
  const { user } = useAuth();
  const { attendance, courses, enrollments } = useData();
  const [selectedCourse, setSelectedCourse] = useState('all');

  // Get student's courses
  const studentEnrollments = enrollments.filter(e => e.studentId === user.id);
  const studentCourses = courses.filter(c => 
    studentEnrollments.some(e => e.courseId === c.id)
  );

  // Get student's attendance
  const studentAttendance = attendance.filter(a => a.studentId === user.id);
  
  // Filter attendance by course
  const filteredAttendance = selectedCourse === 'all' 
    ? studentAttendance 
    : studentAttendance.filter(a => a.courseId === selectedCourse);

  // Calculate attendance statistics
  const getAttendanceStats = (courseId = null) => {
    const relevantAttendance = courseId 
      ? studentAttendance.filter(a => a.courseId === courseId)
      : studentAttendance;
    
    const total = relevantAttendance.length;
    const present = relevantAttendance.filter(a => a.status === 'present').length;
    const absent = relevantAttendance.filter(a => a.status === 'absent').length;
    const late = relevantAttendance.filter(a => a.status === 'late').length;
    
    const percentage = total > 0 ? ((present + late) / total * 100).toFixed(1) : 0;
    
    return { total, present, absent, late, percentage };
  };

  const overallStats = getAttendanceStats();

  const getStatusIcon = (status) => {
    switch (status) {
      case 'present':
        return <FiCheckCircle className="h-5 w-5 text-green-400" />;
      case 'absent':
        return <FiXCircle className="h-5 w-5 text-red-400" />;
      case 'late':
        return <FiAlertCircle className="h-5 w-5 text-yellow-400" />;
      default:
        return <FiClock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'present':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'absent':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'late':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center lg:text-left">
        <h1 className="text-4xl font-bold gradient-text mb-2">My Attendance</h1>
        <p className="text-gray-300 text-lg">Track your attendance records and statistics</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-blue-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiBarChart2 className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Overall Attendance</p>
              <p className="text-3xl font-bold text-white">{overallStats.percentage}%</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-green-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiCheckCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Present</p>
              <p className="text-3xl font-bold text-white">{overallStats.present}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-red-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiXCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Absent</p>
              <p className="text-3xl font-bold text-white">{overallStats.absent}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-yellow-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiAlertCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Late</p>
              <p className="text-3xl font-bold text-white">{overallStats.late}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Course Filter */}
      <div className="modern-card-dark p-6">
        <div className="flex items-center space-x-4">
          <FiCalendar className="h-5 w-5 text-primary-400" />
          <label className="text-white font-semibold">Filter by Course:</label>
          <select
            value={selectedCourse}
            onChange={(e) => setSelectedCourse(e.target.value)}
            className="modern-input border-2 border-primary-600/30 rounded-xl px-4 py-2 bg-dark-800/50 text-white"
          >
            <option value="all" className="bg-dark-800 text-white">All Courses</option>
            {studentCourses.map(course => (
              <option key={course.id} value={course.id} className="bg-dark-800 text-white">
                {course.name} ({course.code})
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Course-wise Attendance Summary */}
      <div className="modern-card-dark p-6">
        <h3 className="text-xl font-bold text-white mb-6">Course-wise Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {studentCourses.map(course => {
            const courseStats = getAttendanceStats(course.id);
            return (
              <div key={course.id} className="border border-primary-600/20 rounded-xl p-4 bg-primary-600/5">
                <h4 className="font-semibold text-white mb-2">{course.name}</h4>
                <p className="text-sm text-gray-300 mb-3">{course.code}</p>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-white">{courseStats.percentage}%</span>
                  <div className="text-right text-sm text-gray-400">
                    <div>Present: {courseStats.present}</div>
                    <div>Total: {courseStats.total}</div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Attendance Records */}
      <div className="modern-card-dark p-6">
        <h3 className="text-xl font-bold text-white mb-6">Attendance Records</h3>
        
        {filteredAttendance.length > 0 ? (
          <div className="space-y-4">
            {filteredAttendance
              .sort((a, b) => new Date(b.date) - new Date(a.date))
              .map((record) => {
                const course = courses.find(c => c.id === record.courseId);
                return (
                  <div key={record.id} className="flex items-center justify-between p-4 border border-primary-600/20 rounded-xl bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200">
                    <div className="flex items-center space-x-4">
                      {getStatusIcon(record.status)}
                      <div>
                        <h4 className="font-semibold text-white">{course?.name}</h4>
                        <p className="text-sm text-gray-300">{course?.code}</p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <p className="text-white font-medium">{formatDate(record.date)}</p>
                      <p className="text-sm text-gray-400">{record.time}</p>
                    </div>
                    
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(record.status)}`}>
                      {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                    </span>
                  </div>
                );
              })}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiCalendar className="h-12 w-12 text-primary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">No attendance records found</h3>
            <p className="text-gray-400">No attendance records available for the selected course.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MyAttendance;
