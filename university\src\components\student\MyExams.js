import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { FiFileText, FiCalendar, FiClock, FiMapPin, FiCheckCircle, FiAlertCircle } from 'react-icons/fi';

const MyExams = () => {
  const { user } = useAuth();
  const { exams, courses, enrollments } = useData();
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Get student's courses
  const studentEnrollments = enrollments.filter(e => e.studentId === user.id);
  const studentCourses = courses.filter(c => 
    studentEnrollments.some(e => e.courseId === c.id)
  );

  // Get student's exams
  const studentExams = exams.filter(exam => 
    studentCourses.some(course => course.id === exam.courseId)
  );

  // Categorize exams
  const today = new Date();
  const upcomingExams = studentExams.filter(exam => {
    const examDate = new Date(exam.date);
    return examDate >= today && exam.status === 'scheduled';
  });

  const ongoingExams = studentExams.filter(exam => exam.status === 'ongoing');
  const completedExams = studentExams.filter(exam => exam.status === 'completed');

  // Filter exams based on selection
  const getFilteredExams = () => {
    switch (selectedFilter) {
      case 'upcoming':
        return upcomingExams;
      case 'ongoing':
        return ongoingExams;
      case 'completed':
        return completedExams;
      default:
        return studentExams;
    }
  };

  const filteredExams = getFilteredExams();

  const getExamTypeColor = (type) => {
    switch (type) {
      case 'final':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'midterm':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'quiz':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'assignment':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'scheduled':
        return <FiCalendar className="h-5 w-5 text-blue-400" />;
      case 'ongoing':
        return <FiAlertCircle className="h-5 w-5 text-yellow-400" />;
      case 'completed':
        return <FiCheckCircle className="h-5 w-5 text-green-400" />;
      default:
        return <FiFileText className="h-5 w-5 text-gray-400" />;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString) => {
    if (timeString.includes('-')) {
      const [start, end] = timeString.split('-');
      return `${start} - ${end}`;
    }
    return timeString;
  };

  const getDaysUntilExam = (dateString) => {
    const examDate = new Date(dateString);
    const diffTime = examDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays > 0) return `${diffDays} days`;
    return 'Past';
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center lg:text-left">
        <h1 className="text-4xl font-bold gradient-text mb-2">My Exams</h1>
        <p className="text-gray-300 text-lg">View exam schedules, results, and performance</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-blue-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiCalendar className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Upcoming</p>
              <p className="text-3xl font-bold text-white">{upcomingExams.length}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-yellow-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiAlertCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Ongoing</p>
              <p className="text-3xl font-bold text-white">{ongoingExams.length}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-green-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiCheckCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Completed</p>
              <p className="text-3xl font-bold text-white">{completedExams.length}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-purple-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiFileText className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Total Exams</p>
              <p className="text-3xl font-bold text-white">{studentExams.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filter */}
      <div className="modern-card-dark p-6">
        <div className="flex items-center space-x-4">
          <FiFileText className="h-5 w-5 text-primary-400" />
          <label className="text-white font-semibold">Filter Exams:</label>
          <select
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
            className="modern-input border-2 border-primary-600/30 rounded-xl px-4 py-2 bg-dark-800/50 text-white"
          >
            <option value="all" className="bg-dark-800 text-white">All Exams</option>
            <option value="upcoming" className="bg-dark-800 text-white">Upcoming</option>
            <option value="ongoing" className="bg-dark-800 text-white">Ongoing</option>
            <option value="completed" className="bg-dark-800 text-white">Completed</option>
          </select>
        </div>
      </div>

      {/* Exams List */}
      <div className="modern-card-dark p-6">
        <h3 className="text-xl font-bold text-white mb-6">Exam Details</h3>
        
        {filteredExams.length > 0 ? (
          <div className="space-y-6">
            {filteredExams
              .sort((a, b) => new Date(a.date) - new Date(b.date))
              .map((exam) => {
                const course = courses.find(c => c.id === exam.courseId);
                return (
                  <div key={exam.id} className="border border-primary-600/20 rounded-xl p-6 bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(exam.status)}
                        <div>
                          <h4 className="text-lg font-bold text-white">{exam.name}</h4>
                          <p className="text-gray-300">{course?.name} ({course?.code})</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getExamTypeColor(exam.type)}`}>
                          {exam.type.charAt(0).toUpperCase() + exam.type.slice(1)}
                        </span>
                        {exam.status === 'scheduled' && (
                          <span className="text-sm font-semibold text-primary-400">
                            {getDaysUntilExam(exam.date)}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Exam Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center space-x-2">
                        <FiCalendar className="h-4 w-4 text-blue-400" />
                        <span className="text-white">{formatDate(exam.date)}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <FiClock className="h-4 w-4 text-green-400" />
                        <span className="text-white">{formatTime(exam.time)}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <FiMapPin className="h-4 w-4 text-red-400" />
                        <span className="text-white">{exam.room}</span>
                      </div>
                    </div>

                    {/* Additional Info */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="bg-dark-800/30 p-3 rounded-lg">
                        <p className="text-xs text-gray-400 mb-1">Duration</p>
                        <p className="text-white font-semibold">
                          {exam.duration > 0 ? `${exam.duration} minutes` : 'No time limit'}
                        </p>
                      </div>
                      <div className="bg-dark-800/30 p-3 rounded-lg">
                        <p className="text-xs text-gray-400 mb-1">Total Marks</p>
                        <p className="text-white font-semibold">{exam.totalMarks}</p>
                      </div>
                    </div>

                    {/* Instructions */}
                    {exam.instructions && (
                      <div className="mb-4">
                        <h5 className="text-sm font-semibold text-gray-300 mb-2">Instructions:</h5>
                        <p className="text-gray-400 text-sm">{exam.instructions}</p>
                      </div>
                    )}

                    {/* Syllabus */}
                    {exam.syllabus && (
                      <div className="mb-4">
                        <h5 className="text-sm font-semibold text-gray-300 mb-2">Syllabus:</h5>
                        <p className="text-gray-400 text-sm">{exam.syllabus}</p>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-3 pt-4 border-t border-primary-600/20">
                      {exam.status === 'scheduled' && (
                        <button className="btn-primary px-4 py-2 rounded-xl font-semibold text-sm">
                          View Details
                        </button>
                      )}
                      {exam.status === 'ongoing' && (
                        <button className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-xl font-semibold text-sm transition-colors duration-200">
                          Continue Exam
                        </button>
                      )}
                      {exam.status === 'completed' && (
                        <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-xl font-semibold text-sm transition-colors duration-200">
                          View Results
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiFileText className="h-12 w-12 text-primary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">No exams found</h3>
            <p className="text-gray-400">No exams available for the selected filter.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MyExams;
