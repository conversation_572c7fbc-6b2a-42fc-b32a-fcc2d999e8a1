import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { FiDollarSign, FiCreditCard, FiCalendar, FiCheckCircle, FiAlertTriangle, FiXCircle } from 'react-icons/fi';

const MyFees = () => {
  const { user } = useAuth();
  const { fees } = useData();
  const [selectedYear, setSelectedYear] = useState('all');

  // Get student's fees
  const studentFees = fees.filter(f => f.studentId === user.id);
  
  // Get unique years
  const years = [...new Set(studentFees.map(f => f.year))].sort((a, b) => b - a);
  
  // Filter fees by year
  const filteredFees = selectedYear === 'all' 
    ? studentFees 
    : studentFees.filter(f => f.year === parseInt(selectedYear));

  // Calculate statistics
  const totalPaid = studentFees.reduce((sum, f) => sum + f.paidAmount, 0);
  const totalDue = studentFees.reduce((sum, f) => sum + f.dueAmount, 0);
  const totalAmount = studentFees.reduce((sum, f) => sum + f.totalAmount, 0);
  
  const paidFees = studentFees.filter(f => f.status === 'paid').length;
  const partialFees = studentFees.filter(f => f.status === 'partial').length;
  const unpaidFees = studentFees.filter(f => f.status === 'unpaid').length;

  const getStatusIcon = (status) => {
    switch (status) {
      case 'paid':
        return <FiCheckCircle className="h-5 w-5 text-green-400" />;
      case 'partial':
        return <FiAlertTriangle className="h-5 w-5 text-yellow-400" />;
      case 'unpaid':
        return <FiXCircle className="h-5 w-5 text-red-400" />;
      default:
        return <FiDollarSign className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'partial':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'unpaid':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center lg:text-left">
        <h1 className="text-4xl font-bold gradient-text mb-2">My Fees</h1>
        <p className="text-gray-300 text-lg">View fee details, payment history, and outstanding balances</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-green-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiCheckCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Total Paid</p>
              <p className="text-2xl font-bold text-white">{formatCurrency(totalPaid)}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-red-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiAlertTriangle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Outstanding</p>
              <p className="text-2xl font-bold text-white">{formatCurrency(totalDue)}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-blue-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiDollarSign className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Total Amount</p>
              <p className="text-2xl font-bold text-white">{formatCurrency(totalAmount)}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-purple-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiCreditCard className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Paid Records</p>
              <p className="text-3xl font-bold text-white">{paidFees}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Year Filter */}
      <div className="modern-card-dark p-6">
        <div className="flex items-center space-x-4">
          <FiCalendar className="h-5 w-5 text-primary-400" />
          <label className="text-white font-semibold">Filter by Year:</label>
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(e.target.value)}
            className="modern-input border-2 border-primary-600/30 rounded-xl px-4 py-2 bg-dark-800/50 text-white"
          >
            <option value="all" className="bg-dark-800 text-white">All Years</option>
            {years.map(year => (
              <option key={year} value={year} className="bg-dark-800 text-white">
                {year}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Fee Records */}
      <div className="modern-card-dark p-6">
        <h3 className="text-xl font-bold text-white mb-6">Fee Records</h3>
        
        {filteredFees.length > 0 ? (
          <div className="space-y-6">
            {filteredFees
              .sort((a, b) => b.year - a.year || b.semester - a.semester)
              .map((fee) => (
                <div key={fee.id} className="border border-primary-600/20 rounded-xl p-6 bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(fee.status)}
                      <div>
                        <h4 className="text-lg font-bold text-white">
                          Semester {fee.semester}, {fee.year}
                        </h4>
                        <p className="text-gray-300">
                          Due: {formatDate(fee.dueDate)}
                        </p>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(fee.status)}`}>
                      {fee.status.charAt(0).toUpperCase() + fee.status.slice(1)}
                    </span>
                  </div>

                  {/* Fee Breakdown */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center p-3 bg-dark-800/30 rounded-lg">
                      <p className="text-xs text-gray-400">Tuition</p>
                      <p className="text-lg font-semibold text-white">{formatCurrency(fee.tuitionFee)}</p>
                    </div>
                    <div className="text-center p-3 bg-dark-800/30 rounded-lg">
                      <p className="text-xs text-gray-400">Library</p>
                      <p className="text-lg font-semibold text-white">{formatCurrency(fee.libraryFee)}</p>
                    </div>
                    <div className="text-center p-3 bg-dark-800/30 rounded-lg">
                      <p className="text-xs text-gray-400">Lab</p>
                      <p className="text-lg font-semibold text-white">{formatCurrency(fee.labFee)}</p>
                    </div>
                    <div className="text-center p-3 bg-dark-800/30 rounded-lg">
                      <p className="text-xs text-gray-400">Other</p>
                      <p className="text-lg font-semibold text-white">
                        {formatCurrency(fee.sportsFee + fee.activityFee + fee.technologyFee + fee.otherFees)}
                      </p>
                    </div>
                  </div>

                  {/* Payment Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="text-center p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                      <p className="text-sm text-blue-400">Total Amount</p>
                      <p className="text-xl font-bold text-white">{formatCurrency(fee.totalAmount)}</p>
                    </div>
                    <div className="text-center p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                      <p className="text-sm text-green-400">Paid Amount</p>
                      <p className="text-xl font-bold text-white">{formatCurrency(fee.paidAmount)}</p>
                    </div>
                    <div className="text-center p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <p className="text-sm text-red-400">Due Amount</p>
                      <p className="text-xl font-bold text-white">{formatCurrency(fee.dueAmount)}</p>
                    </div>
                  </div>

                  {/* Payment Details */}
                  {fee.paymentDate && (
                    <div className="flex items-center justify-between text-sm text-gray-400 border-t border-primary-600/20 pt-4">
                      <div>
                        <span>Payment Date: {formatDate(fee.paymentDate)}</span>
                        {fee.paymentMethod && (
                          <span className="ml-4">Method: {fee.paymentMethod.replace('_', ' ').toUpperCase()}</span>
                        )}
                      </div>
                      {fee.transactionId && (
                        <div>Transaction ID: {fee.transactionId}</div>
                      )}
                    </div>
                  )}

                  {/* Action Button for Outstanding Fees */}
                  {fee.dueAmount > 0 && (
                    <div className="mt-4">
                      <button className="btn-primary px-6 py-2 rounded-xl font-semibold">
                        Pay Now - {formatCurrency(fee.dueAmount)}
                      </button>
                    </div>
                  )}
                </div>
              ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiDollarSign className="h-12 w-12 text-primary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">No fee records found</h3>
            <p className="text-gray-400">No fee records available for the selected year.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MyFees;
