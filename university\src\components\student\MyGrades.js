import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { FiTrendingUp, FiAward, FiCalendar, FiBook, FiBarChart2 } from 'react-icons/fi';

const MyGrades = () => {
  const { user } = useAuth();
  const { grades, courses } = useData();
  const [selectedSemester, setSelectedSemester] = useState('all');

  // Get student's grades
  const studentGrades = grades.filter(g => g.studentId === user.id);
  
  // Get unique semesters
  const semesters = [...new Set(studentGrades.map(g => `${g.semester}-${g.year}`))];
  
  // Filter grades by semester
  const filteredGrades = selectedSemester === 'all' 
    ? studentGrades 
    : studentGrades.filter(g => `${g.semester}-${g.year}` === selectedSemester);

  // Calculate statistics
  const completedGrades = studentGrades.filter(g => g.status === 'completed');
  const totalGPA = completedGrades.length > 0 
    ? (completedGrades.reduce((sum, g) => sum + g.gpa, 0) / completedGrades.length).toFixed(2)
    : '0.00';
  
  const getGradeColor = (grade) => {
    if (!grade || typeof grade !== 'string') return 'text-gray-400';
    if (grade.startsWith('A')) return 'text-green-400';
    if (grade.startsWith('B')) return 'text-blue-400';
    if (grade.startsWith('C')) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getGradeStats = () => {
    const gradeCount = {};
    completedGrades.forEach(g => {
      const letter = g.grade.charAt(0);
      gradeCount[letter] = (gradeCount[letter] || 0) + 1;
    });
    return gradeCount;
  };

  const gradeStats = getGradeStats();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center lg:text-left">
        <h1 className="text-4xl font-bold gradient-text mb-2">My Grades</h1>
        <p className="text-gray-300 text-lg">Track your academic performance and progress</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-green-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiTrendingUp className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Overall GPA</p>
              <p className="text-3xl font-bold text-white">{totalGPA}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-blue-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiBook className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">Completed Courses</p>
              <p className="text-3xl font-bold text-white">{completedGrades.length}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-purple-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiAward className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">A Grades</p>
              <p className="text-3xl font-bold text-white">{gradeStats.A || 0}</p>
            </div>
          </div>
        </div>

        <div className="modern-card-dark p-6 hover:scale-105 transition-all duration-300 group">
          <div className="flex items-center">
            <div className="bg-yellow-500 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <FiBarChart2 className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-300">In Progress</p>
              <p className="text-3xl font-bold text-white">{studentGrades.filter(g => g.status === 'in_progress').length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="modern-card-dark p-6">
        <div className="flex items-center space-x-4">
          <FiCalendar className="h-5 w-5 text-primary-400" />
          <label className="text-white font-semibold">Filter by Semester:</label>
          <select
            value={selectedSemester}
            onChange={(e) => setSelectedSemester(e.target.value)}
            className="modern-input border-2 border-primary-600/30 rounded-xl px-4 py-2 bg-dark-800/50 text-white"
          >
            <option value="all" className="bg-dark-800 text-white">All Semesters</option>
            {semesters.map(sem => (
              <option key={sem} value={sem} className="bg-dark-800 text-white">
                Semester {sem.split('-')[0]} - {sem.split('-')[1]}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Grades List */}
      <div className="modern-card-dark p-6">
        <h3 className="text-xl font-bold text-white mb-6">Grade Details</h3>
        
        {filteredGrades.length > 0 ? (
          <div className="space-y-4">
            {filteredGrades.map((grade) => {
              const course = courses.find(c => c.id === grade.courseId);
              return (
                <div key={grade.id} className="border border-primary-600/20 rounded-xl p-6 bg-primary-600/5 hover:bg-primary-600/10 transition-colors duration-200">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-bold text-white">{course?.name}</h4>
                      <p className="text-gray-300">{course?.code} • Semester {grade.semester}, {grade.year}</p>
                    </div>
                    <div className="text-right">
                      <div className={`text-2xl font-bold ${getGradeColor(grade)}`}>
                        {grade.grade || 'In Progress'}
                      </div>
                      {grade.gpa && (
                        <div className="text-sm text-gray-300">GPA: {grade.gpa}</div>
                      )}
                    </div>
                  </div>

                  {/* Grade Breakdown */}
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mt-4">
                    <div className="text-center">
                      <p className="text-xs text-gray-400">Midterm</p>
                      <p className="text-lg font-semibold text-white">{grade.midterm || 'N/A'}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-400">Final</p>
                      <p className="text-lg font-semibold text-white">{grade.final || 'N/A'}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-400">Assignments</p>
                      <p className="text-lg font-semibold text-white">{grade.assignments || 'N/A'}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-400">Quizzes</p>
                      <p className="text-lg font-semibold text-white">{grade.quizzes || 'N/A'}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-400">Total</p>
                      <p className="text-lg font-semibold text-white">{grade.total || 'N/A'}</p>
                    </div>
                  </div>

                  {/* Status Badge */}
                  <div className="mt-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                      grade.status === 'completed' 
                        ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                        : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                    }`}>
                      {grade.status === 'completed' ? 'Completed' : 'In Progress'}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiBook className="h-12 w-12 text-primary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">No grades found</h3>
            <p className="text-gray-400">No grades available for the selected semester.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MyGrades;
