import React, { createContext, useContext, useState, useEffect } from 'react';
import { mockData } from '../data/mockData';

const DataContext = createContext();

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export const DataProvider = ({ children }) => {
  const [students, setStudents] = useState([]);
  const [faculty, setFaculty] = useState([]);
  const [courses, setCourses] = useState([]);
  const [enrollments, setEnrollments] = useState([]);
  const [grades, setGrades] = useState([]);
  const [attendance, setAttendance] = useState([]);
  const [fees, setFees] = useState([]);
  const [books, setBooks] = useState([]);
  const [borrowedBooks, setBorrowedBooks] = useState([]);
  const [exams, setExams] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load data from localStorage or use mock data
    const loadData = () => {
      try {
        const savedStudents = localStorage.getItem('ums_students');
        const savedFaculty = localStorage.getItem('ums_faculty');
        const savedCourses = localStorage.getItem('ums_courses');
        const savedEnrollments = localStorage.getItem('ums_enrollments');
        const savedGrades = localStorage.getItem('ums_grades');
        const savedAttendance = localStorage.getItem('ums_attendance');
        const savedFees = localStorage.getItem('ums_fees');
        const savedBooks = localStorage.getItem('ums_books');
        const savedBorrowedBooks = localStorage.getItem('ums_borrowed_books');
        const savedExams = localStorage.getItem('ums_exams');

        setStudents(savedStudents ? JSON.parse(savedStudents) : mockData.students);
        setFaculty(savedFaculty ? JSON.parse(savedFaculty) : mockData.faculty);
        setCourses(savedCourses ? JSON.parse(savedCourses) : mockData.courses);
        setEnrollments(savedEnrollments ? JSON.parse(savedEnrollments) : mockData.enrollments);
        setGrades(savedGrades ? JSON.parse(savedGrades) : mockData.grades);
        setAttendance(savedAttendance ? JSON.parse(savedAttendance) : mockData.attendance);
        setFees(savedFees ? JSON.parse(savedFees) : mockData.fees);
        setBooks(savedBooks ? JSON.parse(savedBooks) : mockData.books);
        setBorrowedBooks(savedBorrowedBooks ? JSON.parse(savedBorrowedBooks) : mockData.borrowedBooks);
        setExams(savedExams ? JSON.parse(savedExams) : mockData.exams);
      } catch (error) {
        console.error('Error loading data:', error);
        // Fallback to mock data
        setStudents(mockData.students);
        setFaculty(mockData.faculty);
        setCourses(mockData.courses);
        setEnrollments(mockData.enrollments);
        setGrades(mockData.grades);
        setAttendance(mockData.attendance);
        setFees(mockData.fees);
        setBooks(mockData.books);
        setBorrowedBooks(mockData.borrowedBooks);
        setExams(mockData.exams);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    if (!loading) {
      localStorage.setItem('ums_students', JSON.stringify(students));
    }
  }, [students, loading]);

  useEffect(() => {
    if (!loading) {
      localStorage.setItem('ums_faculty', JSON.stringify(faculty));
    }
  }, [faculty, loading]);

  useEffect(() => {
    if (!loading) {
      localStorage.setItem('ums_courses', JSON.stringify(courses));
    }
  }, [courses, loading]);

  useEffect(() => {
    if (!loading) {
      localStorage.setItem('ums_enrollments', JSON.stringify(enrollments));
    }
  }, [enrollments, loading]);

  useEffect(() => {
    if (!loading) {
      localStorage.setItem('ums_grades', JSON.stringify(grades));
    }
  }, [grades, loading]);

  useEffect(() => {
    if (!loading) {
      localStorage.setItem('ums_attendance', JSON.stringify(attendance));
    }
  }, [attendance, loading]);

  useEffect(() => {
    if (!loading) {
      localStorage.setItem('ums_fees', JSON.stringify(fees));
    }
  }, [fees, loading]);

  useEffect(() => {
    if (!loading) {
      localStorage.setItem('ums_books', JSON.stringify(books));
    }
  }, [books, loading]);

  useEffect(() => {
    if (!loading) {
      localStorage.setItem('ums_borrowed_books', JSON.stringify(borrowedBooks));
    }
  }, [borrowedBooks, loading]);

  useEffect(() => {
    if (!loading) {
      localStorage.setItem('ums_exams', JSON.stringify(exams));
    }
  }, [exams, loading]);

  const value = {
    students,
    setStudents,
    faculty,
    setFaculty,
    courses,
    setCourses,
    enrollments,
    setEnrollments,
    grades,
    setGrades,
    attendance,
    setAttendance,
    fees,
    setFees,
    books,
    setBooks,
    borrowedBooks,
    setBorrowedBooks,
    exams,
    setExams,
    loading,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};
