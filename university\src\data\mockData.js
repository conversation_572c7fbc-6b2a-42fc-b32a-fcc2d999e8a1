export const mockData = {
  students: [
    {
      id: 'STU001',
      name: '<PERSON>',
      email: '<EMAIL>',
      password: 'password123',
      role: 'student',
      phone: '+1234567890',
      address: '123 Main St, City, State',
      dateOfBirth: '2000-05-15',
      enrollmentDate: '2022-09-01',
      semester: 3,
      department: 'Computer Science',
      cgpa: 3.75,
      status: 'active',
      profileImage: null,
    },
    {
      id: 'STU002',
      name: '<PERSON>',
      email: '<EMAIL>',
      password: 'password123',
      role: 'student',
      phone: '+1234567891',
      address: '456 Oak Ave, City, State',
      dateOfBirth: '2001-03-22',
      enrollmentDate: '2022-09-01',
      semester: 3,
      department: 'Business Administration',
      cgpa: 3.92,
      status: 'active',
      profileImage: null,
    },
    {
      id: 'STU003',
      name: '<PERSON>',
      email: '<EMAIL>',
      password: 'password123',
      role: 'student',
      phone: '+1234567892',
      address: '789 Pine St, City, State',
      dateOfBirth: '1999-11-08',
      enrollmentDate: '2021-09-01',
      semester: 5,
      department: 'Engineering',
      cgpa: 3.45,
      status: 'active',
      profileImage: null,
    },
  ],

  faculty: [
    {
      id: 'FAC001',
      name: 'Dr. Sarah Wilson',
      email: '<EMAIL>',
      password: 'password123',
      role: 'faculty',
      phone: '+1234567893',
      address: '321 University Blvd, City, State',
      department: 'Computer Science',
      designation: 'Professor',
      qualification: 'Ph.D. in Computer Science',
      experience: 15,
      specialization: 'Machine Learning, Data Science',
      joinDate: '2010-08-15',
      salary: 85000,
      status: 'active',
      profileImage: null,
    },
    {
      id: 'FAC002',
      name: 'Dr. Robert Brown',
      email: '<EMAIL>',
      password: 'password123',
      role: 'faculty',
      phone: '+1234567894',
      address: '654 Academic Way, City, State',
      department: 'Business Administration',
      designation: 'Associate Professor',
      qualification: 'Ph.D. in Business Administration',
      experience: 12,
      specialization: 'Marketing, Strategic Management',
      joinDate: '2012-01-10',
      salary: 75000,
      status: 'active',
      profileImage: null,
    },
    {
      id: 'FAC003',
      name: 'Dr. Emily Davis',
      email: '<EMAIL>',
      password: 'password123',
      role: 'faculty',
      phone: '+1234567895',
      address: '987 Scholar Lane, City, State',
      department: 'Engineering',
      designation: 'Assistant Professor',
      qualification: 'Ph.D. in Mechanical Engineering',
      experience: 8,
      specialization: 'Thermodynamics, Fluid Mechanics',
      joinDate: '2016-09-01',
      salary: 65000,
      status: 'active',
      profileImage: null,
    },
  ],

  courses: [
    {
      id: 'CS101',
      name: 'Introduction to Programming',
      code: 'CS101',
      department: 'Computer Science',
      credits: 3,
      semester: 1,
      description: 'Basic programming concepts using Python',
      facultyId: 'FAC001',
      schedule: {
        days: ['Monday', 'Wednesday', 'Friday'],
        time: '09:00-10:00',
        room: 'CS-101',
      },
      capacity: 50,
      enrolled: 35,
      status: 'active',
    },
    {
      id: 'CS201',
      name: 'Data Structures',
      code: 'CS201',
      department: 'Computer Science',
      credits: 4,
      semester: 3,
      description: 'Advanced data structures and algorithms',
      facultyId: 'FAC001',
      schedule: {
        days: ['Tuesday', 'Thursday'],
        time: '10:00-12:00',
        room: 'CS-201',
      },
      capacity: 40,
      enrolled: 28,
      status: 'active',
    },
    {
      id: 'BUS101',
      name: 'Business Fundamentals',
      code: 'BUS101',
      department: 'Business Administration',
      credits: 3,
      semester: 1,
      description: 'Introduction to business concepts',
      facultyId: 'FAC002',
      schedule: {
        days: ['Monday', 'Wednesday'],
        time: '14:00-15:30',
        room: 'BUS-101',
      },
      capacity: 60,
      enrolled: 45,
      status: 'active',
    },
    {
      id: 'ENG101',
      name: 'Engineering Mathematics',
      code: 'ENG101',
      department: 'Engineering',
      credits: 4,
      semester: 1,
      description: 'Mathematical foundations for engineering',
      facultyId: 'FAC003',
      schedule: {
        days: ['Tuesday', 'Thursday', 'Friday'],
        time: '08:00-09:00',
        room: 'ENG-101',
      },
      capacity: 45,
      enrolled: 42,
      status: 'active',
    },
  ],

  enrollments: [
    {
      id: 'ENR001',
      studentId: 'STU001',
      courseId: 'CS101',
      enrollmentDate: '2022-09-01',
      status: 'enrolled',
      grade: null,
    },
    {
      id: 'ENR002',
      studentId: 'STU001',
      courseId: 'CS201',
      enrollmentDate: '2023-01-15',
      status: 'enrolled',
      grade: null,
    },
    {
      id: 'ENR003',
      studentId: 'STU002',
      courseId: 'BUS101',
      enrollmentDate: '2022-09-01',
      status: 'enrolled',
      grade: null,
    },
    {
      id: 'ENR004',
      studentId: 'STU003',
      courseId: 'ENG101',
      enrollmentDate: '2021-09-01',
      status: 'completed',
      grade: 'B+',
    },
  ],

  grades: [
    // Student 1 (John Doe) - CS Student
    {
      id: 'GRD001',
      studentId: 'STU001',
      courseId: 'CS101',
      semester: 1,
      year: 2022,
      midterm: 85,
      final: 88,
      assignments: 92,
      quizzes: 90,
      participation: 95,
      total: 88.5,
      grade: 'A-',
      gpa: 3.7,
      status: 'completed',
    },
    {
      id: 'GRD002',
      studentId: 'STU001',
      courseId: 'CS201',
      semester: 3,
      year: 2023,
      midterm: 92,
      final: 89,
      assignments: 95,
      quizzes: 88,
      participation: 92,
      total: 91.2,
      grade: 'A',
      gpa: 4.0,
      status: 'completed',
    },
    {
      id: 'GRD003',
      studentId: 'STU001',
      courseId: 'ENG101',
      semester: 2,
      year: 2023,
      midterm: 78,
      final: 82,
      assignments: 85,
      quizzes: 80,
      participation: 88,
      total: 81.5,
      grade: 'B+',
      gpa: 3.3,
      status: 'completed',
    },

    // Student 2 (Jane Smith) - Business Student
    {
      id: 'GRD004',
      studentId: 'STU002',
      courseId: 'BUS101',
      semester: 1,
      year: 2022,
      midterm: 92,
      final: 95,
      assignments: 89,
      quizzes: 94,
      participation: 96,
      total: 92.3,
      grade: 'A',
      gpa: 4.0,
      status: 'completed',
    },
    {
      id: 'GRD005',
      studentId: 'STU002',
      courseId: 'CS101',
      semester: 2,
      year: 2023,
      midterm: 88,
      final: 85,
      assignments: 90,
      quizzes: 87,
      participation: 89,
      total: 87.8,
      grade: 'A-',
      gpa: 3.7,
      status: 'completed',
    },
    {
      id: 'GRD006',
      studentId: 'STU002',
      courseId: 'ENG101',
      semester: 3,
      year: 2023,
      midterm: 95,
      final: 93,
      assignments: 97,
      quizzes: 92,
      participation: 98,
      total: 94.5,
      grade: 'A',
      gpa: 4.0,
      status: 'completed',
    },

    // Student 3 (Mike Johnson) - Engineering Student
    {
      id: 'GRD007',
      studentId: 'STU003',
      courseId: 'ENG101',
      semester: 1,
      year: 2021,
      midterm: 78,
      final: 82,
      assignments: 85,
      quizzes: 75,
      participation: 80,
      total: 81.5,
      grade: 'B+',
      gpa: 3.3,
      status: 'completed',
    },
    {
      id: 'GRD008',
      studentId: 'STU003',
      courseId: 'CS101',
      semester: 2,
      year: 2022,
      midterm: 72,
      final: 75,
      assignments: 78,
      quizzes: 70,
      participation: 85,
      total: 74.8,
      grade: 'B',
      gpa: 3.0,
      status: 'completed',
    },
    {
      id: 'GRD009',
      studentId: 'STU003',
      courseId: 'CS201',
      semester: 5,
      year: 2024,
      midterm: 85,
      final: null,
      assignments: 88,
      quizzes: 82,
      participation: 90,
      total: null,
      grade: null,
      gpa: null,
      status: 'in_progress',
    },
  ],

  attendance: [
    // CS101 Attendance - January 2024
    { id: 'ATT001', studentId: 'STU001', courseId: 'CS101', date: '2024-01-08', status: 'present', time: '09:00' },
    { id: 'ATT002', studentId: 'STU001', courseId: 'CS101', date: '2024-01-10', status: 'present', time: '09:00' },
    { id: 'ATT003', studentId: 'STU001', courseId: 'CS101', date: '2024-01-12', status: 'present', time: '09:00' },
    { id: 'ATT004', studentId: 'STU001', courseId: 'CS101', date: '2024-01-15', status: 'present', time: '09:00' },
    { id: 'ATT005', studentId: 'STU001', courseId: 'CS101', date: '2024-01-17', status: 'absent', time: '09:00' },
    { id: 'ATT006', studentId: 'STU001', courseId: 'CS101', date: '2024-01-19', status: 'present', time: '09:00' },
    { id: 'ATT007', studentId: 'STU001', courseId: 'CS101', date: '2024-01-22', status: 'present', time: '09:00' },
    { id: 'ATT008', studentId: 'STU001', courseId: 'CS101', date: '2024-01-24', status: 'late', time: '09:15' },
    { id: 'ATT009', studentId: 'STU001', courseId: 'CS101', date: '2024-01-26', status: 'present', time: '09:00' },
    { id: 'ATT010', studentId: 'STU001', courseId: 'CS101', date: '2024-01-29', status: 'present', time: '09:00' },

    // CS201 Attendance for STU001
    { id: 'ATT011', studentId: 'STU001', courseId: 'CS201', date: '2024-01-09', status: 'present', time: '10:00' },
    { id: 'ATT012', studentId: 'STU001', courseId: 'CS201', date: '2024-01-11', status: 'present', time: '10:00' },
    { id: 'ATT013', studentId: 'STU001', courseId: 'CS201', date: '2024-01-16', status: 'present', time: '10:00' },
    { id: 'ATT014', studentId: 'STU001', courseId: 'CS201', date: '2024-01-18', status: 'present', time: '10:00' },
    { id: 'ATT015', studentId: 'STU001', courseId: 'CS201', date: '2024-01-23', status: 'absent', time: '10:00' },
    { id: 'ATT016', studentId: 'STU001', courseId: 'CS201', date: '2024-01-25', status: 'present', time: '10:00' },
    { id: 'ATT017', studentId: 'STU001', courseId: 'CS201', date: '2024-01-30', status: 'present', time: '10:00' },

    // BUS101 Attendance for STU002
    { id: 'ATT018', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-08', status: 'present', time: '14:00' },
    { id: 'ATT019', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-10', status: 'present', time: '14:00' },
    { id: 'ATT020', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-15', status: 'present', time: '14:00' },
    { id: 'ATT021', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-17', status: 'present', time: '14:00' },
    { id: 'ATT022', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-22', status: 'present', time: '14:00' },
    { id: 'ATT023', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-24', status: 'present', time: '14:00' },
    { id: 'ATT024', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-29', status: 'present', time: '14:00' },
    { id: 'ATT025', studentId: 'STU002', courseId: 'BUS101', date: '2024-01-31', status: 'present', time: '14:00' },

    // ENG101 Attendance for STU003
    { id: 'ATT026', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-09', status: 'present', time: '08:00' },
    { id: 'ATT027', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-11', status: 'late', time: '08:20' },
    { id: 'ATT028', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-12', status: 'present', time: '08:00' },
    { id: 'ATT029', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-16', status: 'absent', time: '08:00' },
    { id: 'ATT030', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-18', status: 'present', time: '08:00' },
    { id: 'ATT031', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-19', status: 'present', time: '08:00' },
    { id: 'ATT032', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-23', status: 'absent', time: '08:00' },
    { id: 'ATT033', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-25', status: 'present', time: '08:00' },
    { id: 'ATT034', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-26', status: 'late', time: '08:15' },
    { id: 'ATT035', studentId: 'STU003', courseId: 'ENG101', date: '2024-01-30', status: 'present', time: '08:00' },

    // Cross-enrollment attendance
    { id: 'ATT036', studentId: 'STU002', courseId: 'CS101', date: '2024-01-08', status: 'present', time: '09:00' },
    { id: 'ATT037', studentId: 'STU002', courseId: 'CS101', date: '2024-01-10', status: 'present', time: '09:00' },
    { id: 'ATT038', studentId: 'STU002', courseId: 'CS101', date: '2024-01-12', status: 'absent', time: '09:00' },
    { id: 'ATT039', studentId: 'STU002', courseId: 'CS101', date: '2024-01-15', status: 'present', time: '09:00' },
    { id: 'ATT040', studentId: 'STU002', courseId: 'CS101', date: '2024-01-17', status: 'present', time: '09:00' },
  ],

  fees: [
    // Student 1 (John Doe) - CS Student Fee Records
    {
      id: 'FEE001',
      studentId: 'STU001',
      semester: 1,
      year: 2022,
      tuitionFee: 4500,
      libraryFee: 150,
      labFee: 250,
      sportsFee: 100,
      activityFee: 75,
      technologyFee: 200,
      otherFees: 50,
      totalAmount: 5325,
      paidAmount: 5325,
      dueAmount: 0,
      dueDate: '2022-08-31',
      paymentDate: '2022-08-20',
      status: 'paid',
      paymentMethod: 'bank_transfer',
      transactionId: 'TXN001',
    },
    {
      id: 'FEE002',
      studentId: 'STU001',
      semester: 2,
      year: 2023,
      tuitionFee: 4800,
      libraryFee: 150,
      labFee: 300,
      sportsFee: 100,
      activityFee: 75,
      technologyFee: 200,
      otherFees: 50,
      totalAmount: 5675,
      paidAmount: 5675,
      dueAmount: 0,
      dueDate: '2023-01-31',
      paymentDate: '2023-01-25',
      status: 'paid',
      paymentMethod: 'credit_card',
      transactionId: 'TXN002',
    },
    {
      id: 'FEE003',
      studentId: 'STU001',
      semester: 3,
      year: 2024,
      tuitionFee: 5000,
      libraryFee: 200,
      labFee: 300,
      sportsFee: 100,
      activityFee: 75,
      technologyFee: 250,
      otherFees: 75,
      totalAmount: 6000,
      paidAmount: 6000,
      dueAmount: 0,
      dueDate: '2024-01-31',
      paymentDate: '2024-01-15',
      status: 'paid',
      paymentMethod: 'bank_transfer',
      transactionId: 'TXN003',
    },

    // Student 2 (Jane Smith) - Business Student Fee Records
    {
      id: 'FEE004',
      studentId: 'STU002',
      semester: 1,
      year: 2022,
      tuitionFee: 4200,
      libraryFee: 150,
      labFee: 200,
      sportsFee: 100,
      activityFee: 75,
      technologyFee: 150,
      otherFees: 50,
      totalAmount: 4925,
      paidAmount: 4925,
      dueAmount: 0,
      dueDate: '2022-08-31',
      paymentDate: '2022-08-28',
      status: 'paid',
      paymentMethod: 'bank_transfer',
      transactionId: 'TXN004',
    },
    {
      id: 'FEE005',
      studentId: 'STU002',
      semester: 2,
      year: 2023,
      tuitionFee: 4400,
      libraryFee: 150,
      labFee: 200,
      sportsFee: 100,
      activityFee: 75,
      technologyFee: 175,
      otherFees: 50,
      totalAmount: 5150,
      paidAmount: 5150,
      dueAmount: 0,
      dueDate: '2023-01-31',
      paymentDate: '2023-01-20',
      status: 'paid',
      paymentMethod: 'debit_card',
      transactionId: 'TXN005',
    },
    {
      id: 'FEE006',
      studentId: 'STU002',
      semester: 3,
      year: 2024,
      tuitionFee: 4800,
      libraryFee: 200,
      labFee: 250,
      sportsFee: 100,
      activityFee: 75,
      technologyFee: 200,
      otherFees: 75,
      totalAmount: 5700,
      paidAmount: 2000,
      dueAmount: 3700,
      dueDate: '2024-02-15',
      paymentDate: '2024-01-10',
      status: 'partial',
      paymentMethod: 'bank_transfer',
      transactionId: 'TXN006',
    },

    // Student 3 (Mike Johnson) - Engineering Student Fee Records
    {
      id: 'FEE007',
      studentId: 'STU003',
      semester: 1,
      year: 2021,
      tuitionFee: 5200,
      libraryFee: 150,
      labFee: 400,
      sportsFee: 100,
      activityFee: 75,
      technologyFee: 300,
      otherFees: 75,
      totalAmount: 6300,
      paidAmount: 6300,
      dueAmount: 0,
      dueDate: '2021-08-31',
      paymentDate: '2021-08-25',
      status: 'paid',
      paymentMethod: 'bank_transfer',
      transactionId: 'TXN007',
    },
    {
      id: 'FEE008',
      studentId: 'STU003',
      semester: 4,
      year: 2023,
      tuitionFee: 5500,
      libraryFee: 200,
      labFee: 450,
      sportsFee: 100,
      activityFee: 75,
      technologyFee: 350,
      otherFees: 75,
      totalAmount: 6750,
      paidAmount: 4000,
      dueAmount: 2750,
      dueDate: '2024-02-28',
      paymentDate: '2023-12-15',
      status: 'partial',
      paymentMethod: 'installment',
      transactionId: 'TXN008',
    },
    {
      id: 'FEE009',
      studentId: 'STU003',
      semester: 5,
      year: 2024,
      tuitionFee: 5800,
      libraryFee: 200,
      labFee: 500,
      sportsFee: 100,
      activityFee: 75,
      technologyFee: 400,
      otherFees: 100,
      totalAmount: 7175,
      paidAmount: 0,
      dueAmount: 7175,
      dueDate: '2024-03-15',
      paymentDate: null,
      status: 'unpaid',
      paymentMethod: null,
      transactionId: null,
    },
  ],

  books: [
    {
      id: 'BOOK001',
      title: 'Introduction to Algorithms',
      author: 'Thomas H. Cormen',
      isbn: '978-0262033848',
      category: 'Computer Science',
      publisher: 'MIT Press',
      edition: '3rd',
      totalCopies: 10,
      availableCopies: 7,
      location: 'CS Section - Shelf A1',
      status: 'available',
    },
    {
      id: 'BOOK002',
      title: 'Clean Code',
      author: 'Robert C. Martin',
      isbn: '978-0132350884',
      category: 'Computer Science',
      publisher: 'Prentice Hall',
      edition: '1st',
      totalCopies: 8,
      availableCopies: 5,
      location: 'CS Section - Shelf A2',
      status: 'available',
    },
    {
      id: 'BOOK003',
      title: 'Marketing Management',
      author: 'Philip Kotler',
      isbn: '978-0134236933',
      category: 'Business',
      publisher: 'Pearson',
      edition: '15th',
      totalCopies: 12,
      availableCopies: 9,
      location: 'Business Section - Shelf B1',
      status: 'available',
    },
    {
      id: 'BOOK004',
      title: 'Data Structures and Algorithms in Java',
      author: 'Robert Lafore',
      isbn: '978-**********',
      category: 'Computer Science',
      publisher: 'Sams Publishing',
      edition: '2nd',
      totalCopies: 15,
      availableCopies: 12,
      location: 'CS Section - Shelf A3',
      status: 'available',
    },
    {
      id: 'BOOK005',
      title: 'Financial Accounting',
      author: 'Jerry J. Weygandt',
      isbn: '978-**********',
      category: 'Business',
      publisher: 'Wiley',
      edition: '10th',
      totalCopies: 10,
      availableCopies: 6,
      location: 'Business Section - Shelf B2',
      status: 'available',
    },
    {
      id: 'BOOK006',
      title: 'English Literature: A Survey',
      author: 'William J. Long',
      isbn: '978-**********',
      category: 'Literature',
      publisher: 'Kalyani Publishers',
      edition: '1st',
      totalCopies: 8,
      availableCopies: 5,
      location: 'Literature Section - Shelf C1',
      status: 'available',
    },
    {
      id: 'BOOK007',
      title: 'Engineering Mathematics',
      author: 'K.A. Stroud',
      isbn: '978-**********',
      category: 'Engineering',
      publisher: 'Palgrave Macmillan',
      edition: '7th',
      totalCopies: 20,
      availableCopies: 16,
      location: 'Engineering Section - Shelf D1',
      status: 'available',
    },
    {
      id: 'BOOK008',
      title: 'Physics for Scientists and Engineers',
      author: 'Raymond A. Serway',
      isbn: '978-1133947271',
      category: 'Science',
      publisher: 'Cengage Learning',
      edition: '9th',
      totalCopies: 18,
      availableCopies: 14,
      location: 'Science Section - Shelf E1',
      status: 'available',
    },
    {
      id: 'BOOK009',
      title: 'Organic Chemistry',
      author: 'Paula Yurkanis Bruice',
      isbn: '978-0134042282',
      category: 'Science',
      publisher: 'Pearson',
      edition: '8th',
      totalCopies: 12,
      availableCopies: 8,
      location: 'Science Section - Shelf E2',
      status: 'available',
    },
    {
      id: 'BOOK010',
      title: 'Database System Concepts',
      author: 'Abraham Silberschatz',
      isbn: '978-0078022159',
      category: 'Computer Science',
      publisher: 'McGraw-Hill Education',
      edition: '7th',
      totalCopies: 14,
      availableCopies: 10,
      location: 'CS Section - Shelf A4',
      status: 'available',
    },
    {
      id: 'BOOK011',
      title: 'Principles of Economics',
      author: 'N. Gregory Mankiw',
      isbn: '978-1305585126',
      category: 'Business',
      publisher: 'Cengage Learning',
      edition: '8th',
      totalCopies: 16,
      availableCopies: 13,
      location: 'Business Section - Shelf B3',
      status: 'available',
    },
    {
      id: 'BOOK012',
      title: 'Creative Writing: Four Genres in Brief',
      author: 'David Starkey',
      isbn: '978-1319035334',
      category: 'Literature',
      publisher: 'Bedford/St. Martins',
      edition: '3rd',
      totalCopies: 6,
      availableCopies: 4,
      location: 'Literature Section - Shelf C2',
      status: 'available',
    },
  ],

  borrowedBooks: [
    // Currently Borrowed Books
    {
      id: 'BOR001',
      studentId: 'STU001',
      bookId: 'BOOK001',
      borrowDate: '2024-01-10',
      dueDate: '2024-02-10',
      returnDate: null,
      status: 'borrowed',
      fine: 0,
      renewalCount: 0,
      maxRenewals: 2,
    },
    {
      id: 'BOR002',
      studentId: 'STU001',
      bookId: 'BOOK004',
      borrowDate: '2024-01-15',
      dueDate: '2024-02-15',
      returnDate: null,
      status: 'borrowed',
      fine: 0,
      renewalCount: 1,
      maxRenewals: 2,
    },
    {
      id: 'BOR003',
      studentId: 'STU002',
      bookId: 'BOOK002',
      borrowDate: '2024-01-05',
      dueDate: '2024-02-05',
      returnDate: null,
      status: 'overdue',
      fine: 15,
      renewalCount: 0,
      maxRenewals: 2,
    },
    {
      id: 'BOR004',
      studentId: 'STU002',
      bookId: 'BOOK005',
      borrowDate: '2024-01-20',
      dueDate: '2024-02-20',
      returnDate: null,
      status: 'borrowed',
      fine: 0,
      renewalCount: 0,
      maxRenewals: 2,
    },
    {
      id: 'BOR005',
      studentId: 'STU003',
      bookId: 'BOOK006',
      borrowDate: '2024-01-12',
      dueDate: '2024-02-12',
      returnDate: null,
      status: 'borrowed',
      fine: 0,
      renewalCount: 0,
      maxRenewals: 2,
    },
    {
      id: 'BOR006',
      studentId: 'STU003',
      bookId: 'BOOK007',
      borrowDate: '2024-01-08',
      dueDate: '2024-02-08',
      returnDate: null,
      status: 'overdue',
      fine: 25,
      renewalCount: 2,
      maxRenewals: 2,
    },

    // Previously Returned Books
    {
      id: 'BOR007',
      studentId: 'STU001',
      bookId: 'BOOK003',
      borrowDate: '2023-12-01',
      dueDate: '2024-01-01',
      returnDate: '2023-12-28',
      status: 'returned',
      fine: 0,
      renewalCount: 0,
      maxRenewals: 2,
    },
    {
      id: 'BOR008',
      studentId: 'STU002',
      bookId: 'BOOK008',
      borrowDate: '2023-11-15',
      dueDate: '2023-12-15',
      returnDate: '2023-12-20',
      status: 'returned',
      fine: 10,
      renewalCount: 1,
      maxRenewals: 2,
    },
    {
      id: 'BOR009',
      studentId: 'STU003',
      bookId: 'BOOK009',
      borrowDate: '2023-10-20',
      dueDate: '2023-11-20',
      returnDate: '2023-11-18',
      status: 'returned',
      fine: 0,
      renewalCount: 0,
      maxRenewals: 2,
    },
  ],

  exams: [
    // Upcoming Exams
    {
      id: 'EXM001',
      courseId: 'CS101',
      name: 'Midterm Examination',
      type: 'midterm',
      date: '2024-02-15',
      time: '09:00-12:00',
      room: 'Exam Hall 1',
      duration: 180,
      totalMarks: 100,
      status: 'scheduled',
      instructions: 'Bring calculator and ID card. No mobile phones allowed.',
      syllabus: 'Chapters 1-5: Programming Fundamentals, Data Types, Control Structures',
    },
    {
      id: 'EXM002',
      courseId: 'CS101',
      name: 'Final Examination',
      type: 'final',
      date: '2024-05-20',
      time: '09:00-12:00',
      room: 'Exam Hall 1',
      duration: 180,
      totalMarks: 150,
      status: 'scheduled',
      instructions: 'Comprehensive exam covering all course material. Bring calculator and ID card.',
      syllabus: 'All chapters: Programming, Data Structures, Algorithms, Object-Oriented Programming',
    },
    {
      id: 'EXM003',
      courseId: 'CS201',
      name: 'Quiz 1',
      type: 'quiz',
      date: '2024-02-08',
      time: '10:00-10:30',
      room: 'Room 201',
      duration: 30,
      totalMarks: 25,
      status: 'scheduled',
      instructions: 'Short quiz on recent topics. No materials allowed.',
      syllabus: 'Advanced Data Structures: Trees and Graphs',
    },
    {
      id: 'EXM004',
      courseId: 'CS201',
      name: 'Midterm Examination',
      type: 'midterm',
      date: '2024-03-10',
      time: '14:00-17:00',
      room: 'Exam Hall 2',
      duration: 180,
      totalMarks: 100,
      status: 'scheduled',
      instructions: 'Bring calculator and ID card. Programming questions included.',
      syllabus: 'Chapters 1-6: Advanced Data Structures, Algorithm Analysis, Sorting',
    },
    {
      id: 'EXM005',
      courseId: 'BUS101',
      name: 'Midterm Examination',
      type: 'midterm',
      date: '2024-02-20',
      time: '14:00-16:30',
      room: 'Business Hall A',
      duration: 150,
      totalMarks: 100,
      status: 'scheduled',
      instructions: 'Case study analysis. Bring calculator.',
      syllabus: 'Business Fundamentals, Management Principles, Organizational Behavior',
    },
    {
      id: 'EXM006',
      courseId: 'BUS101',
      name: 'Final Examination',
      type: 'final',
      date: '2024-05-25',
      time: '14:00-17:00',
      room: 'Business Hall A',
      duration: 180,
      totalMarks: 150,
      status: 'scheduled',
      instructions: 'Comprehensive business exam with case studies.',
      syllabus: 'All course material: Management, Marketing, Finance, Operations',
    },
    {
      id: 'EXM007',
      courseId: 'ENG101',
      name: 'Essay Examination',
      type: 'midterm',
      date: '2024-02-25',
      time: '08:00-11:00',
      room: 'Literature Hall',
      duration: 180,
      totalMarks: 100,
      status: 'scheduled',
      instructions: 'Essay writing exam. Bring pen and paper only.',
      syllabus: 'Literary Analysis, Essay Writing, Grammar and Composition',
    },
    {
      id: 'EXM008',
      courseId: 'ENG101',
      name: 'Final Examination',
      type: 'final',
      date: '2024-05-30',
      time: '08:00-11:00',
      room: 'Literature Hall',
      duration: 180,
      totalMarks: 150,
      status: 'scheduled',
      instructions: 'Comprehensive literature and writing exam.',
      syllabus: 'Complete course: Literature, Writing, Critical Analysis, Research Methods',
    },

    // Current/Ongoing Exams
    {
      id: 'EXM009',
      courseId: 'CS101',
      name: 'Assignment 1 Submission',
      type: 'assignment',
      date: '2024-02-05',
      time: '23:59',
      room: 'Online Submission',
      duration: 0,
      totalMarks: 50,
      status: 'ongoing',
      instructions: 'Submit programming assignment via online portal.',
      syllabus: 'Basic Programming Concepts and Problem Solving',
    },

    // Completed Exams
    {
      id: 'EXM010',
      courseId: 'CS101',
      name: 'Quiz 1',
      type: 'quiz',
      date: '2024-01-20',
      time: '09:00-09:30',
      room: 'Room 101',
      duration: 30,
      totalMarks: 25,
      status: 'completed',
      instructions: 'Basic programming concepts quiz.',
      syllabus: 'Introduction to Programming, Variables, Basic Operations',
    },
    {
      id: 'EXM011',
      courseId: 'BUS101',
      name: 'Quiz 1',
      type: 'quiz',
      date: '2024-01-25',
      time: '14:00-14:30',
      room: 'Business Room 1',
      duration: 30,
      totalMarks: 25,
      status: 'completed',
      instructions: 'Business fundamentals quiz.',
      syllabus: 'Introduction to Business, Basic Management Concepts',
    },
    {
      id: 'EXM012',
      courseId: 'ENG101',
      name: 'Essay Assignment 1',
      type: 'assignment',
      date: '2024-01-30',
      time: '23:59',
      room: 'Online Submission',
      duration: 0,
      totalMarks: 40,
      status: 'completed',
      instructions: 'Submit 1000-word essay on assigned topic.',
      syllabus: 'Essay Structure, Thesis Development, Academic Writing',
    },
  ],

  // Admin user
  admin: {
    id: 'ADM001',
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    phone: '+1234567896',
    address: 'University Administration Building',
    status: 'active',
  },
};
