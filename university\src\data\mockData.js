export const mockData = {
  students: [
    {
      id: 'STU001',
      name: '<PERSON>',
      email: '<EMAIL>',
      password: 'password123',
      role: 'student',
      phone: '+1234567890',
      address: '123 Main St, City, State',
      dateOfBirth: '2000-05-15',
      enrollmentDate: '2022-09-01',
      semester: 3,
      department: 'Computer Science',
      cgpa: 3.75,
      status: 'active',
      profileImage: null,
    },
    {
      id: 'STU002',
      name: '<PERSON>',
      email: '<EMAIL>',
      password: 'password123',
      role: 'student',
      phone: '+1234567891',
      address: '456 Oak Ave, City, State',
      dateOfBirth: '2001-03-22',
      enrollmentDate: '2022-09-01',
      semester: 3,
      department: 'Business Administration',
      cgpa: 3.92,
      status: 'active',
      profileImage: null,
    },
    {
      id: 'STU003',
      name: '<PERSON>',
      email: '<EMAIL>',
      password: 'password123',
      role: 'student',
      phone: '+1234567892',
      address: '789 Pine St, City, State',
      dateOfBirth: '1999-11-08',
      enrollmentDate: '2021-09-01',
      semester: 5,
      department: 'Engineering',
      cgpa: 3.45,
      status: 'active',
      profileImage: null,
    },
  ],

  faculty: [
    {
      id: 'FAC001',
      name: 'Dr. Sarah Wilson',
      email: '<EMAIL>',
      password: 'password123',
      role: 'faculty',
      phone: '+1234567893',
      address: '321 University Blvd, City, State',
      department: 'Computer Science',
      designation: 'Professor',
      qualification: 'Ph.D. in Computer Science',
      experience: 15,
      specialization: 'Machine Learning, Data Science',
      joinDate: '2010-08-15',
      salary: 85000,
      status: 'active',
      profileImage: null,
    },
    {
      id: 'FAC002',
      name: 'Dr. Robert Brown',
      email: '<EMAIL>',
      password: 'password123',
      role: 'faculty',
      phone: '+1234567894',
      address: '654 Academic Way, City, State',
      department: 'Business Administration',
      designation: 'Associate Professor',
      qualification: 'Ph.D. in Business Administration',
      experience: 12,
      specialization: 'Marketing, Strategic Management',
      joinDate: '2012-01-10',
      salary: 75000,
      status: 'active',
      profileImage: null,
    },
    {
      id: 'FAC003',
      name: 'Dr. Emily Davis',
      email: '<EMAIL>',
      password: 'password123',
      role: 'faculty',
      phone: '+1234567895',
      address: '987 Scholar Lane, City, State',
      department: 'Engineering',
      designation: 'Assistant Professor',
      qualification: 'Ph.D. in Mechanical Engineering',
      experience: 8,
      specialization: 'Thermodynamics, Fluid Mechanics',
      joinDate: '2016-09-01',
      salary: 65000,
      status: 'active',
      profileImage: null,
    },
  ],

  courses: [
    {
      id: 'CS101',
      name: 'Introduction to Programming',
      code: 'CS101',
      department: 'Computer Science',
      credits: 3,
      semester: 1,
      description: 'Basic programming concepts using Python',
      facultyId: 'FAC001',
      schedule: {
        days: ['Monday', 'Wednesday', 'Friday'],
        time: '09:00-10:00',
        room: 'CS-101',
      },
      capacity: 50,
      enrolled: 35,
      status: 'active',
    },
    {
      id: 'CS201',
      name: 'Data Structures',
      code: 'CS201',
      department: 'Computer Science',
      credits: 4,
      semester: 3,
      description: 'Advanced data structures and algorithms',
      facultyId: 'FAC001',
      schedule: {
        days: ['Tuesday', 'Thursday'],
        time: '10:00-12:00',
        room: 'CS-201',
      },
      capacity: 40,
      enrolled: 28,
      status: 'active',
    },
    {
      id: 'BUS101',
      name: 'Business Fundamentals',
      code: 'BUS101',
      department: 'Business Administration',
      credits: 3,
      semester: 1,
      description: 'Introduction to business concepts',
      facultyId: 'FAC002',
      schedule: {
        days: ['Monday', 'Wednesday'],
        time: '14:00-15:30',
        room: 'BUS-101',
      },
      capacity: 60,
      enrolled: 45,
      status: 'active',
    },
    {
      id: 'ENG101',
      name: 'Engineering Mathematics',
      code: 'ENG101',
      department: 'Engineering',
      credits: 4,
      semester: 1,
      description: 'Mathematical foundations for engineering',
      facultyId: 'FAC003',
      schedule: {
        days: ['Tuesday', 'Thursday', 'Friday'],
        time: '08:00-09:00',
        room: 'ENG-101',
      },
      capacity: 45,
      enrolled: 42,
      status: 'active',
    },
  ],

  enrollments: [
    {
      id: 'ENR001',
      studentId: 'STU001',
      courseId: 'CS101',
      enrollmentDate: '2022-09-01',
      status: 'enrolled',
      grade: null,
    },
    {
      id: 'ENR002',
      studentId: 'STU001',
      courseId: 'CS201',
      enrollmentDate: '2023-01-15',
      status: 'enrolled',
      grade: null,
    },
    {
      id: 'ENR003',
      studentId: 'STU002',
      courseId: 'BUS101',
      enrollmentDate: '2022-09-01',
      status: 'enrolled',
      grade: null,
    },
    {
      id: 'ENR004',
      studentId: 'STU003',
      courseId: 'ENG101',
      enrollmentDate: '2021-09-01',
      status: 'completed',
      grade: 'B+',
    },
  ],

  grades: [
    {
      id: 'GRD001',
      studentId: 'STU001',
      courseId: 'CS101',
      semester: 1,
      year: 2022,
      midterm: 85,
      final: 88,
      assignments: 92,
      total: 88.5,
      grade: 'A-',
      gpa: 3.7,
    },
    {
      id: 'GRD002',
      studentId: 'STU002',
      courseId: 'BUS101',
      semester: 1,
      year: 2022,
      midterm: 92,
      final: 95,
      assignments: 89,
      total: 92.3,
      grade: 'A',
      gpa: 4.0,
    },
    {
      id: 'GRD003',
      studentId: 'STU003',
      courseId: 'ENG101',
      semester: 1,
      year: 2021,
      midterm: 78,
      final: 82,
      assignments: 85,
      total: 81.5,
      grade: 'B+',
      gpa: 3.3,
    },
  ],

  attendance: [
    {
      id: 'ATT001',
      studentId: 'STU001',
      courseId: 'CS101',
      date: '2024-01-15',
      status: 'present',
    },
    {
      id: 'ATT002',
      studentId: 'STU001',
      courseId: 'CS101',
      date: '2024-01-17',
      status: 'present',
    },
    {
      id: 'ATT003',
      studentId: 'STU001',
      courseId: 'CS101',
      date: '2024-01-19',
      status: 'absent',
    },
    {
      id: 'ATT004',
      studentId: 'STU002',
      courseId: 'BUS101',
      date: '2024-01-15',
      status: 'present',
    },
  ],

  fees: [
    {
      id: 'FEE001',
      studentId: 'STU001',
      semester: 3,
      year: 2024,
      tuitionFee: 5000,
      libraryFee: 200,
      labFee: 300,
      otherFees: 100,
      totalAmount: 5600,
      paidAmount: 5600,
      dueAmount: 0,
      dueDate: '2024-01-31',
      paymentDate: '2024-01-15',
      status: 'paid',
      paymentMethod: 'bank_transfer',
    },
    {
      id: 'FEE002',
      studentId: 'STU002',
      semester: 3,
      year: 2024,
      tuitionFee: 4800,
      libraryFee: 200,
      labFee: 250,
      otherFees: 100,
      totalAmount: 5350,
      paidAmount: 2000,
      dueAmount: 3350,
      dueDate: '2024-01-31',
      paymentDate: null,
      status: 'partial',
      paymentMethod: null,
    },
  ],

  books: [
    {
      id: 'BOOK001',
      title: 'Introduction to Algorithms',
      author: 'Thomas H. Cormen',
      isbn: '978-**********',
      category: 'Computer Science',
      publisher: 'MIT Press',
      edition: '3rd',
      totalCopies: 10,
      availableCopies: 7,
      location: 'CS Section - Shelf A1',
      status: 'available',
    },
    {
      id: 'BOOK002',
      title: 'Clean Code',
      author: 'Robert C. Martin',
      isbn: '978-**********',
      category: 'Computer Science',
      publisher: 'Prentice Hall',
      edition: '1st',
      totalCopies: 8,
      availableCopies: 5,
      location: 'CS Section - Shelf A2',
      status: 'available',
    },
    {
      id: 'BOOK003',
      title: 'Marketing Management',
      author: 'Philip Kotler',
      isbn: '978-0134236933',
      category: 'Business',
      publisher: 'Pearson',
      edition: '15th',
      totalCopies: 12,
      availableCopies: 9,
      location: 'Business Section - Shelf B1',
      status: 'available',
    },
  ],

  borrowedBooks: [
    {
      id: 'BOR001',
      studentId: 'STU001',
      bookId: 'BOOK001',
      borrowDate: '2024-01-10',
      dueDate: '2024-02-10',
      returnDate: null,
      status: 'borrowed',
      fine: 0,
    },
    {
      id: 'BOR002',
      studentId: 'STU002',
      bookId: 'BOOK002',
      borrowDate: '2024-01-05',
      dueDate: '2024-02-05',
      returnDate: null,
      status: 'borrowed',
      fine: 0,
    },
  ],

  exams: [
    {
      id: 'EXM001',
      courseId: 'CS101',
      name: 'Midterm Examination',
      type: 'midterm',
      date: '2024-02-15',
      time: '09:00-12:00',
      room: 'Exam Hall 1',
      duration: 180,
      totalMarks: 100,
      status: 'scheduled',
    },
    {
      id: 'EXM002',
      courseId: 'CS201',
      name: 'Final Examination',
      type: 'final',
      date: '2024-05-20',
      time: '14:00-17:00',
      room: 'Exam Hall 2',
      duration: 180,
      totalMarks: 100,
      status: 'scheduled',
    },
  ],

  // Admin user
  admin: {
    id: 'ADM001',
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    phone: '+1234567896',
    address: 'University Administration Building',
    status: 'active',
  },
};
