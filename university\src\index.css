@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Root variables for theme */
:root {
  --primary-50: #f3f1ff;
  --primary-100: #ebe5ff;
  --primary-200: #d9ceff;
  --primary-300: #bea6ff;
  --primary-400: #9f75ff;
  --primary-500: #843dff;
  --primary-600: #7c3aed;
  --primary-700: #6d28d9;
  --primary-800: #5b21b6;
  --primary-900: #4c1d95;
  --primary-950: #2e1065;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  min-height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Modern scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #6d28d9, #9333ea);
  transform: scale(1.1);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #7c3aed rgba(15, 23, 42, 0.1);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 58, 237, 0.2);
}

/* Modern card styles */
.modern-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(203, 213, 225, 0.3);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.modern-card-dark {
  background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
  border: 1px solid rgba(124, 58, 237, 0.2);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.modern-card-dark:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(124, 58, 237, 0.35);
  border-color: rgba(124, 58, 237, 0.4);
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, #7c3aed 0%, #a855f7 50%, #c084fc 100%);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 14px 0 rgba(124, 58, 237, 0.25);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px 0 rgba(124, 58, 237, 0.35);
  background: linear-gradient(135deg, #6d28d9 0%, #9333ea 50%, #a855f7 100%);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 4px 14px 0 rgba(124, 58, 237, 0.25);
}

/* Input styles */
.modern-input {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(203, 213, 225, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-input:focus {
  border-color: #7c3aed;
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
  background: rgba(255, 255, 255, 1);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #7c3aed 0%, #a855f7 50%, #c084fc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading animation */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(124, 58, 237, 0.1), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Selection styles */
::selection {
  background: rgba(124, 58, 237, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(124, 58, 237, 0.3);
  color: white;
}

/* Focus styles */
.focus-ring {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2);
}

/* Responsive utilities */
@media (max-width: 640px) {
  .modern-card {
    margin: 0.5rem;
  }
}
