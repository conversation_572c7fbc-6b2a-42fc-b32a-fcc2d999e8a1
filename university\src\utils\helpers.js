// Date formatting utilities
export const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export const formatDateTime = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Grade utilities
export const calculateGPA = (grades) => {
  if (!grades || grades.length === 0) return 0;
  
  const gradePoints = {
    'A+': 4.0, 'A': 4.0, 'A-': 3.7,
    'B+': 3.3, 'B': 3.0, 'B-': 2.7,
    'C+': 2.3, 'C': 2.0, 'C-': 1.7,
    'D+': 1.3, 'D': 1.0, 'F': 0.0
  };
  
  const totalPoints = grades.reduce((sum, grade) => sum + (gradePoints[grade.grade] || 0), 0);
  return (totalPoints / grades.length).toFixed(2);
};

export const getGradeColor = (grade) => {
  if (!grade || typeof grade !== 'string') return 'text-gray-400';

  const colors = {
    'A+': 'text-green-400', 'A': 'text-green-400', 'A-': 'text-green-300',
    'B+': 'text-blue-400', 'B': 'text-blue-400', 'B-': 'text-blue-300',
    'C+': 'text-yellow-400', 'C': 'text-yellow-400', 'C-': 'text-yellow-300',
    'D+': 'text-orange-400', 'D': 'text-orange-400', 'F': 'text-red-400'
  };
  return colors[grade] || 'text-gray-400';
};

// Status utilities
export const getStatusColor = (status) => {
  const colors = {
    active: 'text-green-600 bg-green-100',
    inactive: 'text-gray-600 bg-gray-100',
    pending: 'text-yellow-600 bg-yellow-100',
    completed: 'text-blue-600 bg-blue-100',
    cancelled: 'text-red-600 bg-red-100',
    paid: 'text-green-600 bg-green-100',
    unpaid: 'text-red-600 bg-red-100',
    partial: 'text-yellow-600 bg-yellow-100',
    borrowed: 'text-blue-600 bg-blue-100',
    returned: 'text-green-600 bg-green-100',
    overdue: 'text-red-600 bg-red-100',
    present: 'text-green-600 bg-green-100',
    absent: 'text-red-600 bg-red-100',
    late: 'text-yellow-600 bg-yellow-100',
  };
  return colors[status] || 'text-gray-600 bg-gray-100';
};

// Search and filter utilities
export const searchItems = (items, searchTerm, searchFields) => {
  if (!searchTerm) return items;
  
  const term = searchTerm.toLowerCase();
  return items.filter(item =>
    searchFields.some(field => {
      const value = getNestedValue(item, field);
      return value && value.toString().toLowerCase().includes(term);
    })
  );
};

export const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

// Validation utilities
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone) => {
  const phoneRegex = /^\+?[\d\s-()]+$/;
  return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
};

export const validateRequired = (value) => {
  return value && value.toString().trim().length > 0;
};

// ID generation
export const generateId = (prefix = '') => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `${prefix}${timestamp}${random}`.toUpperCase();
};

// Currency formatting
export const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

// Percentage calculation
export const calculatePercentage = (value, total) => {
  if (!total || total === 0) return 0;
  return Math.round((value / total) * 100);
};

// Attendance calculation
export const calculateAttendancePercentage = (attendanceRecords) => {
  if (!attendanceRecords || attendanceRecords.length === 0) return 0;
  
  const presentCount = attendanceRecords.filter(record => record.status === 'present').length;
  return calculatePercentage(presentCount, attendanceRecords.length);
};

// Sort utilities
export const sortByDate = (items, dateField, ascending = true) => {
  return [...items].sort((a, b) => {
    const dateA = new Date(getNestedValue(a, dateField));
    const dateB = new Date(getNestedValue(b, dateField));
    return ascending ? dateA - dateB : dateB - dateA;
  });
};

export const sortByField = (items, field, ascending = true) => {
  return [...items].sort((a, b) => {
    const valueA = getNestedValue(a, field);
    const valueB = getNestedValue(b, field);
    
    if (typeof valueA === 'string' && typeof valueB === 'string') {
      return ascending 
        ? valueA.localeCompare(valueB)
        : valueB.localeCompare(valueA);
    }
    
    return ascending ? valueA - valueB : valueB - valueA;
  });
};

// File utilities
export const getFileExtension = (filename) => {
  return filename.split('.').pop().toLowerCase();
};

export const isImageFile = (filename) => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  return imageExtensions.includes(getFileExtension(filename));
};

// Local storage utilities
export const saveToLocalStorage = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('Error saving to localStorage:', error);
    return false;
  }
};

export const getFromLocalStorage = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error('Error reading from localStorage:', error);
    return defaultValue;
  }
};

// Debounce utility
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};
