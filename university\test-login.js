// Simple test to verify login data and logic
const { mockData } = require('./src/data/mockData');

console.log('🔍 Testing Login Data and Logic');
console.log('================================');

// Test data availability
console.log('\n📊 Data Availability:');
console.log('Students count:', mockData.students ? mockData.students.length : 'undefined');
console.log('Faculty count:', mockData.faculty ? mockData.faculty.length : 'undefined');

// Test Hammad's data
console.log('\n👤 Looking for Hammad:');
if (mockData.students) {
  const hammadUser = mockData.students.find(s => s.email === '<EMAIL>');
  if (hammadUser) {
    console.log('✅ Hammad found!');
    console.log('📧 Email:', hammadUser.email);
    console.log('🔑 Password:', hammadUser.password);
    console.log('👨‍🎓 Role:', hammadUser.role);
    console.log('🆔 ID:', hammadUser.id);
    console.log('📛 Name:', hammadUser.name);
  } else {
    console.log('❌ Hammad NOT found');
    console.log('Available students:');
    mockData.students.forEach((student, index) => {
      console.log(`  ${index + 1}. ${student.name} (${student.email})`);
    });
  }
} else {
  console.log('❌ Students array is undefined');
}

// Test login logic
console.log('\n🔐 Testing Login Logic:');
const testCredentials = {
  email: '<EMAIL>',
  password: 'hammad123',
  role: 'student'
};

console.log('Test credentials:', testCredentials);

if (mockData.students) {
  const allUsers = [...mockData.students, ...(mockData.faculty || [])];
  console.log('Total users to search:', allUsers.length);
  
  const foundUser = allUsers.find(u =>
    u.email === testCredentials.email &&
    u.password === testCredentials.password &&
    u.role === testCredentials.role
  );
  
  if (foundUser) {
    console.log('✅ Login would succeed!');
    console.log('Found user:', foundUser.name);
  } else {
    console.log('❌ Login would fail');
    console.log('Checking each condition:');
    
    const emailMatch = allUsers.find(u => u.email === testCredentials.email);
    console.log('Email match:', emailMatch ? '✅' : '❌');
    
    if (emailMatch) {
      console.log('Password match:', emailMatch.password === testCredentials.password ? '✅' : '❌');
      console.log('Role match:', emailMatch.role === testCredentials.role ? '✅' : '❌');
      console.log('Expected password:', emailMatch.password);
      console.log('Expected role:', emailMatch.role);
    }
  }
}

console.log('\n🎯 Test Complete!');
